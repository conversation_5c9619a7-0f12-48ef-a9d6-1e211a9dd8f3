﻿using Binarysharp.MemoryManagement;
using Binarysharp.MemoryManagement.Assembly.CallingConvention;
using KeyAuth;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using AIMemoryReader;
using System.Runtime.InteropServices;


namespace Heth_AIO.myClasses
{
    class CheatCMD
    {
        EngineData myEngineData;
        ProcessCMD myProcess;
        Addresses myAddress;
        bool isPlayerChecking = false;
        bool isAnyPlayerChecking = false;
        bool isTrackingTargetInfo = false;
        bool isTargetLocking = false;
        bool isAttackSpeedOn = false;
        bool isChangingQS = false;
        bool isPWW3rdOn = false;
        bool isPWW2ndOn = false;
        bool isControllingState = false;
        float baseWeaponDelay = 1;
        int controlState = 0;
        //public Memory memory;


        public CheatCMD(EngineData myED, ProcessCMD myP, Addresses myA)
        {
            myEngineData = myED;
            myProcess = myP;
            myAddress = myA;

        }

        public void StopThreads()
        {
             isPlayerChecking = false;
            isAnyPlayerChecking = false;
            isTrackingTargetInfo = false;
             isTargetLocking = false;
             isAttackSpeedOn = false;
             isChangingQS = false;
             isPWW3rdOn = false;
            isPWW2ndOn = false;
            isControllingState = false;
            controlState = 0;
        }

        public bool inGameChat(string msg)
        {
            try
            {
                if (myEngineData.newReader)
                {
                    Memory memory = myProcess.LinkMemory();
                    memory.Assembly.Execute<bool>((IntPtr)myAddress.addChatMessageAdd, CallingConvention.Cdecl, "*", msg, 0x34EB40);
                    return true;
                }



                var sharp = myProcess.LinkMemorySharp();
                sharp.Assembly.Execute<bool>((IntPtr)myAddress.addChatMessageAdd, CallingConventions.Cdecl, "*", msg, 0x34EB40);
                return true;

            }
            catch { return false; }


        }
        public bool SaveQuickslot()
        {
            try
            {

                if (myEngineData.newReader)
                {
                    Memory memory = myProcess.LinkMemory();
                    memory.Assembly.Execute<bool>((IntPtr)myAddress.saveQuickslotAdd, CallingConvention.StdCall);
                    return true;
                }

                var sharp = myProcess.LinkMemorySharp();
                sharp.Assembly.Execute<bool>((IntPtr)myAddress.saveQuickslotAdd, CallingConventions.Stdcall);
                return true;
            }
            catch { return false; }
        }
        public void ControlCharacterState(int state)
        {
            isControllingState = true;
            controlState = state;
            new Thread(() =>
            {
                if (myEngineData.newReader)
                {
                    Memory memory = myProcess.LinkMemory();
                    int curChar = memory.Read<int>((IntPtr)myAddress.gameCharacterAdd);
                    var myWorkingState = state;
                    while (isControllingState && myWorkingState == controlState)
                    {
                        if (!myProcess.IsRunning()) return;

                            try
                            {
                                if (controlState == 0)
                                {
                                    isControllingState = false;
                                 memory.Write<int>((IntPtr)curChar + myAddress.playerStateOffset, 0);
                                }
                                else if (controlState == 1) // block teleport
                                {
                                 memory.Write<int>((IntPtr)curChar + myAddress.playerStateOffset, 70000);
                                }
                                else if (controlState == 2) // hide players,npcs,monsters
                                {
                                  memory.Write<int>((IntPtr)curChar + myAddress.playerStateOffset, 10000);
                                }
                                else if (controlState == 3) // block and hide everything
                                {
                                  memory.Write<int>((IntPtr)curChar + myAddress.playerStateOffset, 77000);
                                }
                            }
                            catch { }

                        
                        Thread.Sleep(2000);
                    }
                }
                else
                {

                


                var sharp = myProcess.LinkMemorySharp();
                int curChar = sharp.Read<int>((IntPtr)myAddress.gameCharacterAdd, false);
                var myWorkingState = state;
                while (isControllingState && myWorkingState == controlState)
                {
                    if (!myProcess.IsRunning()) return;

                        
                       
                    if (sharp.IsRunning)
                    {
                        try {
                            if (controlState == 0)
                            {
                                isControllingState = false;
                                sharp.Write<int>((IntPtr)curChar + myAddress.playerStateOffset, 0, false);
                            }
                            else if (controlState == 1) // block teleport
                            {
                                sharp.Write<int>((IntPtr)curChar + myAddress.playerStateOffset, 70000, false);
                            }
                            else if (controlState == 2) // hide players,npcs,monsters
                            {
                                sharp.Write<int>((IntPtr)curChar + myAddress.playerStateOffset, 10000, false);
                            }
                            else if (controlState == 3) // block and hide everything
                            {
                                sharp.Write<int>((IntPtr)curChar + myAddress.playerStateOffset, 77000, false);
                            }
                        }
                        catch { }

                    }
                    Thread.Sleep(2000);
                }
                }

            }).Start();

        }

        public bool OpenHTMLWindow(string htmlName)
        {
            try
            {
                if (myEngineData.newReader)
                {
                    if (htmlName.Length <= 1 || htmlName == "") return false;
                    Memory memory = myProcess.LinkMemory();
                    memory.Assembly.Execute<bool>((IntPtr)myAddress.openHtmlAdd, CallingConvention.Cdecl, htmlName, 0, 0, 0, 0);
                    return true;
                }

                if (htmlName.Length <= 1 || htmlName == "") return false;
                var sharp = myProcess.LinkMemorySharp();
                sharp.Assembly.Execute<bool>((IntPtr)myAddress.openHtmlAdd, CallingConventions.Cdecl, htmlName, 0, 0, 0, 0);
                return true;
            }
            catch { return false; }
        }

        public void UpdateStats(int Str, int Hp, int Int, int Wis, int Agi)
        {
            if (!myProcess.IsRunning()) return;

            if (myEngineData.newReader)
            {
                Memory memory = myProcess.LinkMemory();
                memory.Write<int>((IntPtr)myAddress.strAdd, Str);
                memory.Write<int>((IntPtr)myAddress.hpAdd, Hp);
                memory.Write<int>((IntPtr)myAddress.intAdd, Int);
                memory.Write<int>((IntPtr)myAddress.wisAdd, Wis);
                memory.Write<int>((IntPtr)myAddress.agiAdd, Agi);
                updateYellowStats();
                return;
            }


            var sharp = myProcess.LinkMemorySharp();
            sharp.Write<int>((IntPtr)myAddress.strAdd, Str, false);
            sharp.Write<int>((IntPtr)myAddress.hpAdd, Hp, false);
            sharp.Write<int>((IntPtr)myAddress.intAdd, Int, false);
            sharp.Write<int>((IntPtr)myAddress.wisAdd, Wis, false);
            sharp.Write<int>((IntPtr)myAddress.agiAdd, Agi, false);
            updateYellowStats();
        }

        private void updateYellowStats()
        {
            if (!myProcess.IsRunning()) return;

            if (myEngineData.newReader)
            {
                //Memory memory = myProcess.LinkMemory();
                //int v5 = memory.Assembly.Execute<int>((IntPtr)myAddress.updateStatsAdd, CallingConvention.Cdecl);
               // memory.Assembly.Execute<bool>((IntPtr)myAddress.updateStats2Add, CallingConvention.FastCall, v5);
                return;
            }

            var sharp = myProcess.LinkMemorySharp();
            var v6 = sharp.Assembly.Execute<int>((IntPtr)myAddress.updateStatsAdd, CallingConventions.Cdecl);
            sharp.Assembly.Execute<bool>((IntPtr)myAddress.updateStats2Add, CallingConventions.Thiscall, v6);
            
        }
        public void StopPlayerChecker()
        {
            isPlayerChecking = false;
        }
        public void StopAnyPlayerChecker()
        {
            isAnyPlayerChecking = false;
        }
        public void StartAnyPlayerChecker()
        {
            isAnyPlayerChecking = true;

            if (myEngineData.newReader)
            {
                new Thread(() =>
                {
                    Memory memory = myProcess.LinkMemory();
                    int lastPlayerID = memory.Read<int>((IntPtr)myAddress.playerAroundAdd);
                    while (isAnyPlayerChecking)
                    {

                        if (!myProcess.IsRunning()) return;
                            int anyPlayerID = memory.Read<int>((IntPtr)myAddress.playerAroundAdd);
                            if (anyPlayerID != lastPlayerID)
                            {
                                inGameChat("--> [Heth AIO] Detected a player near, be careful !");
                                Console.Beep(500, 100);
                                Console.Beep(500, 100);
                            }

                            lastPlayerID = anyPlayerID;
                        
                        Thread.Sleep(3000);
                    }
                }).Start();

                return;
            }


            new Thread(() =>
            {

                var sharp = myProcess.LinkMemorySharp();
                int lastPlayerID = sharp.Read<int>((IntPtr)myAddress.playerAroundAdd, false);
                while (isAnyPlayerChecking)
                {

                    if (!myProcess.IsRunning()) return;
                    if (sharp.IsRunning)
                    {
                        int anyPlayerID = sharp.Read<int>((IntPtr)myAddress.playerAroundAdd, false);
                        if (anyPlayerID != lastPlayerID)
                            {
                                    inGameChat("--> [Heth AIO] Detected a player near, be careful !");
                                    Console.Beep(500, 100);
                                    Console.Beep(500, 100);
                            }

                        lastPlayerID = anyPlayerID;
                    }
                    Thread.Sleep(3000);
                }
            }).Start();
        }
        public void StartPlayerChecker(Label lbl, CheckBox chk, string playerNames)
        {
            isPlayerChecking = true;
            if (myEngineData.newReader)
            {
                new Thread(() =>
                {
                    while (isPlayerChecking)
                    {

                        if (!myProcess.IsRunning()) return;

                        var memory = myProcess.LinkMemory();
                            int v6 = 0;
                            var nameArray = playerNames.Trim().Split(',').ToArray();
                            foreach (var name in nameArray)
                            {
                                v6 = memory.Assembly.Execute<int>((IntPtr)myAddress.findNameNearAdd, CallingConvention.Cdecl, name);
                                if (v6 != 0)
                                {
                                    lbl.BeginInvoke(new MethodInvoker(delegate
                                    {
                                        lbl.Text = "Found (" + name + ")";
                                    }));

                                   
                                    
                                        Console.Beep(500, 100);
                                        Console.Beep(500, 100);
                                        Console.Beep(500, 100);
                                        Console.Beep(500, 100);
                                        Console.Beep(100, 500);
                                Thread.Sleep(3000);

                                    break;
                                }
                            }


                        

                        Thread.Sleep(1000);
                    }
                }).Start();

                return;
            }


            new Thread(() =>
            {
                while (isPlayerChecking)
                {

                    if (!myProcess.IsRunning()) return;

                    var sharp = myProcess.LinkMemorySharp();
                    if (sharp.IsRunning)
                    {
                        int v6 = 0;
                        var nameArray = playerNames.Trim().Split(',').ToArray();
                        foreach (var name in nameArray)
                        {
                            v6 = sharp.Assembly.Execute<int>((IntPtr)myAddress.findNameNearAdd, CallingConventions.Cdecl, name);
                            if (v6 != 0)
                            {
                                lbl.BeginInvoke(new MethodInvoker(delegate
                                {
                                    lbl.Text = "Found (" + name + ")";
                                }));

                                while (chk.Checked)
                                {
                                    Console.Beep(500, 100);
                                    Console.Beep(500, 100);
                                    Console.Beep(500, 100);
                                    Console.Beep(500, 100);
                                    Console.Beep(100, 500);
                                }

                                break;
                            }
                        }


                    }

                    Thread.Sleep(1000);
                }
            }).Start();
        }

        public void StopTargetInfoToChat()
        {
            isTrackingTargetInfo = false;
        }
        public void StartTargetInfoToChat()
        {
            isTrackingTargetInfo = true;
            int lastData = 0;
            if (myEngineData.newReader)
            {
                new Thread(() =>
                {
                    while (isTrackingTargetInfo)
                    {
                        if (myProcess.selectedProcess == 0) return;
                        Memory memory = myProcess.LinkMemory();
                        int curtarget = memory.Read<int>((IntPtr)myAddress.curTargetAddress);
                        if (curtarget != 0)
                        {
                            int curtargetMinHP = memory.Read<int>((IntPtr)curtarget + myAddress.MonsterMinHPOffset);
                            int curtargetID = memory.Read<int>((IntPtr)curtarget + myAddress.MonsterIDOffset);
                            if (lastData != curtargetMinHP + curtargetID)
                            {
                                int curtargetMaxHP = memory.Read<int>((IntPtr)curtarget + myAddress.MonsterMaxHPOffset);

                                string Health = "Target Health: " + curtargetMinHP.ToString() + "/" + curtargetMaxHP.ToString() + " | ID: " + curtargetID.ToString();
                                memory.Assembly.Execute<bool>((IntPtr)myAddress.addInfoMessageAdd, CallingConvention.Cdecl, Health, 0x0000ff, 1);
                                lastData = curtargetMinHP + curtargetID;

                            }

                        }
                        Thread.Sleep(1000);
                    }
                }).Start();


                return;
            }


            new Thread(() =>
            {
                while (isTrackingTargetInfo)
                {
                    if (myProcess.selectedProcess == 0) return;

                    var sharp = myProcess.LinkMemorySharp();
                    int curtarget = sharp.Read<int>((IntPtr)myAddress.curTargetAddress, false);
                    if (curtarget != 0)
                    {
                        int curtargetMinHP = sharp.Read<int>((IntPtr)curtarget + myAddress.MonsterMinHPOffset, false);
                        int curtargetID = sharp.Read<int>((IntPtr)curtarget + myAddress.MonsterIDOffset, false);
                        if (lastData != curtargetMinHP + curtargetID)
                        {
                            int curtargetMaxHP = sharp.Read<int>((IntPtr)curtarget + myAddress.MonsterMaxHPOffset, false);

                            string Health = "Target Health: " + curtargetMinHP.ToString() + "/" + curtargetMaxHP.ToString() + " | ID: " + curtargetID.ToString();
                            sharp.Assembly.Execute<bool>((IntPtr)myAddress.addInfoMessageAdd, CallingConventions.Cdecl, Health, 0x0000ff, 1);
                            lastData = curtargetMinHP + curtargetID;

                        }

                    }
                    Thread.Sleep(1000);
                }
            }).Start();
        }


        public void StopLockTarget()
        {
            isTargetLocking = false;
        }
        public void StartLockTarget()
        {
            isTargetLocking = true;
            int a1 = 0;

            if(myEngineData.newReader)
            {
                Memory memory = myProcess.LinkMemory();
                a1 = memory.Read<int>((IntPtr)myAddress.curTargetAddress);
                new Thread(() =>
                {
                    while (isTargetLocking)
                    {

                        if (!myProcess.IsRunning()) return;


                        if (a1 != 0)
                        {
                            memory.Write<int>((IntPtr)myAddress.curTargetAddress, a1);
                        }
                        else
                        {
                            memory.Write<int>((IntPtr)myAddress.curTargetAddress, 0);
                        }
                        Thread.Sleep(555);
                    }
                }).Start();
                return;
            }

            var sharp = myProcess.LinkMemorySharp();
            a1 = sharp.Read<int>((IntPtr)myAddress.curTargetAddress, false);
            new Thread(() =>
            {
                while (isTargetLocking)
                {

                    if (!myProcess.IsRunning()) return;


                    if (a1 != 0)
                    {
                        sharp.Write<int>((IntPtr)myAddress.curTargetAddress, a1, false);
                    }
                    else
                    {
                        sharp.Write<int>((IntPtr)myAddress.curTargetAddress, 0, false);
                    }
                    Thread.Sleep(555);
                }
            }).Start();
        }

        public void StopAtkSpeed()
        {
            isAttackSpeedOn = false;
            if (myAddress.engineType == 1) return;
            if (!myProcess.IsRunning()) return;
            int curChar = 0;
            if (myEngineData.newReader)
            {
                Memory memory = myProcess.LinkMemory();
                curChar = memory.Read<int>((IntPtr)myAddress.gameCharacterAdd);
                if (curChar != 0)
                {
                    memory.Write<float>((IntPtr)curChar + myAddress.m_fWeaponDelayOffset, baseWeaponDelay);
                }

                return;
            }

            var sharp = myProcess.LinkMemorySharp();
            curChar = sharp.Read<int>((IntPtr)myAddress.gameCharacterAdd, false);
            if (curChar != 0)
            {
                sharp.Write<float>((IntPtr)curChar + myAddress.m_fWeaponDelayOffset, baseWeaponDelay, false);
            }
        }
        public void StartAtkSpeed()
        {
            isAttackSpeedOn = true;
            if (myAddress.engineType == 1) return;

            if (myEngineData.newReader)
            {
                new Thread(() =>
                {
                    Memory memory = myProcess.LinkMemory();
                    int curChar = memory.Read<int>((IntPtr)myAddress.gameCharacterAdd);
                    if (curChar != 0)
                    {
                        baseWeaponDelay = memory.Read<float>((IntPtr)curChar + myAddress.m_fWeaponDelayOffset);
                    }

                    while (isAttackSpeedOn)
                    {
                        if (!myProcess.IsRunning()) return;
                        if (curChar != 0)
                        {
                            memory.Write<int>((IntPtr)myAddress.AtkSpeedAdd, 0);
                            memory.Write<int>((IntPtr)myAddress.NormalAtkFixerAdd, 0);
                            memory.Write<float>((IntPtr)curChar + myAddress.m_fWeaponDelayOffset, 0);
                        }
                        Thread.Sleep(350);
                    }
                }).Start();

                return;
            }


            new Thread(() =>
            {
                var sharp = myProcess.LinkMemorySharp();
                int curChar = sharp.Read<int>((IntPtr)myAddress.gameCharacterAdd, false);
                if (curChar != 0)
                {
                    baseWeaponDelay = sharp.Read<float>((IntPtr)curChar + myAddress.m_fWeaponDelayOffset, false);
                }

                while (isAttackSpeedOn)
                {
                    if (!myProcess.IsRunning()) return;
                    if (curChar != 0)
                    {
                        sharp.Write<int>((IntPtr)myAddress.AtkSpeedAdd, 0, false);
                        sharp.Write<int>((IntPtr)myAddress.NormalAtkFixerAdd, 0, false);
                        sharp.Write<float>((IntPtr)curChar + myAddress.m_fWeaponDelayOffset, 0, false);
                    }
                        Thread.Sleep(350);
                }
            }).Start();

        }

        public void InfiniyRush(int mode)
        {
            //not used
            if (!myProcess.IsRunning()) return;

            //var sharp = myProcess.LinkMemorySharp();
            //if (mode == 1)
            //{
            //    sharp.Write<int>((IntPtr)myAddress.rushTimeAdd, 99999);
            //}
            //else
            //{
            //    sharp.Write<int>((IntPtr)myAddress.rushTimeAdd, 1500);
            //}

        }
        public void ChangeQuickSlot(int page)
        {
            if (!myProcess.IsRunning()) return;

            isChangingQS = true;
            if (myEngineData.newReader)
            {
                new Thread(() =>
                {
                    while (isChangingQS)
                    {
                        if (!myProcess.IsRunning()) return;
                        Memory memory = myProcess.LinkMemory();
                        string myCurPage = memory.ReadString((IntPtr)myAddress.curQuickslot, 1 ,Encoding.ASCII);
                        if (myCurPage != page.ToString())
                        {
                            memory.Assembly.Execute<bool>((IntPtr)myAddress.changeQuickslotAdd, CallingConvention.Cdecl, page - 1, 1);
                        }
                        Thread.Sleep(500);
                    }
                }).Start();

                return;
            }

            new Thread(() =>
            {
                while (isChangingQS)
                {
                    if (!myProcess.IsRunning()) return;
                    var sharp = myProcess.LinkMemorySharp();
                    string myCurPage = sharp.ReadString((IntPtr)myAddress.curQuickslot, Encoding.ASCII, false, 1);
                    if (myCurPage != page.ToString())
                    {
                        sharp.Assembly.Execute<bool>((IntPtr)myAddress.changeQuickslotAdd, CallingConventions.Cdecl, page-1, 1);
                    }
                    Thread.Sleep(500);
                }
            }).Start();
        }

        public void ChangeTabTargetRange(int num)
        {
            if (!myProcess.IsRunning()) return;

            if (myEngineData.newReader)
            {
                Memory memory = myProcess.LinkMemory();
                switch (num)
                {
                    case 0:
                        memory.Write<float>((IntPtr)myAddress.tabRange, 123904);
                        break;
                    case 1:
                        memory.Write<float>((IntPtr)myAddress.tabRange, 53904);
                        break;
                    case 2:
                        memory.Write<float>((IntPtr)myAddress.tabRange, 323904);
                        break;
                    case 3:
                        memory.Write<float>((IntPtr)myAddress.tabRange, 623904);
                        break;
                    case 4:
                        memory.Write<float>((IntPtr)myAddress.tabRange, 923904);
                        break;
                    case 5:
                        memory.Write<float>((IntPtr)myAddress.tabRange, 1023904);
                        break;
                    default:
                        break;
                }

                return;
            }


            var sharp = myProcess.LinkMemorySharp();
            switch(num)
            {
                case 0:
                    sharp.Write<float>((IntPtr)myAddress.tabRange, 123904, false);
                    break;
                case 1:
                    sharp.Write<float>((IntPtr)myAddress.tabRange, 53904, false);
                    break;
                case 2:
                    sharp.Write<float>((IntPtr)myAddress.tabRange, 323904, false);
                    break;
                case 3:
                    sharp.Write<float>((IntPtr)myAddress.tabRange, 623904, false);
                    break;
                case 4:
                    sharp.Write<float>((IntPtr)myAddress.tabRange, 923904, false);
                    break;
                case 5:
                    sharp.Write<float>((IntPtr)myAddress.tabRange, 1023904, false);
                    break;
                default:
                    break;
            }
        }

        public void StopChangeQuickSlot()
        {
            isChangingQS = false;
       }

        public void StopPWW3rd()
        {
            isPWW3rdOn = false;
        }
        public void StartPWW3rd()
        {
            isPWW3rdOn = true;
            if (myEngineData.newReader)
            {
                new Thread(() =>
                {
                    Memory memory = myProcess.LinkMemory();
                    while (isPWW3rdOn)
                    {
                        if (!myProcess.IsRunning()) return;
                        memory.Write<int>((IntPtr)myAddress.comboSkillAdd, 700);
                        Thread.Sleep(300);
                    }
                }).Start();

                return;
            }

            new Thread(() =>
            {
                while (isPWW3rdOn)
                {
                    if (!myProcess.IsRunning()) return;
                    var sharp = myProcess.LinkMemorySharp();
                    sharp.Write<int>((IntPtr)myAddress.comboSkillAdd, 700, false);
                    Thread.Sleep(300);
                }
            }).Start();

        }

        public void StopPWW2nd()
        {
            isPWW2ndOn = false;
        }
        public void StartPWW2nd()
        {
            isPWW2ndOn = true;

            if (myEngineData.newReader)
            {
                new Thread(() =>
                {
                    Memory memory = myProcess.LinkMemory();
                    while (isPWW2ndOn)
                    {
                        if (!myProcess.IsRunning()) return;
                        memory.Write<int>((IntPtr)myAddress.comboSkillAdd, 256);
                        Thread.Sleep(300);
                    }
                }).Start();
                return;
            }


            new Thread(() =>
            {
                while (isPWW2ndOn)
                {
                    if (!myProcess.IsRunning()) return;

                    var sharp = myProcess.LinkMemorySharp();
                    sharp.Write<int>((IntPtr)myAddress.comboSkillAdd, 256, false);
                    Thread.Sleep(300);
                }
            }).Start();

        }

    }
}
