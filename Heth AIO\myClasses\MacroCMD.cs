﻿using AIMemoryReader;
using Binarysharp.MemoryManagement;
using Binarysharp.MemoryManagement.Assembly.CallingConvention;
using Binarysharp.MemoryManagement.Native;
using Heth_AIO.myClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using static Heth_AIO.Imports;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace Heth_AIO
{
    public class MacroCMD
    {
        EngineData myEngineData;
        ProcessCMD myProcess;
        Addresses myAddress;
        

        public MacroCMD(EngineData myED, ProcessCMD myP, Addresses myA)
        {
            myEngineData = myED;
            myProcess = myP;
            myAddress = myA;
        }



        public string ReplaceShortcuts(string Str)
        {
            string myMacros = Str;
            myMacros = myMacros.Replace("ntid", "next target if dead")
               .Replace("ntiw", "next target if wall")
               .Replace("nti \"", "next target if \"")
               .Replace("ntinm", "next target if not monster")
               .Replace("pigh", "pick if good hp")
               .Replace("n atk", "normal atk")
               .Replace("natk", "normal atk");

            for (int i = 0; i <= 83; i++)
            {
                string s = "p " + i + " id";
                string r = "press " + i + " if dead";
                myMacros = myMacros.Replace(s, r);
            }

            for (int i = 0; i <= 83; i++)
            {
                string s = "p " + i + " ilhs ";
                string r = "press " + i + " if low hp stop ";
                myMacros = myMacros.Replace(s, r);
            }

            for (int i = 0; i <= 83; i++)
            {
                string s = "p " + i + " ilms ";
                string r = "press " + i + " if low mana stop ";
                myMacros = myMacros.Replace(s, r);
            }

            return myMacros;
        }

        public bool SmartKeyPress(int key)
        {
            if (myProcess.selectedProcess == 0) return false;
            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                memory.Assembly.Execute<bool>((IntPtr)myAddress.changeQuickslotAdd, CallingConvention.Cdecl, key, 0);
                return true;
            }
            var sharp = myProcess.LinkMemorySharp();
            sharp.Assembly.Execute<bool>((IntPtr)myAddress.changeQuickslotAdd, CallingConventions.Cdecl, key, 0);
            return true;
        }

        public int GetTargetID()
        {
            if (myProcess.selectedProcess == 0) return 0;
            int curtarget = 0;

            if (myEngineData.newReader) {
                var memory = myProcess.LinkMemory();
                 curtarget = memory.Read<int>((IntPtr)myAddress.curTargetAddress);
                 return curtarget;
            }

            var sharp = myProcess.LinkMemorySharp();
             curtarget = sharp.Read<int>((IntPtr)myAddress.curTargetAddress, false);
            return curtarget;
        }
        public UInt32 GetTargetMinHP()
        {
            if (myProcess.selectedProcess == 0) return 1;

            int curtarget = GetTargetID();
            if (curtarget != 0)
            {
                if (myEngineData.newReader)
                {
                    var memory = myProcess.LinkMemory();
                    UInt32 curtargetMinHPx = memory.Read<UInt32>((IntPtr)curtarget + myAddress.MonsterMinHPOffset);
                    return curtargetMinHPx;
                }

                var sharp = myProcess.LinkMemorySharp();
                UInt32 curtargetMinHP = sharp.Read<UInt32>((IntPtr)curtarget + myAddress.MonsterMinHPOffset, false);
                return curtargetMinHP;
            }
            return 1;
        }

        public bool isAttacking()
        {
            if (myProcess.selectedProcess == 0) return false;

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                float isAtkingx = memory.Read<float>((IntPtr)myAddress.AtkSpeedAdd);
                return isAtkingx != 0;
            }

            var sharp = myProcess.LinkMemorySharp();
            float isAtking = sharp.Read<float>((IntPtr)myAddress.AtkSpeedAdd, false);
            return isAtking != 0;
        }

        public bool NextTarget()
        {
            if (myProcess.selectedProcess == 0) return false;

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                memory.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConvention.Cdecl);
                return true;
            }

            var sharp = myProcess.LinkMemorySharp();
            sharp.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConventions.Cdecl);
            return true;
        }

        //this is not used
        public bool Dash()
        {
            if (myProcess.selectedProcess == 0) return false;

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                memory.Assembly.Execute<bool>((IntPtr)0x00414C60, CallingConvention.Cdecl);
                return true;
            }

            var sharp = myProcess.LinkMemorySharp();
            sharp.Assembly.Execute<bool>((IntPtr)0x00414C60, CallingConventions.Cdecl, 1);
            return true;
        }


        public bool NextTargetIfDead()
        {
            if (myProcess.selectedProcess == 0) return false;

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                int curtargetx = memory.Read<int>((IntPtr)myAddress.curTargetAddress);
                if (curtargetx == 0)
                {
                    memory.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConvention.Cdecl);
                    return true;
                }
                return false;
            }

            var sharp = myProcess.LinkMemorySharp();
            int curtarget = sharp.Read<int>((IntPtr)myAddress.curTargetAddress, false);
            if (curtarget == 0)
            {
                sharp.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConventions.Cdecl);
                return true;
            }
            return false;
        }

        public bool NextTargetIfWall()
        {
            if (myProcess.selectedProcess == 0) return false;

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                int curtargetx = memory.Read<int>((IntPtr)myAddress.curTargetAddress);
                if (curtargetx != 0)
                {
                    bool bWall = memory.Assembly.Execute<bool>((IntPtr)myAddress.checkObjectCrashAdd, CallingConvention.Cdecl, curtargetx);
                    if (!bWall)
                    {
                        memory.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConvention.Cdecl);
                        return true;
                    }
                }
                return false;
            }

            var sharp = myProcess.LinkMemorySharp();
            int curtarget = sharp.Read<int>((IntPtr)myAddress.curTargetAddress, false);
            if (curtarget != 0)
            {
                bool bWall = sharp.Assembly.Execute<bool>((IntPtr)myAddress.checkObjectCrashAdd, CallingConventions.Cdecl, curtarget);
                if (!bWall)
                {
                    sharp.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConventions.Cdecl);
                    return true;
                }
            }
            return false;
        }

        public bool NextTargetIfNotMonster()
        {
            if (myProcess.selectedProcess == 0) return false;

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                int curtargetx = memory.Read<int>((IntPtr)myAddress.curTargetAddress);
                if (curtargetx != 0)
                {
                    int curtargetkindx = memory.Read<int>((IntPtr)myAddress.targetKindAdd);
                    if (curtargetkindx == 0 || curtargetkindx == 2)
                    {
                        memory.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConvention.Cdecl);
                        return true;
                    }
                }
                return false;
            }

            var sharp = myProcess.LinkMemorySharp();
            int curtarget = sharp.Read<int>((IntPtr)myAddress.curTargetAddress, false);
            if (curtarget != 0)
            {
                int curtargetkind = sharp.Read<int>((IntPtr)myAddress.targetKindAdd, false);
                if (curtargetkind == 0 || curtargetkind == 2)
                {
                    sharp.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConventions.Cdecl);
                    return true;
                }
            }
            return false;
        }



        public bool NextTargetIfName(string line, string targetName)
        {
            if (myProcess.selectedProcess == 0) return false;

            Match name = Regex.Match(line, "\".*?\"");
            if (name.Success)
            {

                string monsterName = name.Value.ToString().Replace("\"", "").ToLower();
                string myCurTargetName = targetName.ToLower();

                if (myCurTargetName.Contains(monsterName))
                {
                    if (myEngineData.newReader)
                    {
                        var memory = myProcess.LinkMemory();
                        memory.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConvention.Cdecl);
                        return true;
                    }

                    var sharp = myProcess.LinkMemorySharp();
                    sharp.Assembly.Execute<bool>((IntPtr)myAddress.tapTargetAdd, CallingConventions.Cdecl);
                    return true;
                }
            }
            return false;
        }


        public bool SelectPlayer(string line, int mode)
        {
            // 0 normal atk 1 right click
            if (myProcess.selectedProcess == 0) return false;

            var match = Regex.Match(line, @"""([^""]+)""");
            if (!match.Success) return false;
            var Name = match.Groups[1].Value;
                if (myEngineData.newReader)
                {
                    var memory = myProcess.LinkMemory();
                    memory.Assembly.Execute<bool>((IntPtr)myAddress.selectPlayerAdd, CallingConvention.Cdecl, Name, mode);
                    return true;
                }

                var sharp = myProcess.LinkMemorySharp();
                sharp.Assembly.Execute<bool>((IntPtr)myAddress.selectPlayerAdd, CallingConventions.Cdecl, Name, mode);
                return true;
            
            //return false;
        }


        public bool RotateCamera()
        {
            if (myProcess.selectedProcess == 0) return false;
            Random rnd = new Random();

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                float anglex = rnd.Next(6);
                memory.Write<float>((IntPtr)myAddress.rotateCamAngleAdd, anglex);
                return true;
            }


            var sharp = myProcess.LinkMemorySharp();
            float angle = rnd.Next(6);
            sharp.Write<float>((IntPtr)myAddress.rotateCamAngleAdd, angle, false);
            return true;
        }
        public bool AutoAtkBotByName(string line)
        {
            if (myProcess.selectedProcess == 0) return false;
            var match = Regex.Match(line, @"""([^""]+)""");
            if (!match.Success) return false;
            var Name = match.Groups[1].Value;
            int curTarget = 0;
            int targetFoundID = 0;
            int curtargetMinHP = 0;
                if (myEngineData.newReader)
                    {
                        var memory = myProcess.LinkMemory();
                         curTarget = memory.Read<int>((IntPtr)myAddress.curTargetAddress);
                         targetFoundID = memory.Assembly.Execute<int>((IntPtr)myAddress.findNameNearAdd, CallingConvention.Cdecl, Name);

                        
                if(curTarget != 0) { 
                         curtargetMinHP = memory.Read<int>((IntPtr)curTarget + myAddress.MonsterMinHPOffset);
                }
                if (targetFoundID != 0)
                 {
                            memory.Write<int>((IntPtr)myAddress.curTargetAddress, targetFoundID);
                            memory.Assembly.Execute<IntPtr>((IntPtr)myAddress.sendAttackAdd, CallingConvention.Cdecl);
                            if(curTarget != 0) { 
                            int TargetIDx = memory.Read<int>((IntPtr)curTarget + myAddress.targetIDOffset);
                            memory.Assembly.Execute<IntPtr>((IntPtr)myAddress.selUIDAdd, CallingConvention.Cdecl, TargetIDx,1 ,0 );
                    }
                }
                return true;
            }


            var sharp = myProcess.LinkMemorySharp();
            curTarget = sharp.Read<int>((IntPtr)myAddress.curTargetAddress, false);
            targetFoundID = sharp.Assembly.Execute<int>((IntPtr)myAddress.findNameNearAdd, CallingConventions.Cdecl, Name);


            if (curTarget != 0)
            {
                curtargetMinHP = sharp.Read<int>((IntPtr)curTarget + myAddress.MonsterMinHPOffset, false);
            }
            if (targetFoundID != 0)
            {
                sharp.Write<int>((IntPtr)myAddress.curTargetAddress, targetFoundID, false);
                sharp.Assembly.Execute<bool>((IntPtr)myAddress.sendAttackAdd, CallingConventions.Cdecl);
                if (curTarget != 0)
                {
                    int TargetIDx = sharp.Read<int>((IntPtr)curTarget + myAddress.targetIDOffset, false);
                    sharp.Assembly.Execute<bool>((IntPtr)myAddress.selUIDAdd, CallingConventions.Cdecl, TargetIDx, 1, 0);
                }
            }
            return true;
        }

        public bool NormalAttack(int mode)
        {
            if (myProcess.selectedProcess == 0) return false;

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                int curtargetx = memory.Read<int>((IntPtr)myAddress.curTargetAddress);
                if (curtargetx != 0)
                {
                    memory.Write<int>((IntPtr)myAddress.InitReactBattlebLockAdd, 0);
                    memory.Write<int>((IntPtr)myAddress.InitReactBattlebyStartAdd, mode);
                    memory.Assembly.Execute<bool>((IntPtr)myAddress.SetTargetByIdAdd, CallingConvention.Cdecl,curtargetx);
                    memory.Assembly.Execute<bool>((IntPtr)myAddress.sendAttackAdd, CallingConvention.Cdecl);

                    int TargetIDx = memory.Read<int>((IntPtr)curtargetx + myAddress.targetIDOffset);
                    memory.Assembly.Execute<bool>((IntPtr)myAddress.selUIDAdd, CallingConvention.Cdecl, TargetIDx,1 , 0);  
                    return true;
                }
                return true;
            }

            var sharp = myProcess.LinkMemorySharp();
            int curtarget = sharp.Read<int>((IntPtr)myAddress.curTargetAddress, false);
            if (curtarget != 0)
            {
                sharp.Write<int>((IntPtr)myAddress.InitReactBattlebLockAdd, 0, false);
                sharp.Write<int>((IntPtr)myAddress.InitReactBattlebyStartAdd, mode, false);
                sharp.Assembly.Execute<bool>((IntPtr)myAddress.SetTargetByIdAdd, CallingConventions.Cdecl, curtarget);
                sharp.Assembly.Execute<bool>((IntPtr)myAddress.sendAttackAdd, CallingConventions.Cdecl);

                int TargetID = sharp.Read<int>((IntPtr)curtarget + myAddress.targetIDOffset,false);
                 sharp.Assembly.Execute<bool>((IntPtr)myAddress.selUIDAdd, CallingConventions.Cdecl, TargetID, 1, 0);
                return true;
            }

            return true;
        }
        public bool StopNormalAttack()
        {
            if (myProcess.selectedProcess == 0) return false;

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                memory.Write<int>((IntPtr)myAddress.InitReactBattlebLockAdd, 0);
                memory.Write<int>((IntPtr)myAddress.InitReactBattlebyStartAdd, 0);
                return true;
            }

            var sharp = myProcess.LinkMemorySharp();
                sharp.Write<int>((IntPtr)myAddress.InitReactBattlebLockAdd, 0, false);
                sharp.Write<int>((IntPtr)myAddress.InitReactBattlebyStartAdd, 0, false);
            return true;
        }

        public bool UseSkill(string line)
        {
            if (myProcess.selectedProcess == 0) return false;

            int parsedintValue;
            Match aa = Regex.Match(line, @"\d+");
            if (int.TryParse(aa.Value, out parsedintValue))
            {
                int aavalue = int.Parse(aa.Value);

                if (myEngineData.newReader)
                {
                    var memory = myProcess.LinkMemory();
                    memory.Assembly.Execute<bool>((IntPtr)myAddress.useSkillAdd, CallingConvention.Cdecl, aavalue, 0);
                    return true;
                }

                var sharp = myProcess.LinkMemorySharp();
                sharp.Assembly.Execute<bool>((IntPtr)myAddress.useSkillAdd, CallingConventions.Cdecl, aavalue, 0);
                return true;

            }
            return false;
        }

        public bool DropPick(string line)
        {
            if (myProcess.selectedProcess == 0) return false;

            if (myEngineData.newReader)
            {
                var memory = myProcess.LinkMemory();
                memory.Write<int>((IntPtr)myAddress.isDropNearAdd, 1);
                int curtargetx = memory.Read<int>((IntPtr)myAddress.isDropNearAdd);
                if (curtargetx == 1)
                {
                    if (line.Contains("med"))
                    {
                        memory.Assembly.Execute<bool>((IntPtr)myAddress.send_itemAdd, CallingConvention.Cdecl, 400);
                    }
                    else if (line.Contains("far"))
                    {
                        memory.Assembly.Execute<bool>((IntPtr)myAddress.send_itemAdd, CallingConvention.Cdecl, 800);
                    }
                    else if (line.Contains("very far"))
                    {
                        memory.Assembly.Execute<bool>((IntPtr)myAddress.send_itemAdd, CallingConvention.Cdecl, 1600);
                    }
                    else
                    {
                        memory.Assembly.Execute<bool>((IntPtr)myAddress.send_itemAdd, CallingConvention.Cdecl, 140);
                    }
                    return true;
                }
                return false;
            }

            var sharp = myProcess.LinkMemorySharp();
            sharp.Write<int>((IntPtr)myAddress.isDropNearAdd, 1, false);
            int curtarget = sharp.Read<int>((IntPtr)myAddress.isDropNearAdd, false);
            if (curtarget == 1)
            {
                if (line.Contains("med"))
                {
                    sharp.Assembly.Execute<bool>((IntPtr)myAddress.send_itemAdd, CallingConventions.Cdecl, 400);

                }
                else if (line.Contains("far"))
                {
                    sharp.Assembly.Execute<bool>((IntPtr)myAddress.send_itemAdd, CallingConventions.Cdecl, 800);

                }
                else if (line.Contains("very far"))
                {
                    sharp.Assembly.Execute<bool>((IntPtr)myAddress.send_itemAdd, CallingConventions.Cdecl, 1600);

                }
                else
                {
                    sharp.Assembly.Execute<bool>((IntPtr)myAddress.send_itemAdd, CallingConventions.Cdecl, 140);

                }

                return true;
            }
            return false;
        }




    }
}
