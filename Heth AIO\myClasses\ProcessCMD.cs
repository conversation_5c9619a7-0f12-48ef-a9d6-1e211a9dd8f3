﻿using AIMemoryReader;
using Binarysharp.MemoryManagement;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Heth_AIO.myClasses
{
    public class ProcessCMD
    {
        //Imports myImport = new Imports();
        
        public int selectedProcess = 0;
        public IntPtr myCurInjectedHandle = IntPtr.Zero;


        public ProcessCMD(int sp){
            selectedProcess = sp;
            }
        public ProcessCMD()
        {
           
        }
        public bool IsRunning()
        {
            try
            {
                Process.GetProcessById(selectedProcess);
            }
            catch (InvalidOperationException)
            {
                return false;
            }
            catch (ArgumentException)
            {
                return false;
            }
            return true;
        }
        public bool IsUserAdministrator()
        {
            bool isAdmin;
            try
            {
                WindowsIdentity user = WindowsIdentity.GetCurrent();
                WindowsPrincipal principal = new WindowsPrincipal(user);
                isAdmin = principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                isAdmin = false;
            }
            return isAdmin;
        }

        public Process GetEngineProcess()
        {
            Process engine = Process.GetProcessById(selectedProcess);
            return engine;
        }
        public bool injectEngine(string WinName, bool safeMode)
        {
            IntPtr handle = Imports.FindWindow("D3D Window", null);
            if (!Imports.IsWindow(handle))
            {
                return false;
            }
            myCurInjectedHandle = handle;
            

            if (safeMode)
                return true;

            Imports.GetWindowThreadProcessId(handle, out selectedProcess);
            Imports.SetWindowText(handle,WinName);
            return true;
        }

        public bool resetEngine(bool safeMode)
        {
            myCurInjectedHandle = IntPtr.Zero;
            selectedProcess = 0;

            if (safeMode) return true;

            IntPtr handle = myCurInjectedHandle;
            if (handle.Equals(IntPtr.Zero)) return false;
            Imports.SetWindowText(handle, "KalOnline");


            return true;
        }

        public void killEngine()
        {
            if (selectedProcess == 0) return;
                try
                {
                    Process engine = GetEngineProcess();
                    engine.Kill();
                }
                catch{ }

        }
        public MemorySharp LinkMemorySharp()
        {
            MemorySharp sharp = new MemorySharp(GetEngineProcess());
            return sharp;
        }

        public Memory LinkMemory()
        {
            Memory memory = new Memory(selectedProcess);
            return memory;
        }

    }
}
