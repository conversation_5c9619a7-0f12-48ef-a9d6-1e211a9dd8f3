﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1E0149EA-37A9-49DD-B538-D9EBD145CFD8}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Heth_AIO</RootNamespace>
    <AssemblyName>Heth AIO v1.2</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>aio icon.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="abomeme, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\abomeme\abomeme.dll</HintPath>
    </Reference>
    <Reference Include="Fasm.NET, Version=1.0.4939.27955, Culture=neutral, processorArchitecture=x86">
      <HintPath>..\packages\Fasm.NET.1.70.03\lib\Fasm.NET.dll</HintPath>
    </Reference>
    <Reference Include="PortableSettingsProvider, Version=0.2.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\PortableSettingsProvider.0.2.4\lib\net45\PortableSettingsProvider.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Ed25519.cs" />
    <Compile Include="ExtraMacroForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExtraMacroForm.Designer.cs">
      <DependentUpon>ExtraMacroForm.cs</DependentUpon>
    </Compile>
    <Compile Include="KeyAuth.cs" />
    <Compile Include="Login.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Login.Designer.cs">
      <DependentUpon>Login.cs</DependentUpon>
    </Compile>
    <Compile Include="MacroCommandsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MacroCommandsForm.Designer.cs">
      <DependentUpon>MacroCommandsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="myClasses\Addresses.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="myClasses\AutoCMD.cs" />
    <Compile Include="myClasses\CheatCMD.cs" />
    <Compile Include="myClasses\CheckerCMD.cs" />
    <Compile Include="myClasses\EngineData.cs" />
    <Compile Include="myClasses\Imports.cs" />
    <Compile Include="myClasses\MacroCMD.cs" />
    <Compile Include="Memory\Memory.cs" />
    <Compile Include="myClasses\ProcessCMD.cs" />
    <Compile Include="Overlay.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Overlay.Designer.cs">
      <DependentUpon>Overlay.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Settings.cs" />
    <EmbeddedResource Include="ExtraMacroForm.resx">
      <DependentUpon>ExtraMacroForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Login.resx">
      <DependentUpon>Login.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MacroCommandsForm.resx">
      <DependentUpon>MacroCommandsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Overlay.resx">
      <DependentUpon>Overlay.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="..\.editorconfig">
      <Link>.editorconfig</Link>
    </None>
    <None Include="app.manifest" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>PublicSettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ban.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\doctor.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\list-check.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\play-alt.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pulse.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\shield-interrogation.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\syringe2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\rotate-right2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\disk.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\syringeBig.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="aio icon.ico" />
    <Content Include="Fasm.NET.xml" />
    <None Include="Resources\mapico.png" />
    <None Include="Resources\search.png" />
    <None Include="Resources\alarm-clock.png" />
    <None Include="Resources\address-book.png" />
    <None Include="Resources\Logo.png" />
    <None Include="Resources\target.png" />
    <None Include="Resources\pulseRed.png" />
    <None Include="Resources\test-tube.png" />
    <None Include="Resources\heart.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.1">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.1 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>