SelectionName="SoonEngineV2 (Online)";
engineType=4;
gameCharacterAdd=0x008E05E4;
GCMapxOffset=0x46E0;
GCMapyOffset=0x46E8;
playerStateOffset=0x479C;
nameAdd=0x009C9754;
guildAdd=0x009C978C;
moneyAdd=0x009CCD28;
targetAdd=0x009CADE8;
HealthX=0x009C9924;
HealthY=0x008E0678;
HealthAddresse=0x009C966C;
MaxManaAddresse=0x009C9670;
CurrManaAddresse=0x009C9668;
tapTargetAdd=0x00433480;
curTargetAddress=0x008E0610;
useSkillAdd=0x0042E2D0;
isMonsterAlive=0x008E05EC;
tabRange=0x008E066C;
isDropNearAdd=0x008AF204;
send_itemAdd=0x00432180;
targetKindAdd=0x008CE200;
checkObjectCrashAdd=0x006C4BB0;
zoomAdd=0x008EE970;
SetTargetByIdAdd=0x006F94C0;
InitReactBattlebyStartAdd=0x009CB252;
InitReactBattlebLockAdd=0x009CB278;
MonsterMinHPOffset=0x475C;
MonsterMaxHPOffset=0x4760;
MonsterIDOffset=0x4DBC;
NormalAtkFixerAdd=0x008CE200;
AtkSpeedAdd=0x008E05F0;
rushTimeAdd=0x004375EA;
cameraRotateAdd=0x008EE978;
curQuickslot=0x009CDDB0;
changeQuickslotAdd=0x006CBC90;
saveQuickslotAdd=0x006CA4F0;
openHtmlAdd=0x006C0310;
findNameNearAdd=0x00428690;
rotateCamAngleAdd=0x008EE978;
selectPlayerAdd=0x006C3D20;
chattingClearAdd=0x006C3990;
addChatMessageAdd=0x006CC890;
addInfoMessageAdd=0x006CC8C0;
comboSkillAdd=0x008E05EE;
playerAroundAdd=0x009C8218;
strAdd=0x009B5B98;
hpAdd=0x009B5B9A;
intAdd=0x009B5B9C;
wisAdd=0x009B5B9E;
agiAdd=0x009B5BA0;
curstrAdd=0x009C94A8;
curhpAdd=0x009C94AC;
curintAdd=0x009C94B0;
curwisAdd=0x009C94B4;
curagiAdd=0x009C94B8;
curPointsAdd=0x009C94FC;
updateStatsAdd=0x00626100;
updateStats2Add=0x00626F80;
m_fWeaponDelayOffset = 0x4C5C;
targetIDOffset = 0x4444;
selUIDAdd = 0x006C95D0;
sendAttackAdd = 0x006C4D00;