﻿using Heth_AIO.myClasses;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using KeyAuth;
using System.Collections.Specialized;
using System.Text.RegularExpressions;
using System.IO;
using System.Net;
using System.Net.Http;
using Binarysharp.MemoryManagement;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using System.Diagnostics;

namespace Heth_AIO
{
    public partial class MainApp : Form
    {
        string AppName = "Heth AIO";
        string keyExpiry = "";
        Addresses myAddress = new Addresses();
        //MacroCMD myMacro = new MacroCMD();
        AutoCMD myAuto;
        CheckerCMD myChecker;
        EngineData myEngineData;
        CheatCMD myCheat;
        MacroCMD myMacro;
        ProcessCMD myProcess = new ProcessCMD();
        ExtraMacroForm myExtraMacro = null;
        ExtraMacroForm myExtraMacro2 = null;
        ExtraMacroForm myExtraMacro3 = null;
        ExtraMacroForm myExtraMacro4 = null;
        ExtraMacroForm myExtraMacro5 = null;
        //drag stuff
        private bool mouseDown;
        private Point lastLocation;
        bool isUsingAutoHP = false;
        bool isUsingAutoMP = false;
        bool isUsingAutoAFK = false;
        bool runLoop = false;
        bool runCtrlLoop = false;
        bool threadRunning = false;
        bool threadCtrlRunning = false;
        bool isMonitoringKeyboard = true;
        string customMsgBox = "hide";
        string curKey = "0";

        [DllImport("Gdi32.dll", EntryPoint = "DeleteObject")]
        public static extern bool DeleteObject(IntPtr hObject);
        //corner edges
        [DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn
(
    int nLeftRect,     // x-coordinate of upper-left corner
    int nTopRect,      // y-coordinate of upper-left corner
    int nRightRect,    // x-coordinate of lower-right corner
    int nBottomRect,   // y-coordinate of lower-right corner
    int nWidthEllipse, // width of ellipse
    int nHeightEllipse // height of ellipse
);
        public MainApp()
        {
            InitializeComponent();
            //KeyAuthApp = app;
            keyExpiry = UnixTimeToDateTime(long.Parse(Login.KeyAuthApp.user_data.subscriptions[0].expiry)).ToString();
            ChangeSize(856, 534);

            //corner edges
            this.FormBorderStyle = FormBorderStyle.None;
            IntPtr handle = CreateRoundRectRgn(0, 0, Width, Height, 20, 20);
            if (handle == IntPtr.Zero)
#pragma warning disable CS0642 // Possible mistaken empty statement
                ; // error with CreateRoundRectRgn
#pragma warning restore CS0642 // Possible mistaken empty statement
            Region = System.Drawing.Region.FromHrgn(handle);
            DeleteObject(handle);
        }
        private async void Form1_Load(object sender, EventArgs e)
        {


            //SendToDiscord("Heth", "Character: Heth is DEAD 💀 RIP ..\\nTarget Name: blabla\\nWindow Name: KALAIO bla");

            AppName = System.Reflection.Assembly.GetEntryAssembly().GetName().Name;
            string onlineAppName = await Login.KeyAuthApp.var("appname");
            curKey = Login.KeyAuthApp.user_data.username;
            if (onlineAppName != AppName)
            {
                lblUpdateLink.Visible = true;
                DialogResult dialogResult = MessageBox.Show("There's a new version available (" + onlineAppName + "), do you want to download it ?", "Heth AIO", MessageBoxButtons.YesNo);
                if (dialogResult == DialogResult.Yes)
                {
                    System.Diagnostics.Process.Start(await Login.KeyAuthApp.var("updateurl"));
                }
            }

            lblExpire.Text = "  Expiry: " + keyExpiry;

            workerKeyboardMonitor.RunWorkerAsync();
            //center macro box
            richMacroBox.SelectionAlignment = HorizontalAlignment.Center;
            richMacroCtrlBox.SelectionAlignment = HorizontalAlignment.Center;

            panelMacroBox.Size = new Size(222, 345);
            richMacroBox.Size = new Size(222, 321);

            loadUserSettings();

            // Load Pastebin data for engine types 3 and 4
            await LoadPastebinAddresses();

            //make panels rounded
            DrawRoundRect(panelMacroCtrl);
            DrawRoundRect(panelMacroBox);
            DrawRoundRect(panelMacroPresets);
            DrawRoundRect(panelMacroController);
            DrawRoundRect(panelMacroMisc);
            DrawRoundRect(panelAutoAntiAFK);
            DrawRoundRect(panelAutoHPMP);
            DrawRoundRect(panelCheckersDT);
            DrawRoundRect(panelCheckerTimers);
            DrawRoundRect(panelCheckerDeathStuff);
            DrawRoundRect(panelCheatZoom);
            DrawRoundRect(panelCheatTarget);
            DrawRoundRect(panelCheatsMisc);
            DrawRoundRect(panelCheatsStats);
            DrawRoundRect(panelCheatsHTML);
            DrawRoundRect(panelCheatsKnight);
            DrawRoundRect(panelCheckersFindPlayer);
            DrawRoundRect(panelCheckerOther);

            showPanel(panelInject);

            new Thread(async () => {
                Thread.Sleep(1500);
                try
                {
                    await Login.KeyAuthApp.log($"Version: {AppName} IP: {Login.KeyAuthApp.user_data.ip} -> Launched the bot");
                }
                catch { }
            }).Start();



        }

        private async void showCustomMsg()
        {
            if (await Login.KeyAuthApp.var("customBox") != customMsgBox)
            {
                string msg = await Login.KeyAuthApp.var("customBoxMsg");
                MessageBox.Show(msg, "Heth AIO");
                customMsgBox = "show";
            }
        }
        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            resetBot();
        }
        public void ChangeSize(int width, int height)
        {
            this.Size = new Size(width, height);
        }
        public DateTime UnixTimeToDateTime(long unixtime)
        {
            System.DateTime dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Local);
            dtDateTime = dtDateTime.AddSeconds(unixtime).ToLocalTime();
            return dtDateTime;
        }
        public void DrawRoundRect(Panel panel)
        {
            IntPtr handle = CreateRoundRectRgn(0, 0, panel.Width, panel.Height, 15, 15);

            if (handle == IntPtr.Zero)
#pragma warning disable CS0642 // Possible mistaken empty statement
                ; // error with CreateRoundRectRgn
#pragma warning restore CS0642 // Possible mistaken empty statement
            panel.Region = System.Drawing.Region.FromHrgn(handle);
            DeleteObject(handle);
        }


        public static byte[] ImageToByte(Image img)
        {
            ImageConverter converter = new ImageConverter();
            return (byte[])converter.ConvertTo(img, typeof(byte[]));
        }

        public void Console(string value)
        {
            if (InvokeRequired)
            {
                this.Invoke(new Action<string>(Console), new object[] {
          value
        });
                return;
            }
            txtConsole.AppendText(Environment.NewLine + value);
        }
        public string getDiscordName(string key)
        {
            string response = "<@&1166055516737372242>";
            switch (key)
            {
                case "xqGQCe-hUoi5x-62zCd1-QV0DPu":
                    response = "<@123086354451136513>";//heth
                    break;
                case "0L4zMx-fXochV-ExqdER-PRJAad":
                    response = "<@231861663089819648>";//dodan
                    break;
                case "wvSMw7-bH8b79-MjD3je-C6QPa2":
                    response = "<@140207707029241856>";//jawad
                    break;
                case "vouwwd-afsaqn-fqhrhn-wfcvtu":
                    response = "<@201217414245908480>";//sain
                    break;
                case "tXQNpG-O9UyoD-8feU3V-TDOkGR":
                    response = "<@215192079054143488>";//mahmood
                    break;
                case "ohxr4Z-OU2VFa-5ReN14-fEAQ8C":
                    response = "<@125338561506443264>";//mahdi
                    break;
                case "TUi9QZ-4rQr2l-dFXibK-Elb8fa":
                    response = "<@240460299331698688>";//reddot
                    break;
                case "wqthap-utdwii-whhile-ygsvhc":
                    response = "<@194954577378803712>";//aloyka
                    break;
                case "IvvLn7-8k9MW9-Zqb45d-ByHrWx":
                    response = "<@215180314945781771>";//joly
                    break;
                case "CI3OuE-OrEWiF-b9lbh5-cfD0Iw":
                    response = "<@831783020407160833>";//patron
                    break;
                case "ftoidf-fnajmm-rxgscu-evzpgh":
                    response = "<@125603886697414657>";//qassim
                    break;
                case "2ZEadi-mIOi2x-XMTiLm-viD243":
                    response = "<@857179653554831390>";//Abood
                    break;
                case "jfJHfg-pjfbeV-B7yhjC-k4Ecrh":
                    response = "<@429359526488047616>";//troop
                    break;
                case "OlnDLL-nMA8gH-7JtmyD-HmrUja":
                    response = "<@624734761725722664>";//kingkong
                    break;
                case "zqxgya-nmsdbz-ixtufg-wsczeu":
                    response = "<@580651719143325697>";//DeeBo
                    break;
                case "5J4MDT-rt4n0a-kEB3MZ-8jhY2K":
                    response = "<@605916409121210378>";//m aqeel
                    break;
                default:
                    response = "<@&1166055516737372242>";
                    break;
                    
            }
            return response;
        }
        public void SendSSToDiscord(string username , string message)
        {

            new Thread(() => {
                try
                {

                    Imports.SetForegroundWindow(myProcess.myCurInjectedHandle);
                    Imports.SetWindowPos(myProcess.myCurInjectedHandle, 0, 0, 0, 0, 0, 0x0001 | 0x0040);
                    Thread.Sleep(1000);
                    var bmp = Imports.CaptureWindow(myProcess.myCurInjectedHandle);
                    //string Webhook_link = "https://discord.com/api/webhooks/1160226561035866143/VX7uHlty_KkdNrrSE-Z7JfCtPzIFoffkgy7q4rDuVLQ8WMbs-ELgbxIAJDAH6z9XOhoa"; // private
                    string Webhook_link = "https://discord.com/api/webhooks/1103711904209326180/MJSIu0a9972fqCjiuhhN5JaUWrzMb7dtbRsYlIEer2HsHK7WNWySOeGQNkgK_QswYHFb";
                    
                    if (!myProcess.IsRunning())
                    {
                        SendToDiscord(username, message);
                        return;
                    }
                        

                    using (HttpClient httpClient = new HttpClient())
                    {
                        MultipartFormDataContent form = new MultipartFormDataContent();
                        //var file_bytes = System.IO.File.ReadAllBytes();
                        var imageBytes = ImageToByte(bmp);
                        form.Add(new StringContent("{\"username\": \"" + username + "\",\"content\": \"" + message + "\"}"), "payload_json");
                        form.Add(new ByteArrayContent(imageBytes, 0, imageBytes.Length), "Image", "img.png");

                        httpClient.PostAsync(Webhook_link, form).Wait();
                        httpClient.Dispose();
                    }
                }
                catch
                {
                    SendToDiscord(username, message);
                    Console("Attempted to send image to discord failed. let Heth know about it");
                }
            }).Start();



        }

        public void SendToDiscord(string username, string message)
        {
            try
            {
                WebClient client = new WebClient();
                client.Headers.Add("Content-Type", "application/json");
                string payload = "{\"username\": \"" + username + "\",\"content\": \"" + message + "\"}";
                client.UploadData("https://discord.com/api/webhooks/1103711904209326180/MJSIu0a9972fqCjiuhhN5JaUWrzMb7dtbRsYlIEer2HsHK7WNWySOeGQNkgK_QswYHFb", Encoding.UTF8.GetBytes(payload));
            
            }
            catch
            {
                Console("Attempted to send death message to discord failed. let Heth know about it");
            }

        }

        private void saveUserSettings()
        {
            Heth_AIO.Properties.Settings.Default.manaBox = manaBox.SelectedIndex;
            Heth_AIO.Properties.Settings.Default.healthBox = healthBox.SelectedIndex;
            Heth_AIO.Properties.Settings.Default.engineBox = comboEngineType.SelectedIndex;
            Heth_AIO.Properties.Settings.Default.presetBox = comboPresets.SelectedIndex;
            Heth_AIO.Properties.Settings.Default.monsterName = txtTarget.Text;
            Heth_AIO.Properties.Settings.Default.winName = txtWinName.Text;

            Heth_AIO.Properties.Settings.Default.nAntiAfkCD = int.Parse(txtNormalAntiAfk.Text);
            Heth_AIO.Properties.Settings.Default.nAntiAfkX = int.Parse(mouseXNum.Text);
            Heth_AIO.Properties.Settings.Default.nAntiAfkY = int.Parse(mouseYNum.Text);
            Heth_AIO.Properties.Settings.Default.GMNamesBox = txtPlayerNames.Text;
            Heth_AIO.Properties.Settings.Default.ctrlMacro = richMacroCtrlBox.Text;


            Heth_AIO.Properties.Settings.Default.txtMapX1 = txtBoxMapX1.Text;
            Heth_AIO.Properties.Settings.Default.txtMapX2 = txtBoxMapX2.Text;
            Heth_AIO.Properties.Settings.Default.txtMapX3 = txtBoxMapX3.Text;
            Heth_AIO.Properties.Settings.Default.txtMapX4 = txtBoxMapX4.Text;

            Heth_AIO.Properties.Settings.Default.txtMapY1 = txtBoxMapY1.Text;
            Heth_AIO.Properties.Settings.Default.txtMapY2 = txtBoxMapY2.Text;
            Heth_AIO.Properties.Settings.Default.txtMapY3 = txtBoxMapY3.Text;
            Heth_AIO.Properties.Settings.Default.txtMapY4 = txtBoxMapY4.Text;

            if (comboPresets.SelectedIndex == 0)
            {
                Heth_AIO.Properties.Settings.Default.txtMacroName = txtBoxPreset.Text;
                Heth_AIO.Properties.Settings.Default.macroBox = richMacroBox.Text;
                comboPresets.Items[0] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 1)
            {
                Heth_AIO.Properties.Settings.Default.mPreset1 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset1 = txtBoxPreset.Text;
                comboPresets.Items[1] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 2)
            {
                Heth_AIO.Properties.Settings.Default.mPreset2 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset2 = txtBoxPreset.Text;
                comboPresets.Items[2] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 3)
            {
                Heth_AIO.Properties.Settings.Default.mPreset3 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset3 = txtBoxPreset.Text;
                comboPresets.Items[3] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 4)
            {
                Heth_AIO.Properties.Settings.Default.mPreset4 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset4 = txtBoxPreset.Text;
                comboPresets.Items[4] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 5)
            {
                Heth_AIO.Properties.Settings.Default.mPreset5 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset5 = txtBoxPreset.Text;
                comboPresets.Items[5] = txtBoxPreset.Text;
            }

            Heth_AIO.Properties.Settings.Default.Save();
        }
        private void loadUserSettings()
        {
            manaBox.SelectedIndex = Heth_AIO.Properties.Settings.Default.manaBox;
            healthBox.SelectedIndex = Heth_AIO.Properties.Settings.Default.healthBox;
            comboEngineType.SelectedIndex = Heth_AIO.Properties.Settings.Default.engineBox;
            comboPresets.SelectedIndex = Heth_AIO.Properties.Settings.Default.presetBox;

            txtTarget.Text = Heth_AIO.Properties.Settings.Default.monsterName;
            txtWinName.Text = Heth_AIO.Properties.Settings.Default.winName;


            txtNormalAntiAfk.Text = Heth_AIO.Properties.Settings.Default.nAntiAfkCD.ToString();
            mouseXNum.Text = Heth_AIO.Properties.Settings.Default.nAntiAfkX.ToString();
            mouseYNum.Text = Heth_AIO.Properties.Settings.Default.nAntiAfkY.ToString();

            txtPlayerNames.Text = Heth_AIO.Properties.Settings.Default.GMNamesBox.ToString();

            comboPresets.Items[0] = Heth_AIO.Properties.Settings.Default.txtMacroName.ToString();
            comboPresets.Items[1] = Heth_AIO.Properties.Settings.Default.txtPreset1.ToString();
            comboPresets.Items[2] = Heth_AIO.Properties.Settings.Default.txtPreset2.ToString();
            comboPresets.Items[3] = Heth_AIO.Properties.Settings.Default.txtPreset3.ToString();
            comboPresets.Items[4] = Heth_AIO.Properties.Settings.Default.txtPreset4.ToString();
            comboPresets.Items[5] = Heth_AIO.Properties.Settings.Default.txtPreset5.ToString();
            richMacroCtrlBox.Text = Heth_AIO.Properties.Settings.Default.ctrlMacro.ToString();


            txtBoxMapX1.Text = Heth_AIO.Properties.Settings.Default.txtMapX1;
            txtBoxMapX2.Text = Heth_AIO.Properties.Settings.Default.txtMapX2;
            txtBoxMapX3.Text = Heth_AIO.Properties.Settings.Default.txtMapX3;
            txtBoxMapX4.Text = Heth_AIO.Properties.Settings.Default.txtMapX4;

            txtBoxMapY1.Text = Heth_AIO.Properties.Settings.Default.txtMapY1;
            txtBoxMapY2.Text = Heth_AIO.Properties.Settings.Default.txtMapY2;
            txtBoxMapY3.Text = Heth_AIO.Properties.Settings.Default.txtMapY3;
            txtBoxMapY4.Text = Heth_AIO.Properties.Settings.Default.txtMapY4;



            if (comboPresets.SelectedIndex == 0)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtMacroName.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.macroBox.ToString();
            }
            else if (comboPresets.SelectedIndex == 1)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset1.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset1.ToString();
            }
            else if (comboPresets.SelectedIndex == 2)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset2.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset2.ToString();
            }
            else if (comboPresets.SelectedIndex == 3)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset3.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset3.ToString();
            }
            else if (comboPresets.SelectedIndex == 4)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset4.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset4.ToString();
            }
            else if (comboPresets.SelectedIndex == 5)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset5.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset5.ToString();
            }
        }

        private async Task LoadPastebinAddresses()
        {
            try
            {
                // Load addresses for engine type 3
                string pastebinData3 = await myAddress.FetchPastebinData("https://pastebin.com/raw/gLMndm8V");
                if (!string.IsNullOrEmpty(pastebinData3))
                {
                    // Create a temporary Addresses object to parse the data
                    Addresses tempAddress3 = new Addresses();
                    tempAddress3.ParsePastebinData(pastebinData3);

                    // Update the combo box text for index 3
                    if (!string.IsNullOrEmpty(tempAddress3.SelectionName))
                    {
                        comboEngineType.Items[3] = tempAddress3.SelectionName;
                    }
                }

                // Load addresses for engine type 4 (if needed in the future)
                string pastebinData4 = await myAddress.FetchPastebinData("https://pastebin.com/raw/Naasq4tv");
                if (!string.IsNullOrEmpty(pastebinData4))
                {
                    // Create a temporary Addresses object to parse the data
                    Addresses tempAddress4 = new Addresses();
                    tempAddress4.ParsePastebinData(pastebinData4);

                    // Update the combo box text for index 4
                    if (!string.IsNullOrEmpty(tempAddress4.SelectionName))
                    {
                        comboEngineType.Items[4] = tempAddress4.SelectionName;
                    }
                }

                // Load addresses for engine type 5 (if needed in the future)
                string pastebinData5 = await myAddress.FetchPastebinData("https://pastebin.com/raw/0sAjQmj9");
                if (!string.IsNullOrEmpty(pastebinData5))
                {
                    // Create a temporary Addresses object to parse the data
                    Addresses tempAddress5 = new Addresses();
                    tempAddress5.ParsePastebinData(pastebinData5);

                    // Update the combo box text for index 5
                    if (!string.IsNullOrEmpty(tempAddress5.SelectionName))
                    {
                        comboEngineType.Items[5] = tempAddress5.SelectionName;
                    }
                }

            }
            catch (Exception ex)
            {
                Console("Error loading Online Engine data: " + ex.Message);
                // Handle any errors silently or log them
                // The combo box items will remain as "Loading.." if there's an error
            }
        }

        private void sysIsWorking()
        {
            //checkers
            if (checkDeath.Checked || checkStopDead.Checked || checkCloseDead.Checked || checkTarget.Checked || checkAlertLowHP.Checked || btnCheckerTimer.Text == "Stop")
            {
                lblWorkingChecker.Visible = true;
            }
            else
            {
                lblWorkingChecker.Visible = false;
            }

            //macro
            if (btnStartBot.Enabled == false)
            {
                lblWorkingMacro.Visible = true;
            }
            else
            {
                lblWorkingMacro.Visible = false;
            }

            //autos
            if (checkAntiAFK.Checked || checkAutoHealth.Checked || checkAutoMana.Checked)
            {
                lblWorkingAuto.Visible = true;
            }
            else
            {
                lblWorkingAuto.Visible = false;
            }

            //cheats
            if (checkAttackSpeed.Checked || checkInifityRush.Checked || checkTargetOverlay.Checked || checkLockTarget.Checked || checkTargetInfoToSys.Checked || checkPWW3rd.Checked || checkPWW2nd.Checked)
            {
                lblWorkingCheat.Visible = true;
            }
            else
            {
                lblWorkingCheat.Visible = false;
            }

            if (Application.OpenForms["Extra1"] != null || Application.OpenForms["Extra2"] != null || Application.OpenForms["Extra3"] != null || Application.OpenForms["Extra4"] != null || Application.OpenForms["Extra5"] != null)
            {
                panelMacroBox.Size = new Size(222, 200);
                richMacroBox.Size = new Size(222, 176);
                panelMacroCtrl.Visible = true;
            }
            else
            {
                panelMacroBox.Size = new Size(222, 345);
                richMacroBox.Size = new Size(222, 321);
                panelMacroCtrl.Visible = false;
            }

        }
        private void showPanel(Panel panel)
        {
            panel.Size = new Size(610, 437);
            panel.Location = new Point(246, 96);
            panel.BringToFront();
        }
        private void MenuButtonUnselectAll()
        {
            btnMacro.ForeColor = Color.Silver;
            btnAuto.ForeColor = Color.Silver;
            btnCheats.ForeColor = Color.Silver;
            btnCheckers.ForeColor = Color.Silver;



        }
        private void MenuButtonsDisable()
        {
            btnMacro.Enabled = false;
            btnAuto.Enabled = false;
            btnCheats.Enabled = false;
            btnCheckers.Enabled = false;
        }
        private void MenuButtonsEnable()
        {
            btnMacro.Enabled = true;
            btnAuto.Enabled = true;
            btnCheats.Enabled = true;
            btnCheckers.Enabled = true;
        }
        private void MenuButtonClicked(System.Windows.Forms.Button btn,Color color)
        {
            lblTitle.Text = btn.Text.Trim();
            MenuButtonUnselectAll();
            btn.ForeColor = color;
            panelLeft.BackColor = color;
            panelLeft.Visible = true;
            panelLeft.Top = btn.Top;
            panelLeft.Height = btn.Height;
            
        }
        private void extBTN_Click(object sender, EventArgs e)
        {
            isMonitoringKeyboard = false;
            saveUserSettings();
            resetBot();
            Application.Exit();
            
        }

        private void minBTN_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        //btnMacro
        private void button1_Click(object sender, EventArgs e)
        {
            sysIsWorking();
            MenuButtonClicked(btnMacro,Color.FromArgb(101, 69, 151));
            showPanel(panelMacro);
            
        }

        private void btnAuto_Click(object sender, EventArgs e)
        {
            sysIsWorking();
            MenuButtonClicked(btnAuto, Color.FromArgb(238, 225, 179));
            showPanel(panelAuto);
            
            

        }

        private void btnCheckers_Click(object sender, EventArgs e)
        {

            sysIsWorking();
            MenuButtonClicked(btnCheckers, Color.FromArgb(0, 242, 242));
            showPanel(panelCheckers);
            
            
        }

        private void btnCheats_Click(object sender, EventArgs e)
        {
            sysIsWorking();
            MenuButtonClicked(btnCheats, Color.FromArgb(206, 45, 79));
            showPanel(panelCheats);

        }

        StringCollection GetLinesCollectionFromTextBox(RichTextBox textBox)
        {
            StringCollection lines = new StringCollection();

            // lineCount may be -1 if TextBox layout info is not up-to-date.
            int lineCount = textBox.Lines.Count();

            for (int line = 0; line < lineCount; line++)
            {
                // GetLineText takes a zero-based line index.
                lines.Add(textBox.Lines[line]);
            }
            return lines;
        }
        public void resetHighlights(int x, RichTextBox Box)
        {
            if (InvokeRequired)
            {
                this.Invoke(new Action<int, RichTextBox>(resetHighlights), new object[] {
          x,Box
        });
                return;
            }
            if (x == 0)
            {
                Box.SelectAll();
                Box.SelectionColor = Color.Silver;
                Box.DeselectAll();
            }
            else if (x == 1)
            {
                Box.SelectAll();
                Box.SelectionColor = Color.Gold;
                Box.DeselectAll();
            }

            if (Box.HideSelection == false)
            {
                Box.HideSelection = true;
            }

        }
        public void HighlightLine(int lineIdx, RichTextBox Box)
        {
            if (InvokeRequired)
            {
                this.Invoke(new Action<int, RichTextBox>(HighlightLine), new object[] {
          lineIdx,Box
        });
                return;
            }
            Box.Select(Box.GetFirstCharIndexFromLine(lineIdx), Box.Lines[lineIdx].Length);
            Box.SelectionColor = Color.SeaGreen;

        }
        public void HighlightRedLine(int lineIdx, RichTextBox Box)
        {
            if (InvokeRequired)
            {
                this.Invoke(new Action<int, RichTextBox>(HighlightRedLine), new object[] {
          lineIdx,Box
        });
                return;
            }
            Box.Select(Box.GetFirstCharIndexFromLine(lineIdx), Box.Lines[lineIdx].Length);
            Box.SelectionColor = Color.Red;

        }

        public void macroStart(string chosenMacro)
        {
            if (chosenMacro == "main")
            {

                if (runLoop == true)
                    return;

                if (runLoop == false && runCtrlLoop == true)
                {
                    this.Invoke(new MethodInvoker(delegate () { softStartMacroBot(); })); 
                }
                if(myExtraMacro != null)
                    myExtraMacro.MacroStart = false;
                if (myExtraMacro2 != null)
                    myExtraMacro2.MacroStart = false;
                if (myExtraMacro3 != null)
                    myExtraMacro3.MacroStart = false;
                if (myExtraMacro4 != null)
                    myExtraMacro4.MacroStart = false;
                if (myExtraMacro5 != null)
                    myExtraMacro5.MacroStart = false;
            }
            else if (chosenMacro == "macro1")
            {
                if (myExtraMacro.MacroStart)
                    return;

                this.Invoke(new MethodInvoker(delegate () { softStopMacroBot(); }));
                if (myExtraMacro != null)
                    myExtraMacro.MacroStart = true;
                if (myExtraMacro2 != null)
                    myExtraMacro2.MacroStart = false;
                if (myExtraMacro3 != null)
                    myExtraMacro3.MacroStart = false;
                if (myExtraMacro4 != null)
                    myExtraMacro4.MacroStart = false;
                if (myExtraMacro5 != null)
                    myExtraMacro5.MacroStart = false;
            }
            else if (chosenMacro == "macro2")
            {
                if (myExtraMacro2.MacroStart)
                    return;

                this.Invoke(new MethodInvoker(delegate () { softStopMacroBot(); }));
                if (myExtraMacro != null)
                    myExtraMacro.MacroStart = false;
                if (myExtraMacro2 != null)
                    myExtraMacro2.MacroStart = true;
                if (myExtraMacro3 != null)
                    myExtraMacro3.MacroStart = false;
                if (myExtraMacro4 != null)
                    myExtraMacro4.MacroStart = false;
                if (myExtraMacro5 != null)
                    myExtraMacro5.MacroStart = false;
            }
            else if (chosenMacro == "macro3")
            {
                if (myExtraMacro3.MacroStart)
                    return;

                this.Invoke(new MethodInvoker(delegate () { softStopMacroBot(); }));
                if (myExtraMacro != null)
                    myExtraMacro.MacroStart = false;
                if (myExtraMacro2 != null)
                    myExtraMacro2.MacroStart = false;
                if (myExtraMacro3 != null)
                    myExtraMacro3.MacroStart = true;
                if (myExtraMacro4 != null)
                    myExtraMacro4.MacroStart = false;
                if (myExtraMacro5 != null)
                    myExtraMacro5.MacroStart = false;
            }
            else if (chosenMacro == "macro4")
            {
                if (myExtraMacro4.MacroStart)
                    return;

                this.Invoke(new MethodInvoker(delegate () { softStopMacroBot(); }));
                if (myExtraMacro != null)
                    myExtraMacro.MacroStart = false;
                if (myExtraMacro2 != null)
                    myExtraMacro2.MacroStart = false;
                if (myExtraMacro3 != null)
                    myExtraMacro3.MacroStart = false;
                if (myExtraMacro4 != null)
                    myExtraMacro4.MacroStart = true;
                if (myExtraMacro5 != null)
                    myExtraMacro5.MacroStart = false;
            }
            else if (chosenMacro == "macro5")
            {
                if (myExtraMacro5.MacroStart)
                    return;

                this.Invoke(new MethodInvoker(delegate () { softStopMacroBot(); }));
                if (myExtraMacro != null)
                    myExtraMacro.MacroStart = false;
                if (myExtraMacro2 != null)
                    myExtraMacro2.MacroStart = false;
                if (myExtraMacro3 != null)
                    myExtraMacro3.MacroStart = false;
                if (myExtraMacro4 != null)
                    myExtraMacro4.MacroStart = false;
                if (myExtraMacro5 != null)
                    myExtraMacro5.MacroStart = true;
            }
        }
        public void botterCtrl()
        {
            resetHighlights(0,richMacroCtrlBox);
            richMacroCtrlBox.ReadOnly = true;
            Console(Environment.NewLine + "[Controller] Started");

            StringCollection lines = GetLinesCollectionFromTextBox(richMacroCtrlBox);
            if (lines.Count == 0)
            {
                //stopMacroBot();
                //MessageBox.Show("I cant read any macro command, stopping..", "Heth's AIO");
                return;
            }
            if (!richMacroCtrlBox.Text.ToLower().Contains("wait"))
            {
                Console("it's very important to put `Wait xxx` between macros or else it will start and stop them every 1 second");
            }

            runCtrlLoop = true;
            var botterCtrlThread = new Thread(() => {

                try
                {


                    while (runCtrlLoop)
                    {
                        resetHighlights(1, richMacroCtrlBox);
                        int linei = 0;

                        foreach (string line in lines)
                        {
                            if (runCtrlLoop == false)
                            {
                                threadCtrlRunning = true;
                                break;
                            }
                            //lower all
                            string lowerLine = line.ToLower();
                            if (lowerLine.Contains("wait"))
                            {
                                HighlightLine(linei, richMacroCtrlBox);
                                // Console("[Marco] " + line);
                                Match cd = Regex.Match(line, @"\d+");
                                int parsedintValue;
                                if (int.TryParse(cd.Value, out parsedintValue))
                                {
                                    Thread.Sleep(int.Parse(cd.Value));
                                }
                            }
                            else if (lowerLine.Contains("macro main") || lowerLine.Contains("main macro"))
                            {
                                HighlightLine(linei, richMacroCtrlBox);
                                Console(Environment.NewLine + "[Controller] Starting Main Macro");
                                macroStart("main");
                                
                            }
                            else if (lowerLine.Contains("macro 1"))
                            {
                                HighlightLine(linei, richMacroCtrlBox);
                                macroStart("macro1");
                                Console(Environment.NewLine + "[Controller] Started Macro 1 & stopped other macros");
                            }
                            else if (lowerLine.Contains("macro 2"))
                            {
                                HighlightLine(linei, richMacroCtrlBox);
                                macroStart("macro2");
                                Console(Environment.NewLine + "[Controller] Started Macro 2 & stopped other macros");
                            }
                            else if (lowerLine.Contains("macro 3"))
                            {
                                HighlightLine(linei, richMacroCtrlBox);
                                macroStart("macro3");
                                Console(Environment.NewLine + "[Controller] Started Macro 3 & stopped other macros");
                            }
                            else if (lowerLine.Contains("macro 4"))
                            {
                                HighlightLine(linei, richMacroCtrlBox);
                                macroStart("macro4");
                                Console(Environment.NewLine + "[Controller] Started Macro 4 & stopped other macros");
                            }
                            else if (lowerLine.Contains("macro 5"))
                            {
                                HighlightLine(linei, richMacroCtrlBox);
                                macroStart("macro5");
                                Console(Environment.NewLine + "[Controller] Started Macro 5 & stopped other macros");
                            }
                            else { HighlightRedLine(linei, richMacroCtrlBox); }
                            linei++;
                        }


                        Thread.Sleep(1500);
                    }

                    

                    }
                catch
                {
                    Console("[Controller] closed Controller thread successfully.");

                }


            });

            botterCtrlThread.Start();
            new Thread(() => {
                while (threadCtrlRunning)
                {


                    if (runCtrlLoop == false)
                    {
                        botterCtrlThread.Interrupt();
                        botterCtrlThread.Abort();
                        //threadRunning = false;
                    }
                    Thread.Sleep(1000);
                }
            }).Start();
        }

        public void botter()
        {

            resetHighlights(0,richMacroBox);
            richMacroBox.ReadOnly = true;
            Console(Environment.NewLine + "[Macro] Started");

            StringCollection lines = GetLinesCollectionFromTextBox(richMacroBox);
            if (lines.Count == 0)
            {
                stopMacroBot();
                MessageBox.Show("I cant read any macro command, stopping..", "Heth's AIO");
                return;
            }
            IntPtr handle = myProcess.myCurInjectedHandle;
            if (handle.Equals(IntPtr.Zero) || !myProcess.IsRunning())
            {
                myProcess.selectedProcess = 0;
                myProcess.myCurInjectedHandle = IntPtr.Zero;
                stopMacroBot();
                
                return;
            }
            runLoop = true;

            var botterThread = new Thread(() => {
                try
                {

                    while (runLoop)
                    {
                        resetHighlights(1, richMacroBox);
                        int linei = 0;

                        foreach (string line in lines)
                        {

                            if (runLoop == false)
                            {
                                threadRunning = true;
                                break;
                            }
                            if ((handle.Equals(IntPtr.Zero) || !myProcess.IsRunning() || myProcess.selectedProcess == 0) && safeModeCheckBox.Checked == false)
                            {
                                myProcess.myCurInjectedHandle = IntPtr.Zero;
                                runLoop = false;
                                MessageBox.Show("Engine is not running, STOPPING!! .", "Heth's AIO");
                                stopMacroBot();
                                break;
                            }

                            //lower all
                            string lowerLine = line.ToLower();

/*                            if (myExtraMacro != null && myExtraMacro.pauseMacro && myExtraMacro.pauseMacroWaitNow && myExtraMacro.MacroStarted)
                            {
                                runLoop = false;
                                Thread.Sleep(1500);
                                break;
                            }else if (myExtraMacro2 != null && myExtraMacro2.pauseMacro && myExtraMacro2.pauseMacroWaitNow && myExtraMacro2.MacroStarted)
                            {
                                runLoop = false;
                                Thread.Sleep(1500);
                                break;
                            }
                            else if (myExtraMacro3 != null && myExtraMacro3.pauseMacro && myExtraMacro3.pauseMacroWaitNow && myExtraMacro3.MacroStarted)
                            {
                                runLoop = false;
                                Thread.Sleep(1500);
                                break;
                            }
                            else if (myExtraMacro4 != null && myExtraMacro4.pauseMacro && myExtraMacro4.pauseMacroWaitNow && myExtraMacro4.MacroStarted)
                            {
                                runLoop = false;
                                Thread.Sleep(1500);
                                break;
                            }
                            else if (myExtraMacro5 != null && myExtraMacro5.pauseMacro && myExtraMacro5.pauseMacroWaitNow && myExtraMacro5.MacroStarted)
                            {
                                runLoop = false;
                                Thread.Sleep(1500);
                                break;
                            }*/

                            if (lowerLine.Contains("wait"))
                            {
                                HighlightLine(linei, richMacroBox);
                                // Console("[Marco] " + line);
                                Match cd = Regex.Match(line, @"\d+");
                                int parsedintValue;
                                if (int.TryParse(cd.Value, out parsedintValue))
                                {
                                    Thread.Sleep(int.Parse(cd.Value));
                                }
                            }
                            else if (lowerLine == "key space")
                            {
                                HighlightLine(linei, richMacroBox);
                                Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_SPACE, 0);
                            }
                            else if (lowerLine == "key tab")
                            {
                                HighlightLine(linei, richMacroBox);
                                Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_TAB, 0);
                            }
                            else if (lowerLine == "key `")
                            {
                                HighlightLine(linei, richMacroBox);
                                Imports.PostMessage(handle, myAddress.WM_KEYDOWN, 0xC0, 0);
                            }
                            else if (lowerLine == "rotate camera")
                            {
                                
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.RotateCamera())
                                {
                                    Console("[Macro] "+line);
                                }
                            }
                            else if (lowerLine.Contains("smartkey"))
                            {

                                HighlightLine(linei, richMacroBox);
                                
                                int parsedintValue;
                                Match key = Regex.Match(line, @"\d+");
                                if (int.TryParse(key.Value, out parsedintValue))
                                {
                                    int keyvalue = int.Parse(key.Value);
                                    if (myMacro.SmartKeyPress(keyvalue-1))
                                    {
                                        Console("[Marco] Smart Key " + line);
                                    }
                                }
                              
                            }
                            else if (lowerLine.Contains("key") || lowerLine.Contains("keyboard"))
                            {
                                HighlightLine(linei, richMacroBox);
                                Console("[Marco] Pressed " + line);
                                int parsedintValue;
                                Match key = Regex.Match(line, @"\d+");
                                if (int.TryParse(key.Value, out parsedintValue))
                                {
                                    int keyvalue = int.Parse(key.Value);
                                    myEngineData.KeyPress(keyvalue);
                                }
                            }
                            else if (lowerLine.Contains("mouseleft") || lowerLine.Contains("left") || lowerLine.Contains("clickleft"))
                            {
                                HighlightLine(linei, richMacroBox);
                                Console("[Marco] Clicked " + line);
                                MatchCollection key = Regex.Matches(line, @"\d+");
                                if (key.Count == 2)
                                {
                                    int screenX = int.Parse(key[0].Value);
                                    int screenY = int.Parse(key[1].Value);
                                    myEngineData.LeftMouseClick(screenX, screenY);
                                }
                            }
                            else if (lowerLine.Contains("mouseright") || lowerLine.Contains("right") || lowerLine.Contains("clickright"))
                            {
                                HighlightLine(linei, richMacroBox);
                                Console("[Marco] Clicked " + line);
                                MatchCollection key = Regex.Matches(line, @"\d+");
                                if (key.Count == 2)
                                {
                                    int screenX = int.Parse(key[0].Value);
                                    int screenY = int.Parse(key[1].Value);
                                    myEngineData.RightMouseClick(screenX, screenY);
                                }
                            }
                            else if (lowerLine == "pick if good hp")
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myEngineData.curHP > myEngineData.maxHP * 0.80)
                                {
                                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_SPACE, 0);
                                    Console("[Marco] good HP, picking..");
                                }
                            }
                            else if (lowerLine == "drop pick" || lowerLine == "drop pick med" || lowerLine == "drop pick far" || lowerLine == "drop pick very far" || lowerLine == "pick drop" || lowerLine == "pick drop med" || lowerLine == "pick drop far" || lowerLine == "pick drop very far")
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.DropPick(line))
                                {

                                    Console("[Macro] -> " + line);
                                }

                            }
                            else if (lowerLine.Contains("next target if \""))
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.NextTargetIfName(line, myEngineData.targetName))
                                {

                                    Console("[Macro] -> " + line);

                                }

                            }
                            else if (lowerLine == "next target")
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.NextTarget())
                                {

                                    Console("[Macro] -> " + line);
                                }

                            }
                            else if (lowerLine == "next target if not monster")
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.NextTargetIfNotMonster())
                                {

                                    Console("[Macro] -> " + line);
                                }
                            }
                            else if (lowerLine.Contains("next target if dead"))
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.NextTargetIfDead())
                                {

                                    Console("[Macro] -> " + line);
                                }
                            }
                            else if (lowerLine == "next target if wall")
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.NextTargetIfWall())
                                {

                                    Console("[Macro] -> " + line);
                                }
                            }else if (lowerLine.Contains("select player \""))
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.SelectPlayer(line, 0))
                                {
                                    Console("[Macro] -> " + line);
                                }
                            }
                            else if (lowerLine.Contains("target player \""))
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.SelectPlayer(line, 1))
                                {
                                    Console("[Macro] -> " + line);
                                }
                            }
                            else if (lowerLine.Contains("autoatk \""))
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.AutoAtkBotByName(line))
                                {
                                    Console("[Macro] -> " + line);
                                }
                            }
                            else if (lowerLine.Contains("press") && lowerLine.Contains("if dead"))
                            {

                                HighlightLine(linei, richMacroBox);
                                int curtarget = myMacro.GetTargetID();
                                if (curtarget != 0)
                                {
                                    UInt32 curtargetMinHP = myMacro.GetTargetMinHP();
                                    if (curtargetMinHP == 0)
                                    {
                                        Console("[Marco] " + line);
                                        int parsedintValue;
                                        Match key = Regex.Match(line, @"\d+");
                                        if (int.TryParse(key.Value, out parsedintValue))
                                        {
                                            int keyvalue = int.Parse(key.Value);
                                            myEngineData.KeyPress(keyvalue);
                                        }
                                    }
                                }

                            }
                            else if (lowerLine.Contains("press") && lowerLine.Contains("if low hp"))
                            {

                                HighlightLine(linei, richMacroBox);
                                if (myEngineData.curHP < myEngineData.maxHP * 0.20)
                                {
                                    Console("[Marco] " + line);
                                    int parsedintValue;
                                    MatchCollection key = Regex.Matches(line, @"\d+");
                                    if (key.Count == 2)
                                    {
                                        if (int.TryParse(key[0].Value, out parsedintValue) && int.TryParse(key[1].Value, out parsedintValue))
                                        {
                                            int keyvalue = int.Parse(key[0].Value);
                                            int waitvalue = int.Parse(key[1].Value);
                                            myEngineData.KeyPress(keyvalue);
                                            Thread.Sleep(waitvalue);
                                        }

                                    }
                                }

                            }
                            else if (lowerLine.Contains("press") && lowerLine.Contains("if low mana"))
                            {

                                HighlightLine(linei, richMacroBox);

                                if (myEngineData.curMP < myEngineData.maxMP * 0.10)
                                {
                                    Console("[Marco] " + line);
                                    int parsedintValue;
                                    MatchCollection key = Regex.Matches(line, @"\d+");
                                    if (key.Count == 2)
                                    {
                                        if (int.TryParse(key[0].Value, out parsedintValue) && int.TryParse(key[1].Value, out parsedintValue))
                                        {
                                            int keyvalue = int.Parse(key[0].Value);
                                            int waitvalue = int.Parse(key[1].Value);
                                            myEngineData.KeyPress(keyvalue);
                                            Thread.Sleep(waitvalue);
                                        }

                                    }
                                }

                            }
                            else if (lowerLine.Contains("normal attack") || lowerLine.Contains("normal atk"))
                            {
                                HighlightLine(linei, richMacroBox);
                                if (lowerLine == "normal attack 1" || lowerLine == "normal atk 1")
                                {
                                    if (myMacro.NormalAttack(1))
                                        Console("[Marco] " + line);
                                    
                                }else if (lowerLine == "normal attack 2" || lowerLine == "normal atk 2")
                                {
                                    if (myMacro.NormalAttack(2))
                                        Console("[Marco] " + line);
                                    
                                }else if (lowerLine == "normal attack 3" || lowerLine == "normal atk 3")
                                {
                                    if (myMacro.NormalAttack(3))
                                        Console("[Marco] " + line);

                                }else if (lowerLine == "normal attack 0" || lowerLine == "normal atk 0")
                                {
                                    if (myMacro.NormalAttack(0))
                                        Console("[Marco] " + line);

                                }
                                else
                                {
                                    if (myMacro.NormalAttack(2))
                                        Console("[Marco] " + line);
                                    
                                }


                            }
                            else if (lowerLine.Contains("skill"))
                            {
                                HighlightLine(linei, richMacroBox);
                                if (myMacro.UseSkill(line))
                                {

                                    Console("[Marco] " + line);
                                }
                            }
                            else { HighlightRedLine(linei, richMacroBox); }

                            linei++;
                        }

                    }
                }
                catch
                {
                    Console("[Marco] closed macro thread successfully.");
                }
            });
            botterThread.Start();
            new Thread(() => {
                while (threadRunning)
                {


                    if (runLoop == false)
                    {
                        botterThread.Interrupt();
                        botterThread.Abort();
                        threadRunning = false;
                    }
                    Thread.Sleep(200);
                }
            }).Start();

        }


        #region [Start|Stop|Inject|Reset]
        private async Task injectBot()
        {
            if (!txtWinName.Enabled) return;
            if (!myProcess.injectEngine(txtWinName.Text, safeModeCheckBox.Checked)) {
                MessageBox.Show("Cant find any Kalonline running", "Heth's AIO");
                return;
            }
           // new Thread(() =>
           // {
                //Login.KeyAuthApp.check();
                //Console("Current Session Validation Status:" + Login.KeyAuthApp.response.message);
               // if (Login.KeyAuthApp.var("checks") != "authdonetyvmbbxd")
               // {
                //    MessageBox.Show("Nice try faggot, there's something wrong .. bb", "Heth AIO");
                //    Environment.Exit(0);
                //}
           // }).Start();

            // Use async method for engine types 3 and 4, sync for others
            if (comboEngineType.SelectedIndex == 3 || comboEngineType.SelectedIndex == 4 || comboEngineType.SelectedIndex == 5)
            {
                await myAddress.checkSelectedAddressesAsync(comboEngineType.SelectedIndex);
               // txtConsole.AppendText("Selected: " + comboEngineType.SelectedIndex.ToString());
            }
            else
            {
                myAddress.checkSelectedAddresses(comboEngineType.SelectedIndex);
            }
            myEngineData = new EngineData(myProcess, myAddress);
            myChecker = new CheckerCMD(myEngineData);
            myAuto = new AutoCMD(myEngineData, myProcess);
            myCheat = new CheatCMD(myEngineData, myProcess, myAddress);
            myMacro = new MacroCMD(myEngineData, myProcess, myAddress);
            

            if (!safeModeCheckBox.Checked)
            {
                myEngineData.Start(NewReaderCheckBox.Checked);
                MenuButtonsEnable();
            } else
            {
                //label51.Text = sharp.ReadString((IntPtr)myAddress.nameAdd, Encoding.ASCII, false, 13);
                MenuButtonsEnable();
                btnAuto.Enabled = false;
                btnCheats.Enabled = false;
                btnCheckers.Enabled = false;
                txtWinName.Text = "KalOnline";
            }

            safeModeCheckBox.Enabled = false;
            NewReaderCheckBox.Enabled = false;
            comboEngineType.Enabled = false;
            btnInject.Enabled = false;
            btnReset.Enabled = true;
            
            MenuButtonClicked(btnMacro, Color.FromArgb(101, 69, 151));
            showPanel(panelMacro);
            lblRunInfo.Text = "Watching: "+txtWinName.Text;
            DataTimer.Enabled = true;
            txtWinName.Enabled = false;
            Console("[Injected] Changing to " + txtWinName.Text);
            //string pcDesktop = Environment.MachineName.ToString();
            //string pcUsername = Environment.UserName.ToString();
            string AppName = System.Reflection.Assembly.GetEntryAssembly().GetName().Name;
            string engineName = comboEngineType.Text;
            string winName = txtWinName.Text;
            string nwReader = "New Reader: OFF";
            string macroOnly = "Macro Only: OFF";

            if (NewReaderCheckBox.Checked)
                nwReader = "New Reader: ON";
            if(safeModeCheckBox.Checked)
                macroOnly = "Macro Only: ON";

            new Thread(async () => {
                Thread.Sleep(1500);
                Regex regex = new Regex(@"^[a-zA-Z0-9]*$");
                Match match = regex.Match(myEngineData.charName);

                if ((!match.Success || myEngineData.curHP < 0 || myEngineData.curHP > 1000000 || myEngineData.curMP < 0 || myEngineData.curMP > 1000000) && safeModeCheckBox.Checked == false)
                {
                    MessageBox.Show("Looks like you selected the wrong Engine type or this engine is not supported yet !", "Heth AIO");
                    MessageBox.Show("OR i just cant read your character's hp & mp values ! no worries you can still use the basic macro commands", "Heth AIO");
                }

                //enable later
                try
                {
                     await Login.KeyAuthApp.log($"Version: {AppName} IP: {Login.KeyAuthApp.user_data.ip} HWID: {Login.KeyAuthApp.user_data.hwid} -> Injected Engine {engineName} | {macroOnly} | {nwReader} | Window Name: {winName}");
                }
                catch { }

                showCustomMsg();
            }).Start();



            this.Activate();


        }

        private void resetBot()
        {
            if (btnInject.Enabled == true) return;

            stopMacroBot();
            if (!safeModeCheckBox.Checked)
            {
                myMacro.StopNormalAttack();
            }
            

            txtWinName.Enabled = true;
            //disable everything
            disableAllCheckBoxes();
            runLoop = false;
            runCtrlLoop = false;
            myEngineData.StopThreads();
            myCheat.StopThreads();
            myAuto.StopThreads();
            threadRunning = false;
            threadCtrlRunning = false;
            comboEngineType.Enabled = true;
            safeModeCheckBox.Enabled = true;
            NewReaderCheckBox.Enabled = true;
            panelLeft.Visible = false;
            showPanel(panelInject);
            MenuButtonsDisable();
            
            // Reset to default values - use synchronous method
            myAddress.checkSelectedAddresses(comboEngineType.SelectedIndex);
            
            myProcess.resetEngine(safeModeCheckBox.Checked);
            richMacroBox.ReadOnly = false;
            btnInject.Enabled = true;
            btnReset.Enabled = false;
            DataTimer.Enabled = false;
            

            lblName.Text = "Name";
            lblHealth.Text = "0/0";
            lblMana.Text = "0/0";
            lblMap.Text = "X:0/Y:0";

            lblTitle.Text = "Inject Please ..";
            lblRunInfo.Text = "Waiting for injection..";
            checkHideAll.Checked = false;
            checkBlockHide.Checked = false;
            checkBlockTeleport.Checked = false;
            Console("[Reset] Changing to KalOnline");
            
        }
        private void startMacroBot()
        {
            if (btnInject.Enabled == true) return;
            if (btnStartBot.Enabled == false) return;

            //new Thread(() =>
            //{
             //   Login.KeyAuthApp.check();
                //Console("Current Session Validation Status:" + Login.KeyAuthApp.response.message);
            //}).Start();

            string myReplacedMacros = myMacro.ReplaceShortcuts(richMacroBox.Text);
            richMacroBox.Text = myReplacedMacros;
            richMacroBox.ReadOnly = true;
            richMacroCtrlBox.ReadOnly = true;
            btnStartBot.Enabled = false;
            btnStopBot.Enabled = true;

            //auto disable and enable with the macro
            if (isUsingAutoHP)
                checkAutoHealth.Checked = true;

            if (isUsingAutoMP)
                checkAutoMana.Checked = true;

            if(isUsingAutoAFK)
                checkAntiAFK.Checked = true;

            //auto hp
            if (checkAutoHealth.Checked)
                isUsingAutoHP = true;
            else
                isUsingAutoHP = false;
            //auto mp
            if (checkAutoMana.Checked)
                isUsingAutoMP = true;
            else
                isUsingAutoMP = false;
            //anti afk
            if (checkAntiAFK.Checked)
                isUsingAutoAFK = true;
            else
                isUsingAutoAFK = false;

            //save
            saveUserSettings();
            //check working systems
            sysIsWorking();
            //start ctrl macro
                if (Application.OpenForms["Extra1"] != null || Application.OpenForms["Extra2"] != null || Application.OpenForms["Extra3"] != null || Application.OpenForms["Extra4"] != null || Application.OpenForms["Extra5"] != null)
                {
                    botterCtrl();
                }
            //start
            botter();
            //show the custome msg from keyauth
            //showCustomMsg();




        }
        private void stopMacroBot()
        {
                if (btnStartBot.Enabled == true) return;

                if(!safeModeCheckBox.Checked)
            {
                myMacro.StopNormalAttack();
            }
            
            runLoop = false;
            runCtrlLoop = false;
            threadCtrlRunning = true;
            threadRunning = true;
            richMacroBox.ReadOnly = false;
            richMacroCtrlBox.ReadOnly = false;
            btnStartBot.Enabled = true;
            btnStopBot.Enabled = false;

            if (isUsingAutoHP)
                checkAutoHealth.Checked = false;
            if (isUsingAutoMP)
                checkAutoMana.Checked = false;
            if (isUsingAutoAFK)
                checkAntiAFK.Checked = false;

            resetHighlights(0, richMacroBox);
            resetHighlights(0, richMacroCtrlBox);
            sysIsWorking();

            if (myExtraMacro != null)
                myExtraMacro.MacroStart = false;
            if (myExtraMacro2 != null)
                myExtraMacro2.MacroStart = false;
            if (myExtraMacro3 != null)
                myExtraMacro3.MacroStart = false;
            if (myExtraMacro4 != null)
                myExtraMacro4.MacroStart = false;
            if (myExtraMacro5 != null)
                myExtraMacro5.MacroStart = false;
            //showCustomMsg();

        }

        private void softStartMacroBot()
        {
            botter();
        }

        private void softStopMacroBot()
        {
            runLoop = false;
            threadRunning = true;
        }

        private void disableAllCheckBoxes()
        {
            foreach (Control panel in panelCheats.Controls)
            {
  
                 foreach (Control c in panel.Controls)
                {
                    if (c.GetType() == typeof(CheckBox))
                    {
                        ((CheckBox)c).Checked = false;
                    }
                }

            }

            foreach (Control panel in panelCheckers.Controls)
            {
                foreach (Control c in panel.Controls)
                {
                    if (c.GetType() == typeof(CheckBox))
                    {
                        ((CheckBox)c).Checked = false;
                    }
                }
            }


            foreach (Control panel in panelAuto.Controls)
            {
                foreach (Control c in panel.Controls)
                {
                    if (c.GetType() == typeof(CheckBox))
                    {
                        ((CheckBox)c).Checked = false;
                    }
                }
            }

        }
        private async void btnInject_Click(object sender, EventArgs e)
        {
            await injectBot();
        }
        private void btnReset_Click(object sender, EventArgs e)
        {
            resetBot();
            //MenuButtonClicked(btnMacro, Color.FromArgb(101, 69, 151));
        }
        private void btnStartBot_Click(object sender, EventArgs e)
        {
            startMacroBot();
        }
        private void btnStopBot_Click(object sender, EventArgs e)
        {
            stopMacroBot();
        }

        #endregion




        #region [Moveables]
        //draggable panels
        private void label1_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void label1_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }

        private void label1_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }
        private void panel3_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void panelControl_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }

        private void panelControl_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }

        private void panelStart_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void panelStart_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }

        private void panelStart_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }
        private void lblRunInfo_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }

        private void lblRunInfo_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void lblRunInfo_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }
        private void btnMacro_MouseHover(object sender, EventArgs e)
        {
            // picMacroWorking.BackColor = Color.FromArgb(25, 25, 25);
        }

        private void btnMacro_MouseLeave(object sender, EventArgs e)
        {
            // picMacroWorking.BackColor = Color.FromArgb(20, 20, 20);
        }

        private void btnMacro_MouseEnter(object sender, EventArgs e)
        {
            // picMacroWorking.BackColor = Color.FromArgb(25, 25, 25);
        }
        private void lblName_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void lblName_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }

        private void lblName_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }

        private void lblExpire_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void lblExpire_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }

        private void lblExpire_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }
        #endregion

        private void panelMacroBox_Paint_1(object sender, PaintEventArgs e)
        {

        }

        private void richMacroBox_TextChanged(object sender, EventArgs e)
        {
            richMacroBox.SelectionAlignment = HorizontalAlignment.Center;
            btnPresetSave.Enabled = true;
        }

        private void btnMouseTracker_Click(object sender, EventArgs e)
        {
            if (btnMouseTracker.Text == "Enable Mouse Tracker")
            {
                lblInfoMT.Visible = true;
                lblMouseTrackerPosition.Visible = true;
                myEngineData.StartMouseTracker(richMacroBox, lblMouseTrackerPosition);
                btnMouseTracker.Text = "Disable Mouse Tracker";
                btnMouseTracker.ForeColor = Color.FromArgb(206, 45, 79);
            }
            else
            {
                lblInfoMT.Visible = false;
                lblMouseTrackerPosition.Visible = false;
                myEngineData.StopMouseTracker(btnMouseTracker);
                btnMouseTracker.Text = "Enable Mouse Tracker";
                btnMouseTracker.ForeColor = Color.Silver;
            }

        }






        private void label11_Click(object sender, EventArgs e)
        {

        }

        private void checkAutoHealth_CheckedChanged(object sender, EventArgs e)
        {
            if (checkAutoHealth.Checked)
            {
                checkAutoHealth.ForeColor = Color.MediumAquamarine;
                myAuto.StartAutoHealth(checkAutoHealth, Convert.ToInt32(healthBox.SelectedItem), trackBarHP, useSmartKeysChkBox.Checked);
                trackBarHP.Enabled = false;
                healthBox.Enabled = false;
                Console("[Auto] Started Auto Healing, When HP: " + trackBarHP.Value.ToString());

                if (btnStartBot.Enabled == false)
                    isUsingAutoHP = true;
            }
            else
            {
                if (btnStartBot.Enabled == false)
                    isUsingAutoHP = false;
                checkAutoHealth.ForeColor = Color.FromArgb(206, 45, 79);
                trackBarHP.Enabled = true;
                healthBox.Enabled = true;
                myAuto.StopAutoHealth();
                Console("[Auto] Stopped Auto Healing");
            }
        }

        private void checkAutoMana_CheckedChanged(object sender, EventArgs e)
        {
            if (checkAutoMana.Checked)
            {
                checkAutoMana.ForeColor = Color.MediumAquamarine;
                trackBarMP.Enabled = false;
                manaBox.Enabled = false;
                myAuto.StartAutoMana(checkAutoMana, Convert.ToInt32(manaBox.SelectedItem), trackBarMP, useSmartKeysChkBox.Checked);
                Console("[Auto] Started Auto Mana, When MP: " + trackBarMP.Value.ToString());
                if (btnStartBot.Enabled == false)
                    isUsingAutoMP = true;
            }
            else
            {
                if (btnStartBot.Enabled == false)
                    isUsingAutoMP = false;
                checkAutoMana.ForeColor = Color.FromArgb(206, 45, 79);
                trackBarMP.Enabled = true;
                manaBox.Enabled = true;
                myAuto.StopAutoMana();
                Console("[Auto] Stopped Auto Mana");
            }
        }

        private void checkAntiAFK_CheckedChanged(object sender, EventArgs e)
        {
            if (checkAntiAFK.Checked)
            {
                checkAntiAFK.ForeColor = Color.MediumAquamarine;
                myAuto.StartAntiAFK(checkAntiAFK, Convert.ToInt32(txtNormalAntiAfk.Value), Convert.ToInt32(mouseXNum.Value), Convert.ToInt32(mouseYNum.Value));
                Console("[Auto] Started Anti Ingame Window");
                if (btnStartBot.Enabled == false)
                    isUsingAutoAFK = true;
            }
            else
            {
                if (btnStartBot.Enabled == false)
                    isUsingAutoAFK = false;

                checkAntiAFK.ForeColor = Color.FromArgb(206, 45, 79);
                myAuto.StopAntiAFK();
                Console("[Auto] Stopped Anti Ingame Window");
            }
        }

        private void checkDeath_CheckedChanged(object sender, EventArgs e)
        {
            if (checkDeath.Checked)
            {
                checkDeath.ForeColor = Color.MediumAquamarine;
            }
            else
            {
                checkDeath.ForeColor = Color.FromArgb(206, 45, 79);
            }
        }

        private void checkTarget_CheckedChanged(object sender, EventArgs e)
        {
            if (checkTarget.Checked)
            {
                checkTarget.ForeColor = Color.MediumAquamarine;
            }
            else
            {
                checkTarget.ForeColor = Color.FromArgb(206, 45, 79);
            }
        }

        private void checkStopDead_CheckedChanged(object sender, EventArgs e)
        {
            if (checkStopDead.Checked)
            {
                //check Datatimer
                checkStopDead.ForeColor = Color.MediumAquamarine;
                Console("[Checker] Started - Stop macro bot when dead");

            }
            else
            {
                checkStopDead.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Checker] Stopped - Stop macro bot when dead");
            }
        }

        private void checkCloseDead_CheckedChanged(object sender, EventArgs e)
        {
            if (checkCloseDead.Checked)
            {
                checkCloseDead.ForeColor = Color.MediumAquamarine;
                Console("[Checker] Started - Close game when dead");
            }
            else
            {
                checkCloseDead.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Checker] Stopped - Close game when dead");
            }
        }

        private void checkAlertLowHP_CheckedChanged(object sender, EventArgs e)
        {
            if (checkAlertLowHP.Checked)
            {
                checkAlertLowHP.ForeColor = Color.MediumAquamarine;
                Console("[Checker] Started - Alert when low hp");
            }
            else
            {
                checkAlertLowHP.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Checker] Stopped - Alert when low hp");
            }
        }

        private void checkPlayerFinder_CheckedChanged(object sender, EventArgs e)
        {
            if (checkPlayerFinder.Checked)
            {
                checkPlayerFinder.ForeColor = Color.MediumAquamarine;
                myCheat.StartPlayerChecker(lblPlayerFound,checkPlayerFinder, txtPlayerNames.Text);
                Console("[Checker] Started Player Finder");
            }
            else
            {
                myCheat.StopPlayerChecker();
                checkPlayerFinder.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Checker] Stopped Player Finder");
            }
        }
        private void checkChangeVersion_CheckedChanged(object sender, EventArgs e)
        {
            if (checkTargetInfoToSys.Checked)
            {
                checkTargetInfoToSys.ForeColor = Color.MediumAquamarine;
                myCheat.StartTargetInfoToChat();
                Console("[Cheat] Started Target info to system chat");
            }
            else
            {
                checkTargetInfoToSys.ForeColor = Color.FromArgb(206, 45, 79);
                myCheat.StopTargetInfoToChat();
                Console("[Cheat] Stopped Target info to system chat");
            }
        }
        private void CheckLockTarget_CheckedChanged(object sender, EventArgs e)
        {
            if (checkLockTarget.Checked)
            {
                myCheat.StartLockTarget();
                checkLockTarget.ForeColor = Color.MediumAquamarine;
                Console("[Cheat] Started Target Lock");
            }
            else
            {
                myCheat.StopLockTarget();
                checkLockTarget.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Cheat] Stopped Target Lock");
            }
        }

        private void checkAttackSpeed_CheckedChanged(object sender, EventArgs e)
        {
            if (checkAttackSpeed.Checked)
            {
                checkAttackSpeed.ForeColor = Color.MediumAquamarine;
                myCheat.StartAtkSpeed();
                Console("[Cheat] Started Attack Speed Hack");
            }
            else
            {
                checkAttackSpeed.ForeColor = Color.FromArgb(206, 45, 79);
                myCheat.StopAtkSpeed();
                Console("[Cheat] Stopped Attack Speed Hack");
            }
        }

        private void checkInifityRush_CheckedChanged(object sender, EventArgs e)
        {
            if (checkInifityRush.Checked)
            {
                checkInifityRush.ForeColor = Color.MediumAquamarine;
                myCheat.InfiniyRush(1);
                Console("[Cheat] Started Infinity rush between targets");
            }
            else
            {
                myCheat.InfiniyRush(0);
                checkInifityRush.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Cheat] Stopped Infinity Rush");
            }
        }

        private void checkQuickSlot_CheckedChanged(object sender, EventArgs e)
        {
            if (checkQuickSlot.Checked)
            {
                checkQuickSlot.ForeColor = Color.MediumAquamarine;
                myCheat.ChangeQuickSlot(Convert.ToInt32(quickSlotPageNum.SelectedItem));
                Console("[Auto] Started Monitoring The Skill Bar");
            }
            else
            {
                myCheat.StopChangeQuickSlot();
                checkQuickSlot.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Auto] Stopped Monitoring The Skill Bar");
            }
        }
        private void btnCheckerTimer_Click(object sender, EventArgs e)
        {
            if (btnCheckerTimer.Text == "Start")
            {
                aliveTimer.Enabled = true;
                btnCheckerTimer.Text = "Reset";
                Console("[Checker] Started Alive Timer");

            }
            else
            {
                aliveTimer.Enabled = false;
                btnCheckerTimer.Text = "Start";
                myChecker.ResetAliveTimer();
                lblTimer.Text = "0:0:0";
                Console("[Checker] Stopped Alive Timer");
            }
        }

        private void aliveTimer_Tick(object sender, EventArgs e)
        {
            lblTimer.Text = myChecker.AliveTimer();
        }

        private void updateUIDataTimer_Tick(object sender, EventArgs e)
        {
            if (myEngineData.isRunning)
            {
                
                if(lblTitle.Text == "Cheats")
                {
                    updateCurrentStats();
                }

                if (lblName.Text != myEngineData.charName)
                {
                    lblName.Text = myEngineData.charName;
                }

                if (lblHealth.Text != myEngineData.curHP + "/" + myEngineData.maxHP)
                {
                    lblHealth.Text = myEngineData.curHP + "/" + myEngineData.maxHP;
                }

                if (lblMana.Text != myEngineData.curMP + "/" + myEngineData.maxMP)
                {
                    lblMana.Text = myEngineData.curMP + "/" + myEngineData.maxMP;
                }

                if (lblMap.Text != "X:"+myEngineData.curXMap + "|" + "Y:"+ myEngineData.curYMap)
                {
                    lblMap.Text = "X:" + myEngineData.curXMap + "|" + "Y:" + myEngineData.curYMap;
                }

                if (trackBarHP.Maximum != myEngineData.maxHP)
                {
                    try
                    {
                        trackBarHP.Maximum = myEngineData.maxHP;
                        trackBarHP.Value = Convert.ToInt32(trackBarHP.Maximum * 0.80);
                        lblAutoHP.Text = "When HP Reaches: " + trackBarHP.Value.ToString();
                    }
                    catch { }

                }
                    
                if (trackBarMP.Maximum != myEngineData.maxMP)
                {
                    try
                    {
                        trackBarMP.Maximum = myEngineData.maxMP;
                        trackBarMP.Value = Convert.ToInt32(trackBarMP.Maximum * 0.60);
                        lblAutoMP.Text = "When MP Reaches: " + trackBarMP.Value.ToString();
                    }
                    catch { }

                }


                if (checkSSDead.Checked && !btnStartBot.Enabled)
                {
                   
                    if (myChecker.isPlayerDead())
                    {
                        stopMacroBot();
                        aliveTimer.Enabled = false;
                        //<t:1747498758:R>

                        string epochTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
                        string deathTime = "<t:"+ epochTimestamp+":R>";
                        SendSSToDiscord(AppName, getDiscordName(curKey)+ "\\n 👤 Character: `" + myEngineData.charName + "` is DEAD \\n ⏰ Since " + deathTime + " \\n 🎯 Target Name: `" + myEngineData.targetName + "`");
                        Imports.PostMessage(myProcess.myCurInjectedHandle, myAddress.WM_KEYDOWN, myAddress.VK_F12, 0);


                        if (checkCloseDead.Checked) { 
                            new Thread(() => {
                            try
                            {
                                Console("[CLOSE WHEN DEAD] Closing the game after 30 seconds if the character is still dead");
                                Thread.Sleep(30000);
                                if (checkCloseDead.Checked & myChecker.isPlayerDead())
                                    myProcess.killEngine();
                            }
                            catch{}
                        }).Start();
                        }

                    }
                }

                if (checkStopDead.Checked && !btnStartBot.Enabled)
                {
                    if (myChecker.isPlayerDead())
                    {


                        stopMacroBot();
                        //Login.KeyAuthApp.log("Character: " + myEngineData.charName + " is DEAD 💀 RIP ..Target Name: " + myEngineData.targetName + " Window Name: " + txtWinName.Text);
                        aliveTimer.Enabled = false;
                        if (!checkSSDead.Checked)
                        {
                            string epochTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
                            string deathTime = "<t:" + epochTimestamp + ":R>";
                            SendToDiscord(AppName, getDiscordName(curKey) + "\\n 👤 Character: `" + myEngineData.charName + "` is DEAD \\n ⏰ Since " + deathTime + " \\n 🎯 Target Name: `" + myEngineData.targetName + "`");
                        }

                        if (checkCloseDead.Checked)
                        {
                            new Thread(() => {
                                try
                                {
                                    Console("[CLOSE WHEN DEAD] Closing the game after 30 seconds if the character is still dead");
                                    Thread.Sleep(30000);
                                    if (checkCloseDead.Checked & myChecker.isPlayerDead())
                                        myProcess.killEngine();
                                }
                                catch { }
                            }).Start();
                        }

                    }
                }

                if (checkCloseDead.Checked && !btnStartBot.Enabled)
                {
                    if (myChecker.isPlayerDead())
                    {
                        stopMacroBot();
                        aliveTimer.Enabled = false;
                            new Thread(() => {
                                try
                                {
                                    Console("[CLOSE WHEN DEAD] Closing the game after 30 seconds if the character is still dead");
                                    Thread.Sleep(30000);

                                    if (checkCloseDead.Checked & myChecker.isPlayerDead())
                                        myProcess.killEngine();

                                }
                                catch { }
                            }).Start();
                        


                    }
                }

                if (checkXY.Checked && !btnStartBot.Enabled)
                {

                    try {
                        string myX = myEngineData.curXMap.ToString();
                        string myY = myEngineData.curYMap.ToString();

                        if ((txtBoxMapX1.Text == myX && txtBoxMapY1.Text == myY) ||
                            (txtBoxMapX2.Text == myX && txtBoxMapY2.Text == myY) ||
                            (txtBoxMapX3.Text == myX && txtBoxMapY3.Text == myY) ||
                            (txtBoxMapX4.Text == myX && txtBoxMapY4.Text == myY)) 
                        {
                            stopMacroBot();
                            aliveTimer.Enabled = false;
                        }
                    } catch {  }
                    
                    
                }

                if (checkDeath.Checked)
                {
                    if (myChecker.isPlayerDead())
                    {
                        Console("[Checker] You are dead !");
                        myChecker.Alert();
                    }
                }

                if (checkAlertLowHP.Checked)
                {
                    if (myChecker.isPlayerLowHP())
                    {
                        Console("[Checker] Low HP !");
                        myChecker.Alert();
                    }
                }

                if (checkTarget.Checked)
                {
                    if (!myChecker.isSameTarget(txtTarget.Text))
                    {
                        Console("[Checker] Not Same Target !");
                        myChecker.Alert();
                    }
                }

                if (lblCurZoom.Text != "Current Zoom: " + myEngineData.myZoom.ToString())
                {
                    lblCurZoom.Text = "Current Zoom: " + myEngineData.myZoom.ToString();
                }


            }
            else
            {
                if (safeModeCheckBox.Checked) return;
                resetBot();
            }
        }
        public void updateCurrentStats()
        {
            try
            {
                var curStr = 0;
                var curHth = 0;
                var curInt = 0;
                var curWis = 0;
                var curAgi = 0;
                var curPoints = 0;
                if (myEngineData.newReader)
                {
                    var memory = myProcess.LinkMemory();
                    curStr = memory.Read<int>((IntPtr)myAddress.curstrAdd);
                    curHth = memory.Read<int>((IntPtr)myAddress.curhpAdd);
                    curInt = memory.Read<int>((IntPtr)myAddress.curintAdd);
                    curWis = memory.Read<int>((IntPtr)myAddress.curwisAdd);
                    curAgi = memory.Read<int>((IntPtr)myAddress.curagiAdd);
                    curPoints = memory.Read<int>((IntPtr)myAddress.curPointsAdd);
                }
                else
                {
                    var sharp = myProcess.LinkMemorySharp();
                    curStr = sharp.Read<int>((IntPtr)myAddress.curstrAdd, false);
                    curHth = sharp.Read<int>((IntPtr)myAddress.curhpAdd, false);
                    curInt = sharp.Read<int>((IntPtr)myAddress.curintAdd, false);
                    curWis = sharp.Read<int>((IntPtr)myAddress.curwisAdd, false);
                    curAgi = sharp.Read<int>((IntPtr)myAddress.curagiAdd, false);
                    curPoints = sharp.Read<int>((IntPtr)myAddress.curPointsAdd, false);
                }
                    

                lblCurStr.Text = "STR: " + curStr.ToString();
                lblCurHP.Text = "HP: " + curHth.ToString();
                lblCurInt.Text = "INT: " + curInt.ToString();
                lblCurWis.Text = "WIS: " + curWis.ToString();
                lblCurAgi.Text = "AGI: " + curAgi.ToString();
                lblCurPoints.Text = "Points Available: " + curPoints.ToString();
            } catch { }
        }
        private void checkTargetOverlay_CheckedChanged(object sender, EventArgs e)
        {
            if (myProcess.selectedProcess == 0 || myProcess.myCurInjectedHandle.Equals(IntPtr.Zero) && safeModeCheckBox.Checked == false)
            {
                MessageBox.Show("Engine is not running", "Heth's AIO");
                return;
            }

            if (checkTargetOverlay.Checked)
            {
                Overlay f2 = new Overlay(myProcess, myEngineData, comboEngineType.SelectedIndex);
                for (int x = 0; x < Application.OpenForms.Count; x++)
                {
                    if (Application.OpenForms[x].Name == "Overlay")
                        Application.OpenForms[x].Close();
                }
                f2.Show();
                Console("[Cheats] Opened Target Info Overlay");

                checkTargetOverlay.ForeColor = Color.MediumAquamarine;
            }
            else
            {
                checkTargetOverlay.ForeColor = Color.FromArgb(206, 45, 79);
                for (int x = 0; x < Application.OpenForms.Count; x++)
                {
                    if (Application.OpenForms[x].Name == "Overlay")
                        Application.OpenForms[x].Close();

                }
                Console("[Cheats] Closed Target Info Overlay");
            }
        }

        private void trackBarHP_Scroll(object sender, EventArgs e)
        {
            lblAutoHP.Text = "When HP Reaches: " + trackBarHP.Value.ToString();
        }

        private void trackBarMP_Scroll(object sender, EventArgs e)
        {
            lblAutoMP.Text = "When MP Reaches: " + trackBarMP.Value.ToString();
        }

        private void numZoom_ValueChanged(object sender, EventArgs e)
        {
            myEngineData.SetZoom(Convert.ToInt32(numZoom.Value));
        }

        private void btnOpenHTML_Click(object sender, EventArgs e)
        {
            myCheat.OpenHTMLWindow(txtOpenHTML.Text);
            Console("[Cheat] Opened " + txtOpenHTML.Text);
        }

        private void btnSaveQuickslot_Click(object sender, EventArgs e)
        {
            myCheat.SaveQuickslot();
            Console("[Cheat] Saved Quickslot !");
        }

        private void btnStatsAdd_Click(object sender, EventArgs e)
        {
            int Str = Convert.ToInt32(strNBox.Value);
            int Hp = Convert.ToInt32(hthNBox.Value);
            int Int = Convert.ToInt32(intNBox.Value);
            int Wis = Convert.ToInt32(wisNBox.Value);
            int Agi = Convert.ToInt32(agiNBox.Value);

            myCheat.UpdateStats(Str,Hp,Int,Wis,Agi);
            Console("[Cheat] Stats Added !");
        }

        private void checkPWW3rd_CheckedChanged(object sender, EventArgs e)
        {
            if (checkPWW3rd.Checked)
            {
                myCheat.StartPWW3rd();
                Console("[Cheats] knight skill skip to 3rd combo is ON");
                if (checkPWW2nd.Checked)
                {
                    checkPWW2nd.Checked = false;
                }
                checkPWW3rd.ForeColor = Color.MediumAquamarine;
            }
            else
            {
                myCheat.StopPWW3rd();
                Console("[Cheats] knight skill skip to 3rd combo is OFF");
                checkPWW3rd.ForeColor = Color.FromArgb(206, 45, 79);
            }
            
        }

        private void checkPWW2nd_CheckedChanged(object sender, EventArgs e)
        {
            if (checkPWW2nd.Checked)
            {
                myCheat.StartPWW2nd();
                Console("[Cheats] knight skill skip to 2nd combo is ON");
                if (checkPWW3rd.Checked)
                {
                    checkPWW3rd.Checked = false;
                }
                checkPWW2nd.ForeColor = Color.MediumAquamarine;
            }
            else
            {
                myCheat.StopPWW2nd();
                Console("[Cheats] knight skill skip to 2nd combo is OFF");
                checkPWW2nd.ForeColor = Color.FromArgb(206, 45, 79);
            }
            
        }

        private void comboTargetRange_SelectedIndexChanged(object sender, EventArgs e)
        {
            myCheat.ChangeTabTargetRange(comboTargetRange.SelectedIndex);
        }

        private void comboPresets_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (comboPresets.SelectedIndex == 0)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtMacroName.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.macroBox.ToString();

            }
            else if (comboPresets.SelectedIndex == 1)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset1.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset1.ToString();

            }
            else if (comboPresets.SelectedIndex == 2)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset2.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset2.ToString();

            }
            else if (comboPresets.SelectedIndex == 3)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset3.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset3.ToString();
            }
            else if (comboPresets.SelectedIndex == 4)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset4.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset4.ToString();
            }
            else if (comboPresets.SelectedIndex == 5)
            {
                txtBoxPreset.Text = Heth_AIO.Properties.Settings.Default.txtPreset5.ToString();
                richMacroBox.Text = Heth_AIO.Properties.Settings.Default.mPreset5.ToString();
            }


        }

        private void btnPresetSave_Click(object sender, EventArgs e)
        {
            
            if (comboPresets.SelectedIndex == 0)
            {

                Heth_AIO.Properties.Settings.Default.txtMacroName = txtBoxPreset.Text;
                Heth_AIO.Properties.Settings.Default.macroBox = richMacroBox.Text;
                comboPresets.Items[0] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 1)
            {
                Heth_AIO.Properties.Settings.Default.mPreset1 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset1 = txtBoxPreset.Text;
                comboPresets.Items[1] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 2)
            {
                Heth_AIO.Properties.Settings.Default.mPreset2 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset2 = txtBoxPreset.Text;
                comboPresets.Items[2] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 3)
            {
                Heth_AIO.Properties.Settings.Default.mPreset3 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset3 = txtBoxPreset.Text;
                comboPresets.Items[3] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 4)
            {
                Heth_AIO.Properties.Settings.Default.mPreset4 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset4 = txtBoxPreset.Text;
                comboPresets.Items[4] = txtBoxPreset.Text;
            }
            else if (comboPresets.SelectedIndex == 5)
            {
                Heth_AIO.Properties.Settings.Default.mPreset5 = richMacroBox.Text;
                Heth_AIO.Properties.Settings.Default.txtPreset5 = txtBoxPreset.Text;
                comboPresets.Items[5] = txtBoxPreset.Text;
            }
            Heth_AIO.Properties.Settings.Default.Save();
            btnPresetSave.Enabled = false;
        }

        private void txtBoxPreset_TextChanged(object sender, EventArgs e)
        {
            btnPresetSave.Enabled = true;
        }

        private void workerKeyboardMonitor_DoWork(object sender, DoWorkEventArgs e)
        {
            isMonitoringKeyboard = true;
            while (isMonitoringKeyboard)
            {


                if (hotkeys.Checked && (Imports.GetAsyncKeyState(Keys.F6) & 0x8000) != 0)
                {
                    //this.Invoke((MethodInvoker)delegate ()
                    //{
                     //   resetBot();
                     //   stopMacroBot();
                    //});


                }
                else if (hotkeys.Checked && (Imports.GetAsyncKeyState(Keys.F5) & 0x8000) != 0)
                {
                    this.Invoke((MethodInvoker)async delegate ()
                    {
                        await injectBot();
                    });
                }
                else if (hotkeys.Checked && (Imports.GetAsyncKeyState(Keys.F2) & 0x8000) != 0)
                {
                    this.Invoke((MethodInvoker)delegate ()
                    {
                    startMacroBot();
                    });
                }
                else if (hotkeys.Checked && (Imports.GetAsyncKeyState(Keys.F3) & 0x8000) != 0)
                {
                    this.Invoke((MethodInvoker)delegate ()
                    {
                    stopMacroBot();
                    });
                }
                else if ((Imports.GetAsyncKeyState(Keys.Tab) & 0x8000) != 0 && checkTabTarget.Checked && !txtWinName.Enabled)
                {
                    if (safeModeCheckBox.Checked) return;

                    this.Invoke((MethodInvoker)delegate ()
                    {
                        myMacro.NextTarget();
                    });
                }
                Thread.Sleep(70);
            }
        }

        private void btnImport_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog theDialog = new OpenFileDialog();
                theDialog.Title = "Open Text File";
                theDialog.Filter = "TXT files|*.txt";
                if (theDialog.ShowDialog() == DialogResult.OK)
                {
                    string filename = theDialog.FileName;
                    string[] filelines = File.ReadAllLines(filename);
                    foreach (string line in filelines)
                    {
                        richMacroBox.AppendText(Environment.NewLine + line);
                    }

                }
            }
            catch
            {

            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveFileDialog1 = new SaveFileDialog();
                saveFileDialog1.Filter = "txt files (*.txt)|*.txt|All files (*.*)|*.*";
                saveFileDialog1.RestoreDirectory = true;

                if (saveFileDialog1.ShowDialog() == DialogResult.OK)
                {
                    File.WriteAllText(saveFileDialog1.FileName, richMacroBox.Text);
                }
            }
            catch
            {

            }
        }

        private void btnMacroHelpCMD_Click(object sender, EventArgs e)
        {
            MacroCommandsForm f2 = new MacroCommandsForm();
            f2.Show();
        }

        private void btnNewMacro_Click(object sender, EventArgs e)
        {

            if (Application.OpenForms["Extra1"] == null)
            {
                ExtraMacroForm Extra1 = new ExtraMacroForm(myProcess, myEngineData, myAddress, myMacro, txtWinName.Text,"Extra Macro 1",safeModeCheckBox.Checked);
                Extra1.Name = "Extra1";
                Extra1.Show();
                myExtraMacro = Extra1;
            }
            else if(Application.OpenForms["Extra2"] == null)
            {
                ExtraMacroForm Extra2 = new ExtraMacroForm(myProcess, myEngineData, myAddress, myMacro, txtWinName.Text, "Extra Macro 2", safeModeCheckBox.Checked);
                Extra2.Name = "Extra2";
                Extra2.Show();
                myExtraMacro2 = Extra2;
            }
            else if (Application.OpenForms["Extra3"] == null)
            {
                ExtraMacroForm Extra3 = new ExtraMacroForm(myProcess, myEngineData, myAddress, myMacro, txtWinName.Text, "Extra Macro 3", safeModeCheckBox.Checked);
                Extra3.Name = "Extra3";
                Extra3.Show();
                myExtraMacro3 = Extra3;
            }
            else if (Application.OpenForms["Extra4"] == null)
            {
                ExtraMacroForm Extra4 = new ExtraMacroForm(myProcess, myEngineData, myAddress, myMacro, txtWinName.Text, "Extra Macro 4", safeModeCheckBox.Checked);
                Extra4.Name = "Extra4";
                Extra4.Show();
                myExtraMacro4 = Extra4;
            }
            else if (Application.OpenForms["Extra5"] == null)
            {
                ExtraMacroForm Extra5 = new ExtraMacroForm(myProcess, myEngineData, myAddress, myMacro, txtWinName.Text, "Extra Macro 5", safeModeCheckBox.Checked);
                Extra5.Name = "Extra5";
                Extra5.Show();
                myExtraMacro5 = Extra5;
            }
            else
            {
                MessageBox.Show("You can open 5 extra macros only");
            }
            sysIsWorking();

        }

        private async void lblUpdateLink_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Process.Start(await Login.KeyAuthApp.var("updateurl"));
        }

        private async void label1_Click(object sender, EventArgs e)
        {
            await Login.KeyAuthApp.fetchStats();
            MessageBox.Show("\nApp data:\nNumber of users:"+ Login.KeyAuthApp.app_data.numUsers  + "\nNumber of online users: "+ Login.KeyAuthApp.app_data.numOnlineUsers + "\nNumber of keys:"+ Login.KeyAuthApp.app_data.numKeys + "\nMade by Heth#5180");
        }

        private void checkDiscordDead_CheckedChanged(object sender, EventArgs e)
        {
            if (checkSSDead.Checked)
            {

                checkSSDead.ForeColor = Color.MediumAquamarine;
                Console("[Checker] Started - Screenshot when dead");

            }
            else
            {
                checkSSDead.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Checker] Stopped - Screenshot when dead");
            }
        }

        private void checkXY_CheckedChanged(object sender, EventArgs e)
        {
            if (checkXY.Checked)
            {

                checkXY.ForeColor = Color.MediumAquamarine;
                Console("[Checker] Started - Stop macro when in X Y");

            }
            else
            {
                checkXY.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Checker] Stopped - Stop macro when in X Y");
            }
        }

        private void button1_Click_1(object sender, EventArgs e)
        {

            //if (myProcess.selectedProcess == 0) return;
            //myCheat.inGameChat("-> [Heth AIO] crash or not?");
           /* var sharp = new MemorySharp(myProcess.GetEngineProcess());
            int curChar = sharp.Read<int>((IntPtr)myAddress.gameCharacterAdd, false);
            if (curChar != 0)
            {
                float bruh = sharp.Read<float>((IntPtr)curChar + 0x4C5C, false);
                MessageBox.Show(bruh.ToString());
                //sharp.Write<float>((IntPtr)curChar + 0x4C5C,0,false);


                *//*                int mapV1 = sharp.Read<int>((IntPtr)curChar + myAddress.GCMapxOffset, false);
                                int mapV2 = sharp.Read<int>((IntPtr)curChar + myAddress.GCMapyOffset, false);
                                int doneMapV1 = mapV1 / 0x2000;
                                int doneMapV2 = mapV2 / 0x2000;
                                MessageBox.Show("map1: " + doneMapV1.ToString() + " map2: " + doneMapV2.ToString());*//*
            }*/
            //SendSSToDiscord("Bitch","Bittch");
            //Imports.CaptureWindow(myProcess.myCurInjectedHandle);
            //Imports.SetForegroundWindow(myProcess.myCurInjectedHandle);
            //Imports.MoveWindow(myProcess.myCurInjectedHandle, 0, 0, 0, 0, false);
            //Imports.SetWindowPos(myProcess.myCurInjectedHandle, 0, 0, 0, 0, 0, 0x0001 | 0x0040);

            //SendToDiscord("bla", getDiscordName(curKey) + " test");
            //myProcess.killEngine();
            // Imports.PostMessage(myProcess.myCurInjectedHandle, myAddress.WM_KEYDOWN, myAddress.VK_F12, 0);
        }

        private void PauseCheckTimer_Tick(object sender, EventArgs e)
        {
/*            if (btnStartBot.Enabled || runLoop == true) return;

            if (myExtraMacro != null && myExtraMacro.pauseMacro == true && myExtraMacro.pauseMacroWaitNow == false && myExtraMacro.MacroStarted)
            {
                botter();
            }
            else if (myExtraMacro2 != null && myExtraMacro2.pauseMacro == true && myExtraMacro2.pauseMacroWaitNow == false && myExtraMacro2.MacroStarted)
            {
                botter();
            }
            else if (myExtraMacro3 != null && myExtraMacro3.pauseMacro == true && myExtraMacro3.pauseMacroWaitNow == false && myExtraMacro3.MacroStarted)
            {
                botter();
            }
            else if (myExtraMacro4 != null && myExtraMacro4.pauseMacro == true && myExtraMacro4.pauseMacroWaitNow == false && myExtraMacro4.MacroStarted)
            {
                botter();
            }
            else if (myExtraMacro5 != null && myExtraMacro5.pauseMacro == true && myExtraMacro5.pauseMacroWaitNow == false && myExtraMacro5.MacroStarted)
            {
                botter();
            }*/
        }

        private void richMacroCtrlBox_TextChanged(object sender, EventArgs e)
        {
            richMacroCtrlBox.SelectionAlignment = HorizontalAlignment.Center;
        }

        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            if (checkTabTarget.Checked)
            {

                checkTabTarget.ForeColor = Color.MediumAquamarine;
                Console("[Cheat] Enabled Tab to Target");

            }
            else
            {
                checkTabTarget.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Cheat] Disabled Tab to Target");
            }
        }

        private void quickSlotPageNum_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void checkAnyPlayerFinder_CheckedChanged(object sender, EventArgs e)
        {
            if (checkAnyPlayerFinder.Checked)
            {

                checkAnyPlayerFinder.ForeColor = Color.MediumAquamarine;
                Console("[Cheat] Enabled Any Player Finder");
                myCheat.StartAnyPlayerChecker();

            }
            else
            {
                checkAnyPlayerFinder.ForeColor = Color.FromArgb(206, 45, 79);
                Console("[Cheat] Disabled Any Player Finder");
                myCheat.StopAnyPlayerChecker();
            }
        }

        private void hotkeys_CheckedChanged(object sender, EventArgs e)
        {
            if (hotkeys.Checked)
            {
                hotkeys.ForeColor = Color.MediumAquamarine;
            }
            else
            {
                hotkeys.ForeColor = Color.FromArgb(206, 45, 79);
            }
        }

        private void comboEngineType_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void checkTest_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void checkBlockTeleport_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBlockTeleport.Checked)
            {
                checkHideAll.Checked = false;
                checkBlockHide.Checked = false;


                checkBlockTeleport.ForeColor = Color.MediumAquamarine;
                Console("[Cheat] Enabled Block Teleport");
                myCheat.ControlCharacterState(1);

            }
            else
            {
                checkBlockTeleport.ForeColor = Color.FromArgb(206, 45, 79);
                //Console("[Cheat] Disabled Block Teleport");
                if (checkBlockTeleport.Checked == false && checkHideAll.Checked == false && checkBlockHide.Checked == false)
                {
                    myCheat.ControlCharacterState(0);
                    Console("[Cheat] Disabled All Hiding and Blocking Teleports");
                }

            }

        }

        private void checkHideAll_CheckedChanged(object sender, EventArgs e)
        {
            if (checkHideAll.Checked)
            {
                checkBlockTeleport.Checked = false;
                checkBlockHide.Checked = false;


                checkHideAll.ForeColor = Color.MediumAquamarine;
                Console("[Cheat] Enabled Hide Mobs,Npcs and Players");
                myCheat.ControlCharacterState(2);

            }
            else
            {
                checkHideAll.ForeColor = Color.FromArgb(206, 45, 79);
                if (checkBlockTeleport.Checked == false && checkHideAll.Checked == false && checkBlockHide.Checked == false)
                {
                    myCheat.ControlCharacterState(0);
                    Console("[Cheat] Disabled All Hiding and Blocking Teleports");
                }
            }
        }

        private void checkBlockHide_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBlockHide.Checked)
            {
                checkBlockTeleport.Checked = false;
                checkHideAll.Checked = false;


                checkBlockHide.ForeColor = Color.MediumAquamarine;
                Console("[Cheat] Enabled Block Teleport and Hiding Everything");
                myCheat.ControlCharacterState(3);

            }
            else
            {
                checkBlockHide.ForeColor = Color.FromArgb(206, 45, 79);
                if (checkBlockTeleport.Checked == false && checkHideAll.Checked == false && checkBlockHide.Checked == false)
                {
                    myCheat.ControlCharacterState(0);
                    Console("[Cheat] Disabled All Hiding and Blocking Teleports");
                }
            }
        }

        private void checkBox1_CheckedChanged_1(object sender, EventArgs e)
        {
            if (safeModeCheckBox.Checked)
            {
                safeModeCheckBox.ForeColor = Color.MediumAquamarine;
                NewReaderCheckBox.Checked = false;
            }
            else
            {
                safeModeCheckBox.ForeColor = Color.FromArgb(206, 45, 79);
            }
        }

        private void NewReaderCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            if (NewReaderCheckBox.Checked)
            {
                NewReaderCheckBox.ForeColor = Color.MediumAquamarine;
                safeModeCheckBox.Checked = false;
            }
            else
            {
                NewReaderCheckBox.ForeColor = Color.FromArgb(206, 45, 79);
            }
        }

        private void useSmartKeysChkBox_CheckedChanged(object sender, EventArgs e)
        {
            
            if (useSmartKeysChkBox.Checked)
            {
                useSmartKeysChkBox.ForeColor = Color.MediumAquamarine;
            }
            else
            {
                useSmartKeysChkBox.ForeColor = Color.FromArgb(206, 45, 79);
            }
        }
    }
}
