﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Heth_AIO
{
    public partial class MacroCommandsForm : Form
    {
        [DllImport("Gdi32.dll", EntryPoint = "DeleteObject")]
        public static extern bool DeleteObject(IntPtr hObject);
        //corner edges
        [DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn
            (
            int nLeftRect,     // x-coordinate of upper-left corner
            int nTopRect,      // y-coordinate of upper-left corner
            int nRightRect,    // x-coordinate of lower-right corner
            int nBottomRect,   // y-coordinate of lower-right corner
            int nWidthEllipse, // width of ellipse
            int nHeightEllipse // height of ellipse
              );

        private bool mouseDown;
        private Point lastLocation;
        public MacroCommandsForm()
        {
            InitializeComponent();

            this.FormBorderStyle = FormBorderStyle.None;
            IntPtr handle = CreateRoundRectRgn(0, 0, Width, Height, 20, 20);
            if (handle == IntPtr.Zero)
#pragma warning disable CS0642 // Possible mistaken empty statement
                ; // error with CreateRoundRectRgn
#pragma warning restore CS0642 // Possible mistaken empty statement
            Region = System.Drawing.Region.FromHrgn(handle);
            DeleteObject(handle);
        }

        public void DrawRoundRect(Panel panel)
        {
            IntPtr handle = CreateRoundRectRgn(0, 0, panel.Width, panel.Height, 15, 15);

            if (handle == IntPtr.Zero)
#pragma warning disable CS0642 // Possible mistaken empty statement
                ; // error with CreateRoundRectRgn
#pragma warning restore CS0642 // Possible mistaken empty statement
            panel.Region = System.Drawing.Region.FromHrgn(handle);
            DeleteObject(handle);
        }

        private void MacroCommandsForm_Load(object sender, EventArgs e)
        {
            DrawRoundRect(panel1);
            DrawRoundRect(panel2);
            DrawRoundRect(panel3);
        }

        private void extBTN_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void minBTN_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void MacroCommandsForm_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void MacroCommandsForm_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }

        private void MacroCommandsForm_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }

        private void lblRunInfo_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void lblRunInfo_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }

        private void lblRunInfo_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }

        private void textBox6_TextChanged(object sender, EventArgs e)
        {

        }

        private void textBox21_TextChanged(object sender, EventArgs e)
        {

        }
    }
}
