﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Heth_AIO.myClasses
{

    
    class AutoCMD
    {
        EngineData myEngineData;
        ProcessCMD myProcess;
        public bool isAntiAFKRunning = false;
        public bool isAutoHealth = false;
        public bool isAutoMana = false;
        public AutoCMD(EngineData myED, ProcessCMD myP)
        {
            myEngineData = myED;
            myProcess = myP;
        }
        public void StopThreads()
        {
         isAntiAFKRunning = false;
         isAutoHealth = false;
         isAutoMana = false;
        }
        private bool healthCheck(int diff)
        {
            return myEngineData.curHP < diff;
        }
        private bool manaCheck(int diff)
        {
            return myEngineData.curMP < diff;
        }

        public void StopAutoHealth()
        {
            isAutoHealth = false;
        }
        public void StopAutoMana()
        {
            isAutoMana = false;
        }

        public void StartAutoHealth(CheckBox chBox,int key, TrackBar trackBar,bool smartKeyChkBox)
        {
            isAutoHealth = true;
            new Thread(() =>
            {
                while (isAutoHealth)
                {
                    int diff = 0;
                    if (trackBar.InvokeRequired)
                    {
                        trackBar.Invoke((MethodInvoker)(delegate {
                            diff = trackBar.Value;
                        }));
                    }
                    if (healthCheck(diff))
                    {
                        bool isOK = false;
                        if (smartKeyChkBox)
                        {
                            isOK = myEngineData.SmartKeyPress(key);
                        }
                        else
                        {
                            isOK = myEngineData.KeyPress(key);
                        }
                        if (!isOK)
                        {
                            if (chBox.InvokeRequired)
                            {
                                chBox.Invoke((MethodInvoker)(delegate
                                {
                                    chBox.Checked = false;
                                }));
                            }
                            isAutoHealth = false;
                        }
                    }
                    Thread.Sleep(700);
                }
            }).Start();

        }

        public void StartAutoMana(CheckBox chBox, int key, TrackBar trackBar,bool smartKeyChkBox)
        {
            isAutoMana = true;
            new Thread(() =>
            {
                while (isAutoMana)
                {
                    int diff = 0;
                    if (trackBar.InvokeRequired)
                    {
                        trackBar.Invoke((MethodInvoker)(delegate {
                            diff = trackBar.Value;
                        }));
                    }
                    if (manaCheck(diff))
                    {
                        bool isOK = false;
                        if (smartKeyChkBox)
                        {
                             isOK = myEngineData.SmartKeyPress(key);
                        }
                        else
                        {
                             isOK = myEngineData.KeyPress(key);
                        }

                        if (!isOK)
                        {
                            if (chBox.InvokeRequired)
                            {
                                chBox.Invoke((MethodInvoker)(delegate
                                {
                                    chBox.Checked = false;
                                }));
                            }
                            isAutoMana = false;
                        }
                        
                    }
                    Thread.Sleep(900);
                }


            }).Start();

        }

        public void StartAntiAFK(CheckBox chBox, int cd, int x, int y)
        {
            IntPtr handle = myProcess.myCurInjectedHandle;
                isAntiAFKRunning = true;

            new Thread(() =>
                {
                    while (isAntiAFKRunning)
                    {
                        bool isOK = myEngineData.RightMouseClick(x, y);
                        if (!isOK)
                        {
                            if (chBox.InvokeRequired)
                            {
                                chBox.Invoke((MethodInvoker)(delegate {
                                    chBox.Checked = false;
                                }));
                            }
                            isAntiAFKRunning = false;
                        }

                        Thread.Sleep(cd);
                    }
                }).Start();
        }
        public void StopAntiAFK()
        {
            isAntiAFKRunning = false;
        }



    }
}
