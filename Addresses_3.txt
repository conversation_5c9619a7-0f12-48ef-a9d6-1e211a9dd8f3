SelectionName="SoonEngineV3 (Online)";
engineType=5;
gameCharacterAdd=0x00906824;
GCMapxOffset=0x473C;
GCMapyOffset=0x4744;
nameAdd=0x009EFA44;
guildAdd=0x00000000;
moneyAdd=0x00000000;
targetAdd=0x009F10F8;
HealthX=0x009EFC38;
HealthY=0x009068B8;
HealthAddresse=0x009EF95C;
MaxManaAddresse=0x009EF960;
CurrManaAddresse=0x009EF958;
tapTargetAdd=0x00433870;
curTargetAddress=0x00906850;
useSkillAdd=0x0042E360;
isMonsterAlive=0x0090682C;
tabRange=0x009068AC;
isDropNearAdd=0x008D1204;
send_itemAdd=0x00432570;
targetKindAdd=0x008F40F0;
checkObjectCrashAdd=0x006DF580;
zoomAdd=0x008A18BC;
SetTargetByIdAdd=0x00714C10;
InitReactBattlebyStartAdd=0x009F1561;
InitReactBattlebLockAdd=0x009F1588;
MonsterMinHPOffset=0x47B8;
MonsterMaxHPOffset=0x47BC;
MonsterIDOffset=0x4E1C;
NormalAtkFixerAdd=0x008F40F0;
AtkSpeedAdd=0x00906830;
rushTimeAdd=0x00000000;
cameraRotateAdd=0x00914BD8;
curQuickslot=0x009F4138;
changeQuickslotAdd=0x006E65D0;
saveQuickslotAdd=0x006E4E30;
openHtmlAdd=0x006DACC0;
findNameNearAdd=0x004286F0;
rotateCamAngleAdd=0x00914BD8;
selectPlayerAdd=0x006DE650;
chattingClearAdd=0x00000000;
addChatMessageAdd=0x006E72C0;
addInfoMessageAdd=0x006E72F0;
comboSkillAdd=0x0090682E;
playerAroundAdd=0x009EE4E8;
strAdd=0x009DBE60;
hpAdd=0x009DBE62;
intAdd=0x009DBE64;
wisAdd=0x009DBE66;
agiAdd=0x009DBE68;
curstrAdd=0x009EF798;
curhpAdd=0x009EF79C;
curintAdd=0x009EF7A0;
curwisAdd=0x009EF7A4;
curagiAdd=0x009EF7A8;
curPointsAdd=0x009EF7EC;
updateStatsAdd=0x0063AE80;
updateStats2Add=0x0063BD20;
m_fWeaponDelayOffset = 0x4CBC;
targetIDOffset = 0x4444;
selUIDAdd = 0x006E3F00;
sendAttackAdd = 0x006DF6D0;