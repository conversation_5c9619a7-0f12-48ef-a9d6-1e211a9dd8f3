using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace Heth_AIO
{
    public class Addresses
    {
        public UInt32 WM_KEYDOWN = 0x0100;
        public UInt32 WM_KEYUP = 0x0101;
        public int VK_RBUTTON = 0x02;
        public int VK_RETURN = 0x0D;
        public int VK_ESCAPE = 0x1B;
        public int VK_KEY_0 = 0x30;
        public int VK_KEY_1 = 0x31;
        public int VK_KEY_2 = 0x32;
        public int VK_KEY_3 = 0x33;
        public int VK_KEY_4 = 0x34;
        public int VK_KEY_5 = 0x35;
        public int VK_KEY_6 = 0x36;
        public int VK_KEY_7 = 0x37;
        public int VK_KEY_8 = 0x38;
        public int VK_KEY_9 = 0x39;
        public int VK_KEY_A = 0x41;
        public int VK_KEY_B = 0x42;
        public int VK_KEY_C = 0x43;
        public int VK_KEY_D = 0x44;
        public int VK_KEY_E = 0x45;
        public int VK_KEY_F = 0x46;
        public int VK_KEY_G = 0x47;
        public int VK_KEY_H = 0x48;
        public int VK_KEY_I = 0x49;
        public int VK_KEY_J = 0x4A;
        public int VK_KEY_K = 0x4B;
        public int VK_KEY_L = 0x4C;
        public int VK_KEY_M = 0x4D;
        public int VK_KEY_N = 0x4E;
        public int VK_KEY_O = 0x4F;
        public int VK_KEY_P = 0x50;
        public int VK_KEY_Q = 0x51;
        public int VK_KEY_R = 0x52;
        public int VK_KEY_S = 0x53;
        public int VK_KEY_T = 0x54;
        public int VK_KEY_U = 0x55;
        public int VK_KEY_V = 0x56;
        public int VK_KEY_W = 0x57;
        public int VK_KEY_X = 0x58;
        public int VK_KEY_Y = 0x59;
        public int VK_KEY_Z = 0x5A;

        public int VK_F1 = 0x70;
        public int VK_F2 = 0x71;
        public int VK_F3 = 0x72;
        public int VK_F4 = 0x73;
        public int VK_F5 = 0x74;
        public int VK_F6 = 0x75;
        public int VK_F7 = 0x76;
        public int VK_F8 = 0x77;
        public int VK_F9 = 0x78;
        public int VK_F10 = 0x79;
        public int VK_F11 = 0x7A;
        public int VK_F12 = 0x7B;

        public int VK_TAB = 0x09;
        public int VK_SPACE = 0x20;


        public int gameCharacterAdd = 0;
        public int GCMapxOffset = 0;
        public int GCMapyOffset = 0;
        public int playerStateOffset = 0;
        public int nameAdd = 0;
        public int guildAdd = 0;
        public int moneyAdd = 0;
        public int targetAdd = 0;
        public int HealthX = 0;
        public int HealthY = 0;
        public int HealthAddresse = 0;
        public int MaxManaAddresse = 0;
        public int CurrManaAddresse = 0;
        public int curTargetAddress = 0;
        public int tapTargetAdd = 0;
        public int useSkillAdd = 0;
        public int isMonsterAlive = 0;
        public int tabRange = 0;
        public int isDropNearAdd = 0;
        public int targetKindAdd = 0;
        public int send_itemAdd = 0;
        public int checkObjectCrashAdd = 0;
        public int zoomAdd = 0;
        public int SetTargetByIdAdd = 0;
        public int InitReactBattlebyStartAdd = 0;
        public int InitReactBattlebLockAdd = 0;
        public int MonsterMinHPOffset = 0x475C;
        public int MonsterMaxHPOffset = 0x4760;
        public int MonsterIDOffset = 0x4DBC;
        public int NormalAtkFixerAdd = 0;
        public int AtkSpeedAdd = 0;
        public int rushTimeAdd = 0;
        public int cameraRotateAdd = 0;
        public int curQuickslot = 0;
        public int changeQuickslotAdd = 0;
        public int openHtmlAdd = 0;
        public int findNameNearAdd = 0;
        public int rotateCamAngleAdd = 0;
        public int selectPlayerAdd = 0;
        public int playerAroundAdd = 0;
        public int saveQuickslotAdd = 0;
        public int addChatMessageAdd = 0;
        public int addInfoMessageAdd = 0;
        public int comboSkillAdd = 0;
        //points
        public int updateStatsAdd = 0;
        public int updateStats2Add = 0;
        public int strAdd = 0;
        public int hpAdd = 0;
        public int intAdd = 0;
        public int wisAdd = 0;
        public int agiAdd = 0;
        public int curstrAdd = 0;
        public int curhpAdd = 0;
        public int curintAdd = 0;
        public int curwisAdd = 0;
        public int curagiAdd = 0;
        public int curPointsAdd = 0;
        public int chattingClearAdd = 0;
        public int engineType = 1000;
        public int m_fWeaponDelayOffset = 0;
        public int selUIDAdd = 0; // KGameSys::SelUID(int uid, int kind, int action) - so TargetID,1,0
        public int targetIDOffset = 0x4444;
        public int sendAttackAdd = 0;
        public string SelectionName = "";

        public async Task<string> FetchPastebinData(string url)
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    string response = await client.GetStringAsync(url);
                    return response;
                }
            }
            catch (Exception ex)
            {
                // Handle error - could log or return empty string
                return "";
            }
        }

        public void ParsePastebinData(string data)
        {
            if (string.IsNullOrEmpty(data))
                return;

            string[] lines = data.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (string line in lines)
            {
                if (line.Contains("="))
                {
                    string[] parts = line.Split('=');
                    if (parts.Length == 2)
                    {
                        string varName = parts[0].Trim();
                        string varValue = parts[1].Trim().TrimEnd(';');

                        // Remove quotes from string values
                        if (varValue.StartsWith("\"") && varValue.EndsWith("\""))
                        {
                            varValue = varValue.Substring(1, varValue.Length - 2);
                        }

                        // Parse and assign values based on variable name
                        switch (varName)
                        {
                            case "SelectionName":
                                SelectionName = varValue;
                                break;
                            case "engineType":
                                if (int.TryParse(varValue, out int engineTypeVal))
                                    engineType = engineTypeVal;
                                break;
                            case "gameCharacterAdd":
                                if (varValue.StartsWith("0x"))
                                    gameCharacterAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "GCMapxOffset":
                                if (varValue.StartsWith("0x"))
                                    GCMapxOffset = Convert.ToInt32(varValue, 16);
                                break;
                            case "GCMapyOffset":
                                if (varValue.StartsWith("0x"))
                                    GCMapyOffset = Convert.ToInt32(varValue, 16);
                                break;
                            case "playerStateOffset":
                                if (varValue.StartsWith("0x"))
                                    playerStateOffset = Convert.ToInt32(varValue, 16);
                                break;
                            case "nameAdd":
                                if (varValue.StartsWith("0x"))
                                    nameAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "guildAdd":
                                if (varValue.StartsWith("0x"))
                                    guildAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "moneyAdd":
                                if (varValue.StartsWith("0x"))
                                    moneyAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "targetAdd":
                                if (varValue.StartsWith("0x"))
                                    targetAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "HealthX":
                                if (varValue.StartsWith("0x"))
                                    HealthX = Convert.ToInt32(varValue, 16);
                                break;
                            case "HealthY":
                                if (varValue.StartsWith("0x"))
                                    HealthY = Convert.ToInt32(varValue, 16);
                                break;
                            case "HealthAddresse":
                                if (varValue.StartsWith("0x"))
                                    HealthAddresse = Convert.ToInt32(varValue, 16);
                                break;
                            case "MaxManaAddresse":
                                if (varValue.StartsWith("0x"))
                                    MaxManaAddresse = Convert.ToInt32(varValue, 16);
                                break;
                            case "CurrManaAddresse":
                                if (varValue.StartsWith("0x"))
                                    CurrManaAddresse = Convert.ToInt32(varValue, 16);
                                break;
                            case "tapTargetAdd":
                                if (varValue.StartsWith("0x"))
                                    tapTargetAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "curTargetAddress":
                                if (varValue.StartsWith("0x"))
                                    curTargetAddress = Convert.ToInt32(varValue, 16);
                                break;
                            case "useSkillAdd":
                                if (varValue.StartsWith("0x"))
                                    useSkillAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "isMonsterAlive":
                                if (varValue.StartsWith("0x"))
                                    isMonsterAlive = Convert.ToInt32(varValue, 16);
                                break;
                            case "tabRange":
                                if (varValue.StartsWith("0x"))
                                    tabRange = Convert.ToInt32(varValue, 16);
                                break;
                            case "isDropNearAdd":
                                if (varValue.StartsWith("0x"))
                                    isDropNearAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "send_itemAdd":
                                if (varValue.StartsWith("0x"))
                                    send_itemAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "targetKindAdd":
                                if (varValue.StartsWith("0x"))
                                    targetKindAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "checkObjectCrashAdd":
                                if (varValue.StartsWith("0x"))
                                    checkObjectCrashAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "zoomAdd":
                                if (varValue.StartsWith("0x"))
                                    zoomAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "SetTargetByIdAdd":
                                if (varValue.StartsWith("0x"))
                                    SetTargetByIdAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "InitReactBattlebyStartAdd":
                                if (varValue.StartsWith("0x"))
                                    InitReactBattlebyStartAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "InitReactBattlebLockAdd":
                                if (varValue.StartsWith("0x"))
                                    InitReactBattlebLockAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "MonsterMinHPOffset":
                                if (varValue.StartsWith("0x"))
                                    MonsterMinHPOffset = Convert.ToInt32(varValue, 16);
                                break;
                            case "MonsterMaxHPOffset":
                                if (varValue.StartsWith("0x"))
                                    MonsterMaxHPOffset = Convert.ToInt32(varValue, 16);
                                break;
                            case "MonsterIDOffset":
                                if (varValue.StartsWith("0x"))
                                    MonsterIDOffset = Convert.ToInt32(varValue, 16);
                                break;
                            case "NormalAtkFixerAdd":
                                if (varValue.StartsWith("0x"))
                                    NormalAtkFixerAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "AtkSpeedAdd":
                                if (varValue.StartsWith("0x"))
                                    AtkSpeedAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "rushTimeAdd":
                                if (varValue.StartsWith("0x"))
                                    rushTimeAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "cameraRotateAdd":
                                if (varValue.StartsWith("0x"))
                                    cameraRotateAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "curQuickslot":
                                if (varValue.StartsWith("0x"))
                                    curQuickslot = Convert.ToInt32(varValue, 16);
                                break;
                            case "changeQuickslotAdd":
                                if (varValue.StartsWith("0x"))
                                    changeQuickslotAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "saveQuickslotAdd":
                                if (varValue.StartsWith("0x"))
                                    saveQuickslotAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "openHtmlAdd":
                                if (varValue.StartsWith("0x"))
                                    openHtmlAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "findNameNearAdd":
                                if (varValue.StartsWith("0x"))
                                    findNameNearAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "rotateCamAngleAdd":
                                if (varValue.StartsWith("0x"))
                                    rotateCamAngleAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "selectPlayerAdd":
                                if (varValue.StartsWith("0x"))
                                    selectPlayerAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "chattingClearAdd":
                                if (varValue.StartsWith("0x"))
                                    chattingClearAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "addChatMessageAdd":
                                if (varValue.StartsWith("0x"))
                                    addChatMessageAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "addInfoMessageAdd":
                                if (varValue.StartsWith("0x"))
                                    addInfoMessageAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "comboSkillAdd":
                                if (varValue.StartsWith("0x"))
                                    comboSkillAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "playerAroundAdd":
                                if (varValue.StartsWith("0x"))
                                    playerAroundAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "strAdd":
                                if (varValue.StartsWith("0x"))
                                    strAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "hpAdd":
                                if (varValue.StartsWith("0x"))
                                    hpAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "intAdd":
                                if (varValue.StartsWith("0x"))
                                    intAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "wisAdd":
                                if (varValue.StartsWith("0x"))
                                    wisAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "agiAdd":
                                if (varValue.StartsWith("0x"))
                                    agiAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "curstrAdd":
                                if (varValue.StartsWith("0x"))
                                    curstrAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "curhpAdd":
                                if (varValue.StartsWith("0x"))
                                    curhpAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "curintAdd":
                                if (varValue.StartsWith("0x"))
                                    curintAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "curwisAdd":
                                if (varValue.StartsWith("0x"))
                                    curwisAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "curagiAdd":
                                if (varValue.StartsWith("0x"))
                                    curagiAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "curPointsAdd":
                                if (varValue.StartsWith("0x"))
                                    curPointsAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "updateStatsAdd":
                                if (varValue.StartsWith("0x"))
                                    updateStatsAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "updateStats2Add":
                                if (varValue.StartsWith("0x"))
                                    updateStats2Add = Convert.ToInt32(varValue, 16);
                                break;
                            case "m_fWeaponDelayOffset":
                                if (varValue.StartsWith("0x"))
                                    m_fWeaponDelayOffset = Convert.ToInt32(varValue, 16);
                                break;
                            case "targetIDOffset":
                                if (varValue.StartsWith("0x"))
                                    targetIDOffset = Convert.ToInt32(varValue, 16);
                                break;
                            case "selUIDAdd":
                                if (varValue.StartsWith("0x"))
                                    selUIDAdd = Convert.ToInt32(varValue, 16);
                                break;
                            case "sendAttackAdd":
                                if (varValue.StartsWith("0x"))
                                    selUIDAdd = Convert.ToInt32(varValue, 16);
                                break;
                        }
                    }
                }
            }
        }

        public async Task checkSelectedAddressesAsync(int selectedEngineType)
        {
            if (selectedEngineType == 3)
            {
                // Load addresses from Pastebin for engine type 3
                string pastebinData = await FetchPastebinData("https://pastebin.com/raw/gLMndm8V");
                if (!string.IsNullOrEmpty(pastebinData))
                {
                    ParsePastebinData(pastebinData);
                    return;
                }
                MessageBox.Show("Error Getting Online Engine Data");
                return;
            }
            else if (selectedEngineType == 4)
            {
                // Load addresses from Pastebin for engine type 4
                string pastebinData = await FetchPastebinData("https://pastebin.com/raw/Naasq4tv");
                if (!string.IsNullOrEmpty(pastebinData))
                {
                    ParsePastebinData(pastebinData);
                    return;
                }
                MessageBox.Show("Error Getting Online Engine Data");
                return;
            }else if (selectedEngineType == 5)
            {
                // Load addresses from Pastebin for engine type 5
                string pastebinData = await FetchPastebinData("https://pastebin.com/raw/0sAjQmj9");
                if (!string.IsNullOrEmpty(pastebinData))
                {
                    ParsePastebinData(pastebinData);
                    return;
                }
                MessageBox.Show("Error Getting Online Engine Data");
                return;
            }

                // For other engine types, use the synchronous method
                checkSelectedAddresses(selectedEngineType);
        }

        public void checkSelectedAddresses(int selectedEngineType)
        {
            //hypernetwork
            if (selectedEngineType == 0)
            {
                engineType = 0;
                gameCharacterAdd = 0x008E05E4;
                GCMapxOffset = 0x46E0; // /0x2000
                GCMapyOffset = 0x46E8; // /0x2000
                playerStateOffset = 0x479C;
                nameAdd = 0x009C9754;
                guildAdd = 0x009C978C;
                moneyAdd = 0x009CCD28;
                targetAdd = 0x009CADE8;
                HealthX = 0x009C9924;
                HealthY = 0x008E0678;
                HealthAddresse = 0x009C966C;
                MaxManaAddresse = 0x009C9670;
                CurrManaAddresse = 0x009C9668;
                tapTargetAdd = 0x00433480;
                curTargetAddress = 0x008E0610;
                useSkillAdd = 0x0042E2D0;
                isMonsterAlive = 0x008E05EC;
                tabRange = 0x008E066C;
                isDropNearAdd = 0x008AF204;
                send_itemAdd = 0x00432180;
                targetKindAdd = 0x008CE200;
                checkObjectCrashAdd = 0x006C4BB0;
                zoomAdd = 0x008EE970;
                SetTargetByIdAdd = 0x006F94C0;
                InitReactBattlebyStartAdd = 0x009CB252;
                InitReactBattlebLockAdd = 0x009CB278;
                MonsterMinHPOffset = 0x475C;
                MonsterMaxHPOffset = 0x4760;
                MonsterIDOffset = 0x4DBC;
                NormalAtkFixerAdd = 0x008CE200;
                AtkSpeedAdd = 0x008E05F0;
                rushTimeAdd = 0x004375EA;
                cameraRotateAdd = 0x008EE978;
                curQuickslot = 0x009CDDB0;
                changeQuickslotAdd = 0x006CBC90;
                saveQuickslotAdd = 0x006CA4F0;
                openHtmlAdd = 0x006C0310;
                findNameNearAdd = 0x00428690;
                rotateCamAngleAdd = 0x008EE978;
                selectPlayerAdd = 0x006C3D20;
                chattingClearAdd = 0x006C3990;
                addChatMessageAdd = 0x006CC890;
                addInfoMessageAdd = 0x006CC8C0;
                comboSkillAdd = 0x008E05EE;
                playerAroundAdd = 0x009C8218;
                strAdd = 0x009B5B98;
                hpAdd = 0x009B5B9A;
                intAdd = 0x009B5B9C;
                wisAdd = 0x009B5B9E;
                agiAdd = 0x009B5BA0;
                curstrAdd = 0x009C94A8;
                curhpAdd = 0x009C94AC;
                curintAdd = 0x009C94B0;
                curwisAdd = 0x009C94B4;
                curagiAdd = 0x009C94B8;
                curPointsAdd = 0x009C94FC;
                updateStatsAdd = 0x00626100;
                updateStats2Add = 0x00626F80;
                m_fWeaponDelayOffset = 0x4C5C;
                targetIDOffset = 0x4444;
                selUIDAdd = 0x006C95D0;
                sendAttackAdd = 0x006C4D00;

            }
            //bango
            else if (selectedEngineType == 1)
            {
                engineType = 1;
                nameAdd = 0x00992900;
                targetAdd = 0x00993EC0;
                HealthX = 0x00992AD0;
                HealthY = 0x008A9858;
                HealthAddresse = 0x00992818;
                MaxManaAddresse = 0x0099281C;
                CurrManaAddresse = 0x00992814;
                tapTargetAdd = 0x0041A340;
                curTargetAddress = 0x008A97F0;
                useSkillAdd = 0x00415250;
                isMonsterAlive = 0x008A97CC;
                tabRange = 0x008A984C;
                isDropNearAdd = 0x0087C204;
                targetKindAdd = 0x00898B38;
                send_itemAdd = 0x00419040;
                checkObjectCrashAdd = 0x0069EB00;
                zoomAdd = 0x008B7B70;
                SetTargetByIdAdd = 0x006D3C20;
                InitReactBattlebyStartAdd = 0x00994329;
                InitReactBattlebLockAdd = 0x0099434C;
                MonsterMinHPOffset = 0x475C;
                MonsterMaxHPOffset = 0x4760;
                MonsterIDOffset = 0x4DBC;
                NormalAtkFixerAdd = 0x00898B38;
                AtkSpeedAdd = 0x008A97D0;
                rushTimeAdd = 0x0041E2CA;
                cameraRotateAdd = 0x008B7B78;
                curQuickslot = 0x00996E80;
                changeQuickslotAdd = 0x006A5CE0;
                saveQuickslotAdd = 0x006A4540;
                openHtmlAdd = 0x0069A0E0;
                findNameNearAdd = 0x0040E2C0;
                rotateCamAngleAdd = 0x008B7B78;
                selectPlayerAdd = 0x0069DC60;
                chattingClearAdd = 0x0069D8E0;
                addChatMessageAdd = 0x006A68F0;
                addInfoMessageAdd = 0x006A6920;
                comboSkillAdd = 0x008A97CE;

                strAdd = 0x0097ED68;
                hpAdd = 0x0097ED6A;
                intAdd = 0x0097ED6C;
                wisAdd = 0x0097ED6E;
                agiAdd = 0x0097ED70;
                curstrAdd = 0x00992650;
                curhpAdd = 0x00992654;
                curintAdd = 0x00992658;
                curwisAdd = 0x0099265C;
                curagiAdd = 0x00992660;
                curPointsAdd = 0x009926A4;
                updateStatsAdd = 0x00604740;
                updateStats2Add = 0x006055C0;

                //missing
                gameCharacterAdd = 0x0097ED6A;
                playerStateOffset = 0x0000;
                GCMapxOffset = 0x2000; // /0x2000
                GCMapyOffset = 0x2000; // /0x2000
                guildAdd = 0x0097ED6A;
                moneyAdd = 0x0097ED6A;
                playerAroundAdd = 0x0097ED6A;
            }
            // New 2019 (timo)
            else if (selectedEngineType == 2)
            {
                engineType = 2;
                gameCharacterAdd = 0x00906824; // new 2019
                GCMapxOffset = 0x473C; // /0x2000 // new
                GCMapyOffset = 0x4744; // /0x2000 // new
                playerStateOffset = 0x47F4; // new 2019
                nameAdd = 0x009EFA44; //new
                guildAdd = 0x0; //not used
                moneyAdd = 0x0; // not used
                targetAdd = 0x009F10F8; //new
                HealthX = 0x009EFC38; //new
                HealthY = 0x009068B8; //new
                HealthAddresse = 0x009EF95C; //new
                MaxManaAddresse = 0x009EF960; //new 
                CurrManaAddresse = 0x009EF958; //new
                tapTargetAdd = 0x00433870; // new
                curTargetAddress = 0x00906850;//new
                useSkillAdd = 0x0042E360; //new
                isMonsterAlive = 0x0090682C; //new
                tabRange = 0x009068AC; //new
                isDropNearAdd = 0x008D1204;//new
                send_itemAdd = 0x00432570; //new
                targetKindAdd = 0x008F40F0; //new
                checkObjectCrashAdd = 0x006DF580;//new
                zoomAdd = 0x008A18BC; //new
                SetTargetByIdAdd = 0x00714C10;//new
                InitReactBattlebyStartAdd = 0x009F1561;//new
                InitReactBattlebLockAdd = 0x009F1588;//new
                MonsterMinHPOffset = 0x47B8; // new (search CGameEffectBoneModel -> first function)
                MonsterMaxHPOffset = 0x47BC; //new (search CGameEffectBoneModel -> first function)
                MonsterIDOffset = 0x4E1C; // new search IsSkill or value 421 second function
                NormalAtkFixerAdd = 0x008F40F0; // new // same as targetKindAdd fml
                AtkSpeedAdd = 0x00906830; // new
                rushTimeAdd = 0x00000000; // not used
                cameraRotateAdd = 0x00914BD8;// new
                rotateCamAngleAdd = 0x00914BD8;// new
                curQuickslot = 0x009F4138; //new search CControl::OnPaint line 13
                changeQuickslotAdd = 0x006E65D0;//new
                saveQuickslotAdd = 0x006E4E30; //new search KWindowCollector__CloseAll go to restoredevice then the first call
                openHtmlAdd = 0x006DACC0; //new
                findNameNearAdd = 0x004286F0; //new
                selectPlayerAdd = 0x006DE650;//new
                chattingClearAdd = 0x00000000; //not used
                addChatMessageAdd = 0x006E72C0;//new
                addInfoMessageAdd = 0x006E72F0;//new
                comboSkillAdd = 0x0090682E; //new
                playerAroundAdd = 0x009EE4E8; //new     search from DeleteSocketMsg 
                strAdd = 0x009DBE60; // new
                hpAdd = 0x009DBE62; // new
                intAdd = 0x009DBE64; // new
                wisAdd = 0x009DBE66; // new
                agiAdd = 0x009DBE68; // new
                curstrAdd = 0x009EF798; //new
                curhpAdd = 0x009EF79C; //new
                curintAdd = 0x009EF7A0;//new
                curwisAdd = 0x009EF7A4; //new
                curagiAdd = 0x009EF7A8; //new
                curPointsAdd = 0x009EF7EC; //new
                updateStatsAdd = 0x0063AE80; //new
                updateStats2Add = 0x0063BD20; // new <- AddInfoPoints
                m_fWeaponDelayOffset = 0x4CBC; //new
                targetIDOffset = 0x4444; // new
                selUIDAdd = 0x006E3F00; // new
                sendAttackAdd = 0x006DF6D0;// new

            }
        }

    }
}
