using AIMemoryReader;
using Binarysharp.MemoryManagement;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using static Heth_AIO.Imports;

namespace Heth_AIO.myClasses
{
    public class EngineData
    {
        public string targetName = "";
        public int maxHP = 0;
        public int curHP = 0;
        public int maxMP = 0;
        public int curMP = 0;
        public int curXMap = 0;
        public int curYMap = 0;
        public float myZoom = 0;
        public string myZoomStr = "";
        public string charName = "";
        public bool isRunning = false;
        bool isMouseTracking = false;
        string lastAddedMousePos = "";
        ProcessCMD myProcess;
        Addresses myAddress;
        public Memory memory;
        public bool newReader = false;

        public EngineData(ProcessCMD myP, Addresses myAdd)
        {
            myProcess = myP;
            myAddress = myAdd;
                memory = new Memory(myProcess.selectedProcess);
        }


        public void StopThreads()
        {
         isRunning = false;
         isMouseTracking = false;
        }


        public void Start(bool newreader){
            newReader = newreader;
            isRunning = true;
            if (newReader)
            {
                memory = new Memory(myProcess.selectedProcess);
            }
            new Thread(() => {
                while (isRunning)
                {
                    getMemoryData();
                    Thread.Sleep(600);
                }
            }).Start();
        }

        public void Stop()
        {
            isRunning = false;
        }

        private void getMemoryData()
        {
            if (myProcess.selectedProcess == 0) return;


            if (myProcess.IsRunning())
            {
                try
                {
                    if (newReader)
                    {
                        charName = memory.ReadString((IntPtr)myAddress.nameAdd, 13, Encoding.ASCII);
                            maxMP = memory.Read<int>((IntPtr)myAddress.MaxManaAddresse);
                        curMP = memory.Read<int>((IntPtr)myAddress.CurrManaAddresse);
                        int healthNumX = memory.Read<int>((IntPtr)myAddress.HealthX);
                        int healthNumY = memory.Read<int>((IntPtr)myAddress.HealthY);
                        maxHP = memory.Read<int>((IntPtr)myAddress.HealthAddresse);
                        curHP = healthNumX ^ healthNumY;
                        targetName = memory.ReadString((IntPtr)myAddress.targetAdd,50, Encoding.ASCII);
                        myZoom = memory.Read<float>((IntPtr)myAddress.zoomAdd);
                            int curChar = memory.Read<int>((IntPtr)myAddress.gameCharacterAdd);
                            int mapV1 = memory.Read<int>((IntPtr)(curChar + myAddress.GCMapxOffset));
                            int mapV2 = memory.Read<int>((IntPtr)(curChar + myAddress.GCMapyOffset));
                            int doneMapV1 = mapV1 / 0x2000;
                            int doneMapV2 = mapV2 / 0x2000;
                            curXMap = doneMapV1;
                            curYMap = doneMapV2;
                        
                    }
                    else { 
                        Process engine = Process.GetProcessById(myProcess.selectedProcess);
                    var sharp = new MemorySharp(engine);
                        maxMP = sharp.Read<int>((IntPtr)myAddress.MaxManaAddresse, false);
                        curMP = sharp.Read<int>((IntPtr)myAddress.CurrManaAddresse, false);
                        int healthNumX = sharp.Read<int>((IntPtr)myAddress.HealthX, false);
                        int healthNumY = sharp.Read<int>((IntPtr)myAddress.HealthY, false);
                        maxHP = sharp.Read<int>((IntPtr)myAddress.HealthAddresse, false);
                        curHP = healthNumX ^ healthNumY;
                        charName = sharp.ReadString((IntPtr)myAddress.nameAdd, Encoding.ASCII, false, 13);
                        targetName = sharp.ReadString((IntPtr)myAddress.targetAdd, Encoding.ASCII, false);
                        myZoom = sharp.Read<float>((IntPtr)myAddress.zoomAdd, false);
                             int curChar = sharp.Read<int>((IntPtr)myAddress.gameCharacterAdd, false);
                             int mapV1 = sharp.Read<int>((IntPtr)curChar + myAddress.GCMapxOffset, false);
                             int mapV2 = sharp.Read<int>((IntPtr)curChar + myAddress.GCMapyOffset, false);
                             int doneMapV1 = mapV1 / 0x2000;
                             int doneMapV2 = mapV2 / 0x2000;
                             curXMap = doneMapV1;
                             curYMap = doneMapV2;
                    
                    }








                    // double manaDiff = maxMP * 0.60;
                    // double healthDiff = maxHP * 0.80;
                }
                catch (Exception ex){

                    //MessageBox.Show(ex.Message,"Heth AIO [Error EngineData->getMemoryData]"); 
                    Thread.Sleep(2000);

                }

            }
            else
            {
                isRunning = false;
                myProcess.myCurInjectedHandle = IntPtr.Zero;
                myProcess.selectedProcess = 0;
                MessageBox.Show("Engine is not running cant get data", "Heth's AIO");
                return;
            }
        }


        public void SetZoom(float val)
        {
            if (myProcess.selectedProcess == 0) return;

            if (newReader)
            {
               // memory = new Memory(myProcess.selectedProcess);
                memory.Write<float>((IntPtr)myAddress.zoomAdd, val);
            }
            else
            {
                var sharp = new MemorySharp(myProcess.GetEngineProcess());
                sharp.Write<float>((IntPtr)myAddress.zoomAdd, Convert.ToInt32(val), false);
            }

        }
        public bool RightMouseClick(int x, int y)
        {
            IntPtr handle = myProcess.myCurInjectedHandle;
            if (handle == IntPtr.Zero) return false;
            
            int lParam = Imports.MakeLParam(x, y);
            //int leftDown = 0x201;
            //int leftUp = 0x202;
            int rightDown = 0x204;
            int rightUp = 0x205;

            new Thread(() => {
                Imports.SendMessage(handle, rightDown, 0, lParam);
                Thread.Sleep(10);
                Imports.SendMessage(handle, rightUp, 0, lParam);

            }).Start();
            return true;
        }

        public bool LeftMouseClick(int x, int y)
        {
            IntPtr handle = myProcess.myCurInjectedHandle;
            if (handle == IntPtr.Zero) return false;

            int lParam = Imports.MakeLParam(x, y);
            int leftDown = 0x201;
            int leftUp = 0x202;
            //int rightDown = 0x204;
            //int rightUp = 0x205;

            new Thread(() => {
                Imports.SendMessage(handle, leftDown, 0, lParam);
                Thread.Sleep(10);
                Imports.SendMessage(handle, leftUp, 0, lParam);

            }).Start();
            return true;
        }

        public bool KeyPress(int keyvalue)
        {
            IntPtr handle = myProcess.myCurInjectedHandle;
            if (handle == IntPtr.Zero) return false;

            switch (keyvalue)
            {
                case 0:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_0, 0);
                    break;
                case 1:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_1, 0);
                    break;
                case 2:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_2, 0);
                    break;
                case 3:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_3, 0);
                    break;
                case 4:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_4, 0);
                    break;
                case 5:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_5, 0);
                    break;
                case 6:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_6, 0);
                    break;
                case 7:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_7, 0);
                    break;
                case 8:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_8, 0);
                    break;
                case 9:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_KEY_9, 0);
                    break;
                case 11:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F1, 0);
                    break;
                case 12:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F2, 0);
                    break;
                case 13:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F3, 0);
                    break;
                case 14:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F4, 0);
                    break;
                case 15:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F5, 0);
                    break;
                case 16:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F6, 0);
                    break;
                case 17:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F7, 0);
                    break;
                case 18:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F8, 0);
                    break;
                case 19:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F9, 0);
                    break;
                case 20:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F10, 0);
                    break;
                case 21:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F11, 0);
                    break;
                case 22:
                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_F12, 0);
                    break;
                default:
                    break;
            }
            return true;
        }

        public void StartMouseTracker(RichTextBox macroBox, Label lblMousePos)
        {
            IntPtr handle = myProcess.myCurInjectedHandle;
            if (handle.Equals(IntPtr.Zero))
            {
                MessageBox.Show("The app is not injected", "Heth AIO");
                isMouseTracking = false;
                return;
            }
            isMouseTracking = true;
            new Thread(() => {
                while (isMouseTracking)
                {
                    if (handle.Equals(IntPtr.Zero)) return;
                    var mousePosition = Cursor.Position;
                    RECT rct = new RECT();
                    GetWindowRect(handle, ref rct);
                    int mouserelativepositionX = mousePosition.X - rct.left - 8;
                    int mouserelativepositionY = mousePosition.Y - rct.top - 30;
                    if ((GetAsyncKeyState(Keys.F7) & 0x8000) != 0)
                    {
                        string addItem = "left " + mouserelativepositionX.ToString() + " , " + mouserelativepositionY.ToString();
                        if (lastAddedMousePos != addItem)
                        {
                            lastAddedMousePos = addItem;
                            if (macroBox.InvokeRequired)
                            {
                                macroBox.Invoke((MethodInvoker)(delegate {
                                    macroBox.AppendText(System.Environment.NewLine + addItem);
                                }));
                            }
                        }
                    }
                    if ((GetAsyncKeyState(Keys.F8) & 0x8000) != 0)
                    {
                        string addItem = "right " + mouserelativepositionX.ToString() + " , " + mouserelativepositionY.ToString();
                        if (lastAddedMousePos != addItem)
                        {
                            lastAddedMousePos = addItem;
                            if (macroBox.InvokeRequired)
                            {
                                macroBox.Invoke((MethodInvoker)(delegate {
                                    macroBox.AppendText(System.Environment.NewLine + addItem);
                                }));
                            }
                        }
                    }

                    if (lblMousePos.InvokeRequired)
                    {
                        lblMousePos.Invoke((MethodInvoker)(delegate {
                            lblMousePos.Text = "Mouse Position: "+mouserelativepositionX.ToString() + " , " + mouserelativepositionY.ToString();
                        }));
                    }

                    Thread.Sleep(100);
                }
            }).Start();
        }

        public void StopMouseTracker(Button myBtn)
        {
            isMouseTracking = false;
            if (myBtn.InvokeRequired)
            {
                myBtn.Invoke((MethodInvoker)(delegate {
                    myBtn.PerformClick();
                }));
            }
            
        }
        private void mouseTracker(RichTextBox macroBox, Label lblMousePos)
        {

          

        }

    }
}
