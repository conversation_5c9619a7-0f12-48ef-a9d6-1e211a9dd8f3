﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Heth_AIO.myClasses
{
    class CheckerCMD
    {
        private int h, m, s;
        EngineData myEngineData;

        public CheckerCMD(EngineData myED)
        {
            myEngineData = myED;
        }
        public bool isSameTarget(string myMob)
        {
            return myEngineData.targetName.Contains(myMob);
        }
        public bool isPlayerDead()
        {
            return myEngineData.curHP == 0;
        }

        public bool isPlayerLowHP()
        {
            return myEngineData.curHP < myEngineData.curHP * 0.40;
        }

        public void Alert()
        {
            new Thread(() => Imports.Beep(300, 350)).Start();
        }
        public string AliveTimer()
        {
            s++;
            if (s >= 60)
            {
                s = 0;
                m++;
            }
            if (m >= 60)
            {
                m = 0;
                h++;
            }

            return h + ":" + m + ":" + s;
        }
        public void ResetAliveTimer()
        {
            
            h = 0;
            m = 0;
            s = 0;
        }



    }
}
