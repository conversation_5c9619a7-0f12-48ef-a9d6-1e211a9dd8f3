﻿using Heth_AIO.myClasses;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Heth_AIO
{
    public partial class ExtraMacroForm : Form
    {
        Addresses myAddress;
        ProcessCMD myProcess;
        EngineData myEngineData;
        MacroCMD myMacro;
        bool threadRunning = true;
        bool runLoop = false;
        string winName = "error";
        string macroName = "error cant get macro number";
        public bool MacroStart = false;
        bool safeMode = false;
        //public bool pauseMacro = false;
        //public bool pauseMacroWaitNow = false;
        
        public ExtraMacroForm(ProcessCMD myP, EngineData myE,Addresses myA, MacroCMD myM, string wN,string name,bool safemode)
        {
            myAddress = myA;
            myProcess = myP;
            myEngineData = myE;
            myMacro = myM;
            winName = wN;
            macroName = name;
            safeMode = safemode;
            InitializeComponent();

        }

        private void ExtraMacroForm_Load(object sender, EventArgs e)
        {
            richMacroBox.SelectionAlignment = HorizontalAlignment.Center;
            richMacroBox.Text = Heth_AIO.Properties.Settings.Default.extraMacro;
            this.Text = "Working on: "+winName;
            lblRunInfo.Text = macroName;
        }

        private void richMacroBox_TextChanged(object sender, EventArgs e)
        {
            richMacroBox.SelectionAlignment = HorizontalAlignment.Center;
        }

        private void ExtraMacroForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            Heth_AIO.Properties.Settings.Default.extraMacro = richMacroBox.Text;
              MacroStart = false;

    }


        StringCollection GetLinesCollectionFromTextBox(RichTextBox textBox)
        {
            StringCollection lines = new StringCollection();

            // lineCount may be -1 if TextBox layout info is not up-to-date.
            int lineCount = textBox.Lines.Count();

            for (int line = 0; line < lineCount; line++)
            {
                // GetLineText takes a zero-based line index.
                lines.Add(textBox.Lines[line]);
            }
            return lines;
        }
        public void resetHighlights(int x)
        {
            if (InvokeRequired)
            {
                this.Invoke(new Action<int>(resetHighlights), new object[] {
          x
        });
                return;
            }
            if (x == 0)
            {
                richMacroBox.SelectAll();
                richMacroBox.SelectionColor = Color.Silver;
                richMacroBox.DeselectAll();
            }
            else if (x == 1)
            {
                richMacroBox.SelectAll();
                richMacroBox.SelectionColor = Color.Gold;
                richMacroBox.DeselectAll();
            }

            if (richMacroBox.HideSelection == false)
            {
                richMacroBox.HideSelection = true;
            }

        }
        public void HighlightLine(int lineIdx)
        {
            if (InvokeRequired)
            {
                this.Invoke(new Action<int>(HighlightLine), new object[] {
          lineIdx
        });
                return;
            }
            richMacroBox.Select(richMacroBox.GetFirstCharIndexFromLine(lineIdx), richMacroBox.Lines[lineIdx].Length);
            richMacroBox.SelectionColor = Color.SeaGreen;

        }
        public void HighlightRedLine(int lineIdx)
        {
            if (InvokeRequired)
            {
                this.Invoke(new Action<int>(HighlightRedLine), new object[] {
          lineIdx
        });
                return;
            }
            richMacroBox.Select(richMacroBox.GetFirstCharIndexFromLine(lineIdx), richMacroBox.Lines[lineIdx].Length);
            richMacroBox.SelectionColor = Color.Red;

        }
        public void botter()
        {
            resetHighlights(0);
            richMacroBox.ReadOnly = true;
            StringCollection lines = GetLinesCollectionFromTextBox(richMacroBox);
            if (lines.Count == 0)
            {
                stopMacroBot();
                MessageBox.Show("I cant read any macro command, stopping..", "Heth's AIO");
                return;
            }
            IntPtr handle = myProcess.myCurInjectedHandle;
            if (handle.Equals(IntPtr.Zero) || !myProcess.IsRunning())
            {
                myProcess.selectedProcess = 0;
                myProcess.myCurInjectedHandle = IntPtr.Zero;
                stopMacroBot();

                return;
            }
            runLoop = true;

            var botterThread = new Thread(() => {
                try
                {

                    while (runLoop)
                    {
                        resetHighlights(1);
                        int linei = 0;

                        foreach (string line in lines)
                        {

                            if (runLoop == false)
                            {
                                threadRunning = true;
                                break;
                            }
                            if ((handle.Equals(IntPtr.Zero) || !myProcess.IsRunning() || myProcess.selectedProcess == 0) && !safeMode)
                            {
                                myProcess.myCurInjectedHandle = IntPtr.Zero;
                                runLoop = false;
                                MessageBox.Show("Engine is not running, STOPPING!! .", "Heth's AIO");
                                stopMacroBot();
                                break;
                            }

                            //lower all
                            string capLine = line;
                            line.ToLower();


                            if (line.Contains("wait"))
                            {
                                HighlightLine(linei);
                                // Console("[Marco] " + line);
                                Match cd = Regex.Match(line, @"\d+");
                                int parsedintValue;
                                if (int.TryParse(cd.Value, out parsedintValue))
                                {
                                    Thread.Sleep(int.Parse(cd.Value));
                                }
                            }
                            else if (line == "key space")
                            {
                                HighlightLine(linei);
                                Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_SPACE, 0);
                            }
                            else if (line == "key tab")
                            {
                                HighlightLine(linei);
                                Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_TAB, 0);
                            }
                            else if (line == "key `")
                            {
                                HighlightLine(linei);
                                Imports.PostMessage(handle, myAddress.WM_KEYDOWN, 0xC0, 0);
                            }
                            else if (line == "rotate camera")
                            {

                                HighlightLine(linei);
                                if (myMacro.RotateCamera())
                                {
                                    
                                }
                            }
                            else if (line.Contains("smartkey"))
                            {

                                HighlightLine(linei);

                                int parsedintValue;
                                Match key = Regex.Match(line, @"\d+");
                                if (int.TryParse(key.Value, out parsedintValue))
                                {
                                    int keyvalue = int.Parse(key.Value);
                                    if (myMacro.SmartKeyPress(keyvalue - 1))
                                    {
                                       // Console("[Marco] Smart Key " + line);
                                    }
                                }

                            }
                            else if (line.Contains("key") || line.Contains("keyboard"))
                            {
                                HighlightLine(linei);
                                
                                int parsedintValue;
                                Match key = Regex.Match(line, @"\d+");
                                if (int.TryParse(key.Value, out parsedintValue))
                                {
                                    int keyvalue = int.Parse(key.Value);
                                    myEngineData.KeyPress(keyvalue);
                                }
                            }
                            else if (line.Contains("mouseleft") || line.Contains("left") || line.Contains("clickleft"))
                            {
                                HighlightLine(linei);
                                
                                MatchCollection key = Regex.Matches(line, @"\d+");
                                if (key.Count == 2)
                                {
                                    int screenX = int.Parse(key[0].Value);
                                    int screenY = int.Parse(key[1].Value);
                                    myEngineData.LeftMouseClick(screenX, screenY);
                                }
                            }
                            else if (line.Contains("mouseright") || line.Contains("right") || line.Contains("clickright"))
                            {
                                HighlightLine(linei);
                             
                                MatchCollection key = Regex.Matches(line, @"\d+");
                                if (key.Count == 2)
                                {
                                    int screenX = int.Parse(key[0].Value);
                                    int screenY = int.Parse(key[1].Value);
                                    myEngineData.RightMouseClick(screenX, screenY);
                                }
                            }
                            else if (line == "pick if good hp")
                            {
                                HighlightLine(linei);
                                if (myEngineData.curHP > myEngineData.maxHP * 0.80)
                                {
                                    Imports.PostMessage(handle, myAddress.WM_KEYDOWN, myAddress.VK_SPACE, 0);
                                    
                                }
                            }
                            else if (line == "drop pick" || line == "drop pick med" || line == "drop pick far" || line == "drop pick very far" || line == "pick drop" || line == "pick drop med" || line == "pick drop far" || line == "pick drop very far")
                            {
                                HighlightLine(linei);
                                if (myMacro.DropPick(line))
                                {

                                    
                                }

                            }
                            else if (line.Contains("next target if \""))
                            {
                                HighlightLine(linei);
                                if (myMacro.NextTargetIfName(line, myEngineData.targetName))
                                {

                                    

                                }

                            }
                            else if (line == "next target")
                            {
                                HighlightLine(linei);
                                if (myMacro.NextTarget())
                                {

                                  
                                }

                            }
                            else if (line == "next target if not monster")
                            {
                                HighlightLine(linei);
                                if (myMacro.NextTargetIfNotMonster())
                                {

                                 
                                }
                            }
                            else if (line.Contains("next target if dead"))
                            {
                                HighlightLine(linei);
                                if (myMacro.NextTargetIfDead())
                                {

                                   
                                }
                            }
                            else if (line == "next target if wall")
                            {
                                HighlightLine(linei);
                                if (myMacro.NextTargetIfWall())
                                {

                                  
                                }
                            }
                            else if (line.Contains("select player \""))
                            {
                                HighlightLine(linei);
                                if (myMacro.SelectPlayer(capLine, 0))
                                {
                                   
                                }
                            }
                            else if (line.Contains("target player \""))
                            {
                                HighlightLine(linei);
                                if (myMacro.SelectPlayer(capLine, 1))
                                {
                                   
                                }
                            }
                            else if (line.Contains("press") && line.Contains("if dead"))
                            {

                                HighlightLine(linei);
                                int curtarget = myMacro.GetTargetID();
                                if (curtarget != 0)
                                {
                                    UInt32 curtargetMinHP = myMacro.GetTargetMinHP();
                                    if (curtargetMinHP == 0)
                                    {
                                       
                                        int parsedintValue;
                                        Match key = Regex.Match(line, @"\d+");
                                        if (int.TryParse(key.Value, out parsedintValue))
                                        {
                                            int keyvalue = int.Parse(key.Value);
                                            myEngineData.KeyPress(keyvalue);
                                        }
                                    }
                                }

                            }
                            else if (line.Contains("press") && line.Contains("if low hp"))
                            {

                                HighlightLine(linei);
                                if (myEngineData.curHP < myEngineData.maxHP * 0.20)
                                {
                                    
                                    int parsedintValue;
                                    MatchCollection key = Regex.Matches(line, @"\d+");
                                    if (key.Count == 2)
                                    {
                                        if (int.TryParse(key[0].Value, out parsedintValue) && int.TryParse(key[1].Value, out parsedintValue))
                                        {
                                            int keyvalue = int.Parse(key[0].Value);
                                            int waitvalue = int.Parse(key[1].Value);
                                            myEngineData.KeyPress(keyvalue);
                                            Thread.Sleep(waitvalue);
                                        }

                                    }
                                }

                            }
                            else if (line.Contains("press") && line.Contains("if low mana"))
                            {

                                HighlightLine(linei);

                                if (myEngineData.curMP < myEngineData.maxMP * 0.10)
                                {
                                   
                                    int parsedintValue;
                                    MatchCollection key = Regex.Matches(line, @"\d+");
                                    if (key.Count == 2)
                                    {
                                        if (int.TryParse(key[0].Value, out parsedintValue) && int.TryParse(key[1].Value, out parsedintValue))
                                        {
                                            int keyvalue = int.Parse(key[0].Value);
                                            int waitvalue = int.Parse(key[1].Value);
                                            myEngineData.KeyPress(keyvalue);
                                            Thread.Sleep(waitvalue);
                                        }

                                    }
                                }

                            }
                            else if (line.Contains("normal attack") || line.Contains("normal atk"))
                            {
                                HighlightLine(linei);
                                if (line == "normal attack 1" || line == "normal atk 1")
                                {
                                    if (myMacro.NormalAttack(1)) { }
                                        

                                }
                                else if (line == "normal attack 2" || line == "normal atk 2")
                                {
                                    if (myMacro.NormalAttack(2)) { }
                                        

                                }
                                else
                                {
                                    if (myMacro.NormalAttack(2)) { }
                                        

                                }


                            }
                            else if (line.Contains("skill"))
                            {
                                HighlightLine(linei);
                                if (myMacro.UseSkill(line))
                                {
                                }
                            }
                            else { HighlightRedLine(linei); }

                            linei++;
                        }

/*                        if (pauseMacro)
                        {
                            pauseMacroWaitNow = false;
                        }*/

                    }
                }
                catch
                {
/*                    if (pauseMacro)
                    {
                        pauseMacroWaitNow = false;
                    }*/
                    //MessageBox.Show("The extra macro thread crashed or stopped forcefully, it's okay you can start again.","Heth AIO");
                }
            });
            botterThread.Start();
            new Thread(() => {
                while (threadRunning)
                {
                    if (runLoop == false)
                    {
                        botterThread.Interrupt();
                        botterThread.Abort();
                        //threadRunning = false;
                    }
                    Thread.Sleep(200);
                }
            }).Start();

        }

        private void startMacroBot()
        {
            string myReplacedMacros = myMacro.ReplaceShortcuts(richMacroBox.Text);
            richMacroBox.Text = myReplacedMacros;
            btnStartBot.Enabled = false;
            btnStopBot.Enabled = true;
            MacroStart = true;
            botter();
            

        }
            private void stopMacroBot()
        {
            resetHighlights(0);
            richMacroBox.ReadOnly = false;
            runLoop = false;
            btnStartBot.Enabled = true;
            btnStopBot.Enabled = false;
            MacroStart = false;
            Heth_AIO.Properties.Settings.Default.extraMacro = richMacroBox.Text;
        }
        private void btnStartBot_Click(object sender, EventArgs e)
        {
            startMacroBot();
        }

        private void btnStopBot_Click(object sender, EventArgs e)
        {
            stopMacroBot();
        }

        private void checkPause_CheckedChanged(object sender, EventArgs e)
        {
/*            if (checkPause.Checked)
            {
                checkPause.ForeColor = Color.MediumAquamarine;
                pauseMacro = true;
                MessageBox.Show("Use the macro 'pause 20000' to pause the macro for 20 seconds\nMain macro will stop while this macro is running!\nMain macro will continue if this macro is on pause");
            }
            else
            {
                checkPause.ForeColor = Color.FromArgb(206, 45, 79);
                pauseMacro = false;
            }*/
        }

        private void timerChecker_Tick(object sender, EventArgs e)
        {


            if (MacroStart == true && btnStartBot.Enabled == true)
            {
                startMacroBot();
            }
            else if(MacroStart == false && btnStartBot.Enabled == false)
            {
                stopMacroBot();
            }
        }

        private void lblRunInfo_Click(object sender, EventArgs e)
        {

        }
    }
}
