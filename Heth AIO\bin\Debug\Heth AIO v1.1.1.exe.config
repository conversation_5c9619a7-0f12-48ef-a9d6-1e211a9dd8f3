﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="Heth_AIO.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7" />
    </startup>
    <userSettings>
        <Heth_AIO.Properties.Settings>
            <setting name="winName" serializeAs="String">
                <value>KalAIO Online</value>
            </setting>
            <setting name="monsterName" serializeAs="String">
                <value>High Class</value>
            </setting>
            <setting name="macroBox" serializeAs="String">
                <value>Your macro</value>
            </setting>
            <setting name="GMNames" serializeAs="String">
                <value>Admin</value>
            </setting>
            <setting name="txtMacroName" serializeAs="String">
                <value>Default</value>
            </setting>
            <setting name="mPreset1" serializeAs="String">
                <value />
            </setting>
            <setting name="mPreset2" serializeAs="String">
                <value />
            </setting>
            <setting name="mPreset3" serializeAs="String">
                <value />
            </setting>
            <setting name="mPreset4" serializeAs="String">
                <value />
            </setting>
            <setting name="mPreset5" serializeAs="String">
                <value />
            </setting>
            <setting name="engineBox" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="manaBox" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="healthBox" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="nAntiAfkCD" serializeAs="String">
                <value>5000</value>
            </setting>
            <setting name="nAntiAfkX" serializeAs="String">
                <value>150</value>
            </setting>
            <setting name="nAntiAfkY" serializeAs="String">
                <value>150</value>
            </setting>
            <setting name="presetBox" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="txtPreset1" serializeAs="String">
                <value>Preset 1</value>
            </setting>
            <setting name="txtPreset2" serializeAs="String">
                <value>Preset 2</value>
            </setting>
            <setting name="txtPreset3" serializeAs="String">
                <value>Preset 3</value>
            </setting>
            <setting name="txtPreset4" serializeAs="String">
                <value>Preset 4</value>
            </setting>
            <setting name="txtPreset5" serializeAs="String">
                <value>Preset 5</value>
            </setting>
            <setting name="licenseKey" serializeAs="String">
                <value>xxxx-xxxx-xxxx-xxxx-xxxx</value>
            </setting>
            <setting name="GMNamesBox" serializeAs="String">
                <value>Admin,&lt;A&gt;Admin</value>
            </setting>
            <setting name="extraMacro" serializeAs="String">
                <value>Your Macro...</value>
            </setting>
            <setting name="ctrlMacro" serializeAs="String">
                <value />
            </setting>
            <setting name="txtMapX1" serializeAs="String">
                <value />
            </setting>
            <setting name="txtMapX2" serializeAs="String">
                <value />
            </setting>
            <setting name="txtMapX3" serializeAs="String">
                <value />
            </setting>
            <setting name="txtMapX4" serializeAs="String">
                <value />
            </setting>
            <setting name="txtMapY1" serializeAs="String">
                <value />
            </setting>
            <setting name="txtMapY2" serializeAs="String">
                <value />
            </setting>
            <setting name="txtMapY3" serializeAs="String">
                <value />
            </setting>
            <setting name="txtMapY4" serializeAs="String">
                <value />
            </setting>
        </Heth_AIO.Properties.Settings>
    </userSettings>
</configuration>