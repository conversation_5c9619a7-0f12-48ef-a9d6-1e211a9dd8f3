﻿using AIMemoryReader;
using Binarysharp.MemoryManagement;
using Heth_AIO.myClasses;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Heth_AIO
{
    public partial class Overlay : Form
    {
        Addresses myAddress = new Addresses();
        ProcessCMD myProcess;
        EngineData myEngineData;
        int selectedEngineType = 0;

        #region [RECT Import]



        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool GetWindowRect(IntPtr hwnd, out RECT IpRect);

        public static RECT rect;

        public struct RECT
        {
            public int left, top, right, bottom;
        }
        #endregion

        public Overlay(ProcessCMD pCMD, EngineData myED, int selectedET)
        {
            myProcess = pCMD;
            myEngineData = myED;
            selectedEngineType = selectedET;
            InitializeComponent();
            
        }

        private void Overlay_Load(object sender, EventArgs e)
        {
            CheckForIllegalCrossThreadCalls = false;
            this.BackColor = Color.Black;
            this.TransparencyKey = Color.Black;
            this.TopMost = true;
            myAddress.checkSelectedAddresses(selectedEngineType);
            int initialStyle = Imports.GetWindowLong(this.Handle, -20);
            Imports.SetWindowLong(this.Handle, -20, initialStyle | 0x8000 | 0x20);
            backgroundWorker1.RunWorkerAsync();
            backgroundWorker2.RunWorkerAsync();
        }

        private void backgroundWorker1_DoWork(object sender, DoWorkEventArgs e)
        {
            while (true)
            {
                if (myProcess.myCurInjectedHandle != IntPtr.Zero)
                {

                    GetWindowRect(myProcess.myCurInjectedHandle, out rect);
                    this.Size = new Size(rect.right - rect.left, rect.bottom - rect.top);
                    this.Left = rect.left;
                    this.Top = rect.top + 300;
                    Thread.Sleep(1000);
                }
                else
                {
                    this.Close();
                }
            }
        }

        private void backgroundWorker2_DoWork(object sender, DoWorkEventArgs e)
        {
            while (true)
            {
                if (!myProcess.IsRunning())
                {
                    this.Close();
                    myProcess.selectedProcess = 0;
                }
                if (myProcess.selectedProcess == 0) return;

                if (myEngineData.newReader)
                {
                    var memory = new Memory(myProcess.selectedProcess);
                    int curtargetx = memory.Read<int>((IntPtr)myAddress.curTargetAddress);
                    if (curtargetx != 0)
                    {
                        int curtargetMinHP = memory.Read<int>((IntPtr)curtargetx + myAddress.MonsterMinHPOffset);
                        int curtargetMaxHP = memory.Read<int>((IntPtr)curtargetx + myAddress.MonsterMaxHPOffset);
                        int curtargetID = memory.Read<int>((IntPtr)curtargetx + myAddress.MonsterIDOffset);
                        healthLBL.Text = "Health: " + curtargetMinHP.ToString() + "/" + curtargetMaxHP.ToString();
                        idLBL.Text = "ID: " + curtargetID.ToString();
                    }
                    else
                    {
                        healthLBL.Text = "Health: -/-";
                        idLBL.Text = "ID: -";
                    }

                }
                else {
                    var sharp = new MemorySharp(myProcess.GetEngineProcess());
                    int curtarget = sharp.Read<int>((IntPtr)myAddress.curTargetAddress, false);
                    if (curtarget != 0)
                    {
                        int curtargetMinHP = sharp.Read<int>((IntPtr)curtarget + myAddress.MonsterMinHPOffset, false);
                        int curtargetMaxHP = sharp.Read<int>((IntPtr)curtarget + myAddress.MonsterMaxHPOffset, false);
                        int curtargetID = sharp.Read<int>((IntPtr)curtarget + myAddress.MonsterIDOffset, false);
                        healthLBL.Text = "Health: " + curtargetMinHP.ToString() + "/" + curtargetMaxHP.ToString();
                        idLBL.Text = "ID: " + curtargetID.ToString();
                    }
                    else
                    {
                        healthLBL.Text = "Health: -/-";
                        idLBL.Text = "ID: -";
                    }

                }

                Thread.Sleep(500);
            }
        }
    }
}
