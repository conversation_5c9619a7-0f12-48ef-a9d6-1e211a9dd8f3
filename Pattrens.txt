gameCharacterAdd = 0x008E05E4; Pattern: 83 3D ? ? ? ? ? 0F 84 ? ? ? ? A1 ? ? ? ? 8B 8D 70 FF FF FF
GCMapxOffset = 0x46E0; Pattern: 03 C2 C1 F8 ? 89 45 D4
GCMapyOffset = 0x46E8; Pattern: 03 C2 C1 F8 ? 89 45 D8
nameAdd = 0x009C9754; Pattern: 8B 15 ? ? ? ? 89 95 7C FF FF FF EB ?
guildAdd = 0x009C978C;  Pattern: 8B 15 ? ? ? ? 89 95 70 FF FF FF
moneyAdd = 0x009CCD28; Pattern: 68 ? ? ? ? E8 ? ? ? ? 83 C4 ? 68 ? ? ? ? 68 ? ? ? ? 8D 4D D8 51 E8 ? ? ? ? 83 C4 ? C7 45 D4 ? ? ? ?
targetAdd = 0x009CADE8; Pattern: 68 ? ? ? ? 8B 55 F8 DB ? ? ? ? ?
HealthX = 0x009C9924; Pattern: 8B 15 ? ? ? ? 33 15 ? ? ? ? 52
HealthY = 0x008E0678; Pattern: 33 15 ? ? ? ? 52
HealthAddresse = 0x009C966C; Pattern: 51 8B 15 ? ? ? ? 33 15 ? ? ? ?
MaxManaAddresse = 0x009C9670; Pattern: 8B 15 ? ? ? ? 03 51 10
CurrManaAddresse = 0x009C9668; Pattern: A1 ? ? ? ? 50 68 ? ? ? ? 68 ? ? ? ? 8D 8D E0 FE FF FF
tapTargetAdd = 0x00433480; Pattern: 55 8B EC 83 EC ? A1 ? ? ? ? 83 E0 ? 8B 0D ? ? ? ? 83 E1 ?
curTargetAddress = 0x008E0610; Pattern: 83 3D ? ? ? ? ? 74 ? E8 ? ? ? ? B8 ? ? ? ?
useSkillAdd = 0x0042E2D0; Pattern: 55 8B EC 81 EC ? ? ? ? 56 E8 ? ? ? ?
isMonsterAlive = 0x008E05EC; Pattern: C6 05 ? ? ? ? ? B8 ? ? ? ? E9 ? ? ? ? 6A ?
tabRange = 0x008E066C; Pattern: D9 ? ? ? ? ? D9 ? ? C7 45 E8 ? ? ? ?
isDropNearAdd = 0x008AF204; Pattern: C6 05 ? ? ? ? ? B8 ? ? ? ? EB ? EB ? 0F B6 0D ? ? ? ?
send_itemAdd = 0x00432180; Pattern: 55 8B EC 51 8B 45 08 50 E8 ? ? ? ? 83 C4 ? 89 45 FC 83 7D FC ? 74 ? B9 ? ? ? ?
targetKindAdd = 0x008CE200; Pattern: 83 3D ? ? ? ? ? 0F 85 ? ? ? ? 6A ? A1 ? ? ? ? 50 E8 ? ? ? ? 83 C4 ? 89 45 E8
checkObjectCrashAdd = 0x006C4BB0; Pattern: 55 8B EC 83 EC ? 83 7D 08 ? 74 ? 83 3D ? ? ? ? ?
zoomAdd = 0x008EE970; Pattern: D9 ? ? ? ? ? C7 85 5C FF FF FF ? ? ? ?
SetTargetByIdAdd = 0x006F94C0; Pattern: 55 8B EC 83 EC ? 83 3D ? ? ? ? ? 75 ? 0F B6 05 ? ? ? ?
InitReactBattlebyStartAdd = 0x009CB252; Pattern: C6 05 ? ? ? ? ? C7 05 ? ? ? ? ? ? ? ? C7 05 ? ? ? ? ? ? ? ? 5D
InitReactBattlebLockAdd = 0x009CB278; Pattern: C7 05 ? ? ? ? ? ? ? ? C7 05 ? ? ? ? ? ? ? ? 5D C3 55
MonsterMinHPOffset = 0x475C; Pattern: DB ? ? ? ? ? 8B 45 F8 DA ? ? ? ? ?
MonsterMaxHPOffset = 0x4760; Pattern: DA ? ? ? ? ? D9 ? ? ? ? ? D9 ? ? ? ? ? 51 D9 ? ? 8B 0D ? ? ? ? E8 ? ? ? ? C7 05 ? ? ? ? ? ? ? ?
MonsterIDOffset = 0x4DBC; Pattern: 55 8B EC 51 89 4D FC A1 ? ? ? ? 83 E0 ? 74 ? 8B 4D FC
NormalAtkFixerAdd = 0x008CE200; Pattern: 83 3D ? ? ? ? ? 0F 85 ? ? ? ? 6A ? A1 ? ? ? ? 50 E8 ? ? ? ? 83 C4 ? 89 45 E8
AtkSpeedAdd = 0x008E05F0; Pattern: D9 ? ? ? ? ? 6A ? 8B 45 F8
cameraRotateAdd = 0x008EE978; Pattern: D9 ? ? ? ? ? D9 ? ? 8D 45 B0
curQuickslot = 0x009CDDB0; Pattern: 68 ? ? ? ? E8 ? ? ? ? 83 C4 ? 8B 55 D8 69 D2 ? ? ? ?
changeQuickslotAdd = 0x006CBC90; Pattern: 55 8B EC 83 3D ? ? ? ? ? 74 ? 83 7D 0C ?
saveQuickslotAdd = 0x006CA4F0; Pattern: 55 8B EC 83 3D ? ? ? ? ? 74 ? 8B 0D ? ? ? ? E8 ? ? ? ? 5D C3 CC CC CC CC CC CC CC 55 8B EC 8B 45 08
openHtmlAdd = 0x006C0310; Pattern: 55 8B EC 81 EC ? ? ? ? 83 7D 08 ? 74 ?
findNameNearAdd = 0x00428690; Pattern: 55 8B EC 83 EC ? A1 ? ? ? ? 89 45 E8 8B 4D E8 8B 11 89 55 E4 C7 45 F4 ? ? ? ?
rotateCamAngleAdd = 0x008EE978; Pattern: D9 ? ? ? ? ? D9 ? ? 8D 45 B0
selectPlayerAdd = 0x006C3D20; Pattern: 55 8B EC 51 8B 45 08 50 E8 ? ? ? ? 83 C4 ? 89 45 FC 83 7D FC ? 75 ? 33 C0
chattingClearAdd = 0x006C3990; Pattern: 55 8B EC 81 EC ? ? ? ? 83 3D ? ? ? ? ? 74 ? A1 ? ? ? ?
addChatMessageAdd = 0x006CC890; Pattern: 55 8B EC 83 3D ? ? ? ? ? 74 ? 8B 45 10 50 8B 4D 0C 51 8A 55 08
addInfoMessageAdd = 0x006CC8C0; Pattern: 55 8B EC 51 83 7D 10 ? 75 ? 68 ? ? ? ?
comboSkillAdd = 0x008E05EE; Pattern: 74 ? 0F B6 0D ? ? ? ? 83 F9 ? 74 ? 83 3D ? ? ? ? ?
playerAroundAdd = 0x009C8218; Pattern: B9 ? ? ? ? E8 ? ? ? ? 68 ? ? ? ? 8D 4D DC

strAdd = 0x009B5B98; Pattern: B8 ? ? ? ? 5D C3 CC CC CC CC CC CC CC CC CC CC CC CC CC 55 8B EC 51
hpAdd = 0x009B5B9A;
intAdd = 0x009B5B9C;
wisAdd = 0x009B5B9E;
agiAdd = 0x009B5BA0;
curstrAdd = 0x009C94A8; Pattern: 66 89 85 DC FE FF FF
curhpAdd = 0x009C94AC; Pattern: 66 89 45 F8 0F B7 05 ? ? ? ?
curintAdd = 0x009C94B0; Pattern: 66 89 45 F4 0F B7 05 ? ? ? ?
curwisAdd = 0x009C94B4; Pattern: 66 89 85 D8 FE FF FF
curagiAdd = 0x009C94B8; Pattern: 66 89 45 FC 83 7D F0 ?
curPointsAdd = 0x009C94FC; Pattern: 0F B7 0D ? ? ? ? 8B 95 CC FE FF FF 0F B7 42 34 2B C8 51 68 ? ? ? ? 8B 4D F0 E8 ? ? ? ? C7 85 88 FE FF FF ? ? ? ? EB ?
updateStatsAdd = 0x00626100; Pattern: 55 8B EC A1 ? ? ? ? 83 E0 ? 75 ?
updateStats2Add = 0x00626F80; Pattern: 55 8B EC 81 EC ? ? ? ? A1 ? ? ? ? 33 C5 89 45 EC 89 8D CC FE FF FF