﻿using KeyAuth;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Heth_AIO
{
    public partial class Login : Form
    {

        //drag stuff
        private bool mouseDown;
        private Point lastLocation;

        [DllImport("Gdi32.dll", EntryPoint = "DeleteObject")]
        public static extern bool DeleteObject(IntPtr hObject);

        [DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn
        (
          int nLeftRect,     // x-coordinate of upper-left corner
          int nTopRect,      // y-coordinate of upper-left corner
          int nRightRect,    // x-coordinate of lower-right corner
          int nBottomRect,   // y-coordinate of lower-right corner
          int nWidthEllipse, // width of ellipse
          int nHeightEllipse // height of ellipse
        );


        public static api KeyAuthApp = new api(
            name: "<PERSON><PERSON> <PERSON><PERSON>", // App name
            ownerid: "BXlubZxGqn", // Account ID
            version: "1.0" // Application version. Used for automatic downloads see video here https://www.youtube.com/watch?v=kW195PLCBKs
                           //path: @"Your_Path_Here" // (OPTIONAL) see tutorial here https://www.youtube.com/watch?v=I9rxt821gMk&t=1s
        );

        public Login()
        {
            InitializeComponent();

            //corner edges
            this.FormBorderStyle = FormBorderStyle.None;
            IntPtr handle = CreateRoundRectRgn(0, 0, Width, Height, 20, 20);
            if (handle == IntPtr.Zero)
#pragma warning disable CS0642 // Possible mistaken empty statement
                ; // error with CreateRoundRectRgn
#pragma warning restore CS0642 // Possible mistaken empty statement
            Region = System.Drawing.Region.FromHrgn(handle);
            DeleteObject(handle);
        }


        private async void Login_Load(object sender, EventArgs e)
        {
            
            await KeyAuthApp.init();



            if (!KeyAuthApp.response.success)
            {
                MessageBox.Show(KeyAuthApp.response.message);
                Environment.Exit(0);
            }

            if (KeyAuthApp.response.success)
            {
                panel2.Visible = false;
            }

            //if (await KeyAuthApp.checkblack()) // check if hwid is blacklisted
            //{
            //    MessageBox.Show("You are blacklisted, bye.");
            //    Environment.Exit(0);
           // }


            txtKey.Text = Properties.Settings.Default.licenseKey;
            //await KeyAuthApp.check();


        }

        private void label1_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void label1_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }

        private void label1_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }

        private void Login_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            lastLocation = e.Location;
        }

        private void Login_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }

        private void Login_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown)
            {
                this.Location = new Point(
                    (this.Location.X - lastLocation.X) + e.X, (this.Location.Y - lastLocation.Y) + e.Y);

                this.Update();
            }
        }

        private void minBTN_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void extBTN_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void txtKey_Enter(object sender, EventArgs e)
        {
            if (txtKey.Text == "xxxx-xxxx-xxxx-xxxx-xxxx")
            {
                txtKey.ForeColor = Color.Silver;
                txtKey.Text = "";
            }
        }

        private void txtKey_Leave(object sender, EventArgs e)
        {
            if (txtKey.Text == "")
            {
                txtKey.ForeColor = Color.DimGray;
                txtKey.Text = "xxxx-xxxx-xxxx-xxxx-xxxx";
            }
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
       
            //KeyAuthApp.login("Heth","x");
            await KeyAuthApp.license(txtKey.Text);
            if (KeyAuthApp.response.success)
            {
                MainApp main = new MainApp();
                main.Show();
                this.Hide();
            }
            else
                statusLbl.Text = KeyAuthApp.response.message;
        }

        private void Login_FormClosing(object sender, FormClosingEventArgs e)
        {
            Properties.Settings.Default.licenseKey = txtKey.Text;
            Properties.Settings.Default.Save();

            Environment.Exit(0);
        }

        private void Login_Shown(object sender, EventArgs e)
        {

        }
    }
}
