<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MemorySharp</name>
    </assembly>
    <members>
        <member name="T:Binarysharp.MemoryManagement.Assembly.Assembler.Fasm32Assembler">
            <summary>
            Implement Fasm.NET compiler for 32-bit development.
            More info: https://github.com/ZenLulz/Fasm.NET
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.Assembler.Fasm32Assembler.Assemble(System.String)">
            <summary>
            Assemble the specified assembly code.
            </summary>
            <param name="asm">The assembly code.</param>
            <returns>An array of bytes containing the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.Assembler.Fasm32Assembler.Assemble(System.String,System.IntPtr)">
            <summary>
            Assemble the specified assembly code at a base address.
            </summary>
            <param name="asm">The assembly code.</param>
            <param name="baseAddress">The address where the code is rebased.</param>
            <returns>An array of bytes containing the assembly code.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.Assembler.IAssembler">
            <summary>
            Interface defining an assembler.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.Assembler.IAssembler.Assemble(System.String)">
            <summary>
            Assemble the specified assembly code.
            </summary>
            <param name="asm">The assembly code.</param>
            <returns>An array of bytes containing the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.Assembler.IAssembler.Assemble(System.String,System.IntPtr)">
            <summary>
            Assemble the specified assembly code at a base address.
            </summary>
            <param name="asm">The assembly code.</param>
            <param name="baseAddress">The address where the code is rebased.</param>
            <returns>An array of bytes containing the assembly code.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.AssemblyFactory">
            <summary>
            Class providing tools for manipulating assembly code.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.MemorySharp">
            <summary>
            The reference of the <see cref="F:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.MemorySharp"/> object.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Assembler">
            <summary>
            The assembler used by the factory.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.#ctor(Binarysharp.MemoryManagement.MemorySharp)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Assembly.AssemblyFactory"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="F:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.MemorySharp"/> object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.BeginTransaction(System.IntPtr,System.Boolean)">
            <summary>
            Begins a new transaction to inject and execute assembly code into the process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is injected.</param>
            <param name="autoExecute">Indicates whether the assembly code is executed once the object is disposed.</param>
            <returns>The return value is a new transaction.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.BeginTransaction(System.Boolean)">
            <summary>
            Begins a new transaction to inject and execute assembly code into the process.
            </summary>
            <param name="autoExecute">Indicates whether the assembly code is executed once the object is disposed.</param>
            <returns>The return value is a new transaction.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Assembly.AssemblyFactory"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Execute``1(System.IntPtr)">
            <summary>
            Executes the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Execute(System.IntPtr)">
            <summary>
            Executes the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Execute``1(System.IntPtr,System.Object)">
            <summary>
            Executes the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <param name="parameter">The parameter used to execute the assembly code.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Execute(System.IntPtr,System.Object)">
            <summary>
            Executes the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <param name="parameter">The parameter used to execute the assembly code.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Execute``1(System.IntPtr,Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions,System.Object[])">
            <summary>
            Executes the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <param name="callingConvention">The calling convention used to execute the assembly code with the parameters.</param>
            <param name="parameters">An array of parameters used to execute the assembly code.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Execute(System.IntPtr,Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions,System.Object[])">
            <summary>
            Executes the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <param name="callingConvention">The calling convention used to execute the assembly code with the parameters.</param>
            <param name="parameters">An array of parameters used to execute the assembly code.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.ExecuteAsync``1(System.IntPtr)">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.ExecuteAsync(System.IntPtr)">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.ExecuteAsync``1(System.IntPtr,System.Object)">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <param name="parameter">The parameter used to execute the assembly code.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.ExecuteAsync(System.IntPtr,System.Object)">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <param name="parameter">The parameter used to execute the assembly code.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.ExecuteAsync``1(System.IntPtr,Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions,System.Object[])">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <param name="callingConvention">The calling convention used to execute the assembly code with the parameters.</param>
            <param name="parameters">An array of parameters used to execute the assembly code.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.ExecuteAsync(System.IntPtr,Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions,System.Object[])">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="address">The address where the assembly code is located.</param>
            <param name="callingConvention">The calling convention used to execute the assembly code with the parameters.</param>
            <param name="parameters">An array of parameters used to execute the assembly code.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Inject(System.String,System.IntPtr)">
            <summary>
            Assembles mnemonics and injects the corresponding assembly code into the remote process at the specified address.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Inject(System.String[],System.IntPtr)">
            <summary>
            Assembles mnemonics and injects the corresponding assembly code into the remote process at the specified address.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Inject(System.String)">
            <summary>
            Assembles mnemonics and injects the corresponding assembly code into the remote process.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <returns>The address where the assembly code is injected.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.Inject(System.String[])">
            <summary>
            Assembles mnemonics and injects the corresponding assembly code into the remote process.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <returns>The address where the assembly code is injected.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecute``1(System.String,System.IntPtr)">
            <summary>
            Assembles, injects and executes the mnemonics into the remote process at the specified address.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecute(System.String,System.IntPtr)">
            <summary>
            Assembles, injects and executes the mnemonics into the remote process at the specified address.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecute``1(System.String[],System.IntPtr)">
            <summary>
            Assembles, injects and executes the mnemonics into the remote process at the specified address.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecute(System.String[],System.IntPtr)">
            <summary>
            Assembles, injects and executes the mnemonics into the remote process at the specified address.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecute``1(System.String)">
            <summary>
            Assembles, injects and executes the mnemonics into the remote process.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecute(System.String)">
            <summary>
            Assembles, injects and executes the mnemonics into the remote process.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecute``1(System.String[])">
            <summary>
            Assembles, injects and executes the mnemonics into the remote process.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecute(System.String[])">
            <summary>
            Assembles, injects and executes the mnemonics into the remote process.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecuteAsync``1(System.String,System.IntPtr)">
            <summary>
            Assembles, injects and executes asynchronously the mnemonics into the remote process at the specified address.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecuteAsync(System.String,System.IntPtr)">
            <summary>
            Assembles, injects and executes asynchronously the mnemonics into the remote process at the specified address.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecuteAsync``1(System.String[],System.IntPtr)">
            <summary>
            Assembles, injects and executes asynchronously the mnemonics into the remote process at the specified address.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecuteAsync(System.String[],System.IntPtr)">
            <summary>
            Assembles, injects and executes asynchronously the mnemonics into the remote process at the specified address.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <param name="address">The address where the assembly code is injected.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecuteAsync``1(System.String)">
            <summary>
            Assembles, injects and executes asynchronously the mnemonics into the remote process.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecuteAsync(System.String)">
            <summary>
            Assembles, injects and executes asynchronously the mnemonics into the remote process.
            </summary>
            <param name="asm">The mnemonics to inject.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecuteAsync``1(System.String[])">
            <summary>
            Assembles, injects and executes asynchronously the mnemonics into the remote process.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyFactory.InjectAndExecuteAsync(System.String[])">
            <summary>
            Assembles, injects and executes asynchronously the mnemonics into the remote process.
            </summary>
            <param name="asm">An array containing the mnemonics to inject.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction">
            <summary>
            Class representing a transaction where the user can insert mnemonics.
            The code is then executed when the object is disposed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.MemorySharp">
            <summary>
            The reference of the <see cref="F:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.MemorySharp"/> object.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.Mnemonics">
            <summary>
            The builder contains all the mnemonics inserted by the user.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.ExitCode">
            <summary>
            The exit code of the thread created to execute the assembly code.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.Address">
            <summary>
            The address where to assembly code is assembled.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.IsAutoExecuted">
            <summary>
            Gets the value indicating whether the assembly code is executed once the object is disposed.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.IntPtr,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="F:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.MemorySharp"/> object.</param>
            <param name="address">The address where the assembly code is injected.</param>
            <param name="autoExecute">Indicates whether the assembly code is executed once the object is disposed.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="F:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.MemorySharp"/> object.</param>
            <param name="autoExecute">Indicates whether the assembly code is executed once the object is disposed.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.AddLine(System.String,System.Object[])">
            <summary>
            Adds a mnemonic to the transaction.
            </summary>
            <param name="asm">A composite format string.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.Assemble">
            <summary>
            Assemble the assembly code of this transaction.
            </summary>
            <returns>An array of bytes containing the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.Clear">
            <summary>
            Removes all mnemonics from the transaction.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.GetExitCode``1">
            <summary>
            Gets the termination status of the thread.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.AssemblyTransaction.InsertLine(System.Int32,System.String,System.Object[])">
            <summary>
            Inserts a mnemonic to the transaction at a given index.
            </summary>
            <param name="index">The position in the transaction where insertion begins.</param>
            <param name="asm">A composite format string.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventionSelector">
            <summary>
            Static class providing calling convention instances.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventionSelector.Get(Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions)">
            <summary>
            Gets a calling convention object according the given type.
            </summary>
            <param name="callingConvention">The type of calling convention to get.</param>
            <returns>The return value is a singleton of a <see cref="T:Binarysharp.MemoryManagement.Assembly.CallingConvention.ICallingConvention"/> child.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.CallingConvention.CdeclCallingConvention">
            <summary>
            Define the C Declaration Calling Convention.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.CdeclCallingConvention.Name">
            <summary>
            The name of the calling convention.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.CdeclCallingConvention.Cleanup">
            <summary>
            Defines which function performs the clean-up task.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.CdeclCallingConvention.FormatParameters(System.IntPtr[])">
            <summary>
            Formats the given parameters to call a function.
            </summary>
            <param name="parameters">An array of parameters.</param>
            <returns>The mnemonics to pass the parameters.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.CdeclCallingConvention.FormatCalling(System.IntPtr)">
            <summary>
            Formats the call of a given function.
            </summary>
            <param name="function">The function to call.</param>
            <returns>The mnemonics to call the function.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.CdeclCallingConvention.FormatCleaning(System.Int32)">
            <summary>
            Formats the cleaning of a given number of parameters.
            </summary>
            <param name="nbParameters">The number of parameters to clean.</param>
            <returns>The mnemonics to clean a given number of parameters.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.CallingConvention.ThiscallCallingConvention">
            <summary>
            Define the 'This' Calling Convention.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.ThiscallCallingConvention.Name">
            <summary>
            The name of the calling convention.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.ThiscallCallingConvention.Cleanup">
            <summary>
            Defines which function performs the clean-up task.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.ThiscallCallingConvention.FormatParameters(System.IntPtr[])">
            <summary>
            Formats the given parameters to call a function. The 'this' pointer must be passed in first.
            </summary>
            <param name="parameters">An array of parameters.</param>
            <returns>The mnemonics to pass the parameters.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.ThiscallCallingConvention.FormatCalling(System.IntPtr)">
            <summary>
            Formats the call of a given function.
            </summary>
            <param name="function">The function to call.</param>
            <returns>The mnemonics to call the function.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.ThiscallCallingConvention.FormatCleaning(System.Int32)">
            <summary>
            Formats the cleaning of a given number of parameters.
            </summary>
            <param name="nbParameters">The number of parameters to clean.</param>
            <returns>The mnemonics to clean a given number of parameters.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.CallingConvention.FastcallCallingConvention">
            <summary>
            Define the Fast Calling Convention (aka __msfastcall).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.FastcallCallingConvention.Name">
            <summary>
            The name of the calling convention.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.FastcallCallingConvention.Cleanup">
            <summary>
            Defines which function performs the clean-up task.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.FastcallCallingConvention.FormatParameters(System.IntPtr[])">
            <summary>
            Formats the given parameters to call a function.
            </summary>
            <param name="parameters">An array of parameters.</param>
            <returns>The mnemonics to pass the parameters.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.FastcallCallingConvention.FormatCalling(System.IntPtr)">
            <summary>
            Formats the call of a given function.
            </summary>
            <param name="function">The function to call.</param>
            <returns>The mnemonics to call the function.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.FastcallCallingConvention.FormatCleaning(System.Int32)">
            <summary>
            Formats the cleaning of a given number of parameters.
            </summary>
            <param name="nbParameters">The number of parameters to clean.</param>
            <returns>The mnemonics to clean a given number of parameters.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.CallingConvention.StdcallCallingConvention">
            <summary>
            Define the Standard Calling Convention.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.StdcallCallingConvention.Name">
            <summary>
            The name of the calling convention.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.StdcallCallingConvention.Cleanup">
            <summary>
            Defines which function performs the clean-up task.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.StdcallCallingConvention.FormatParameters(System.IntPtr[])">
            <summary>
            Formats the given parameters to call a function.
            </summary>
            <param name="parameters">An array of parameters.</param>
            <returns>The mnemonics to pass the parameters.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.StdcallCallingConvention.FormatCalling(System.IntPtr)">
            <summary>
            Formats the call of a given function.
            </summary>
            <param name="function">The function to call.</param>
            <returns>The mnemonics to call the function.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.StdcallCallingConvention.FormatCleaning(System.Int32)">
            <summary>
            Formats the cleaning of a given number of parameters.
            </summary>
            <param name="nbParameters">The number of parameters to clean.</param>
            <returns>The mnemonics to clean a given number of parameters.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.CallingConvention.ICallingConvention">
            <summary>
            Interface defining a calling convention.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.ICallingConvention.Name">
            <summary>
            The name of the calling convention.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Assembly.CallingConvention.ICallingConvention.Cleanup">
            <summary>
            Defines which function performs the clean-up task.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.ICallingConvention.FormatParameters(System.IntPtr[])">
            <summary>
            Formats the given parameters to call a function.
            </summary>
            <param name="parameters">An array of parameters.</param>
            <returns>The mnemonics to pass the parameters.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.ICallingConvention.FormatCalling(System.IntPtr)">
            <summary>
            Formats the call of a given function.
            </summary>
            <param name="function">The function to call.</param>
            <returns>The mnemonics to call the function.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Assembly.CallingConvention.ICallingConvention.FormatCleaning(System.Int32)">
            <summary>
            Formats the cleaning of a given number of parameters.
            </summary>
            <param name="nbParameters">The number of parameters to clean.</param>
            <returns>The mnemonics to clean a given number of parameters.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions">
            <summary>
            A list of calling conventions.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions.Cdecl">
            <summary>
            Name       : C Declaration Calling Convention
            Clean-up   : Caller
            Parameters : Passed on the stack in reverse order
            Ret. value : Returned in the EAX register
            Notes      : Widely used by the compiler GCC
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions.Stdcall">
            <summary>
            Name       : Standard Calling Convention
            Clean-up   : Callee
            Parameters : Passed on the stack in reverse order
            Ret. value : Returned in the EAX register
            Notes      : Convention created by Microsoft, used in the Win32 API
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions.Fastcall">
            <summary>
            Name       : Fast Calling Convention (aka __msfastcall)
            Clean-up   : Callee
            Parameters : The first two parameters are placed in the ECX and EDX registers respectively.
                         Any remaining parameters are placed on the stack in reverse order.
            Ret. Value : Returned in the EAX register
            Notes      : A variation of the stdcall convention
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions.Thiscall">
            <summary>
            Name       : This Calling Convention
            Clean-up   : Callee
            Parameters : The 'this' pointer is placed in the ECX register.
                         Parameters are placed on the stack in reverse order.
            Ret. Value : Returned in the EAX register
            Notes      : Used for object-oriented programming by Microsoft Visual C++
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Assembly.CallingConvention.CleanupTypes">
            <summary>
            A list of type of clean-up available in calling conventions.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.CallingConvention.CleanupTypes.Callee">
            <summary>
            The clean-up task is performed by the called function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Assembly.CallingConvention.CleanupTypes.Caller">
            <summary>
            The clean-up task is performed by the caller function. 
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Helpers.ApplicationFinder">
            <summary>
            Static helper class providing tools for finding applications.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Helpers.ApplicationFinder.TopLevelWindows">
            <summary>
            Gets all top-level windows on the screen.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Helpers.ApplicationFinder.Windows">
            <summary>
            Gets all the windows on the screen.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.ApplicationFinder.FromProcessId(System.Int32)">
            <summary>
            Returns a new <see cref="T:System.Diagnostics.Process"/> component, given the identifier of a process.
            </summary>
            <param name="processId">The system-unique identifier of a process resource.</param>
            <returns>A <see cref="T:System.Diagnostics.Process"/> component that is associated with the local process resource identified by the processId parameter.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.ApplicationFinder.FromProcessName(System.String)">
            <summary>
            Creates an collection of new <see cref="T:System.Diagnostics.Process"/> components and associates them with all the process resources that share the specified process name.
            </summary>
            <param name="processName">The friendly name of the process.</param>
            <returns>A collection of type <see cref="T:System.Diagnostics.Process"/> that represents the process resources running the specified application or file.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.ApplicationFinder.FromWindowClassName(System.String)">
            <summary>
            Creates a collection of new <see cref="T:System.Diagnostics.Process"/> components and associates them with all the process resources that share the specified class name.
            </summary>
            <param name="className">The class name string.</param>
            <returns>A collection of type <see cref="T:System.Diagnostics.Process"/> that represents the process resources running the specified application or file.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.ApplicationFinder.FromWindowHandle(System.IntPtr)">
            <summary>
            Retrieves a new <see cref="T:System.Diagnostics.Process"/> component that created the window. 
            </summary>
            <param name="windowHandle">A handle to the window.</param>
            <returns>A <see cref="T:System.Diagnostics.Process"/>A <see cref="T:System.Diagnostics.Process"/> component that is associated with the specified window handle.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.ApplicationFinder.FromWindowTitle(System.String)">
            <summary>
            Creates a collection of new <see cref="T:System.Diagnostics.Process"/> components and associates them with all the process resources that share the specified window title.
            </summary>
            <param name="windowTitle">The window title string.</param>
            <returns>A collection of type <see cref="T:System.Diagnostics.Process"/> that represents the process resources running the specified application or file.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.ApplicationFinder.FromWindowTitleContains(System.String)">
            <summary>
            Creates a collection of new <see cref="T:System.Diagnostics.Process"/> components and associates them with all the process resources that contain the specified window title.
            </summary>
            <param name="windowTitle">A part a window title string.</param>
            <returns>A collection of type <see cref="T:System.Diagnostics.Process"/> that represents the process resources running the specified application or file.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Helpers.HandleManipulator">
            <summary>
            Static helper class providing tools for manipulating handles.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.HandleManipulator.CloseHandle(System.IntPtr)">
            <summary>
            Closes an open object handle.
            </summary>
            <param name="handle">A valid handle to an open object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.HandleManipulator.HandleToProcess(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Converts an handle into a <see cref="T:System.Diagnostics.Process"/> object assuming this is a process handle.
            </summary>
            <param name="processHandle">A valid handle to an opened process.</param>
            <returns>A <see cref="T:System.Diagnostics.Process"/> object from the specified handle.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.HandleManipulator.HandleToProcessId(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Converts an handle into a process id assuming this is a process handle.
            </summary>
            <param name="processHandle">A valid handle to an opened process.</param>
            <returns>A process id from the specified handle.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.HandleManipulator.HandleToThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Converts an handle into a <see cref="T:System.Diagnostics.ProcessThread"/> object assuming this is a thread handle.
            </summary>
            <param name="threadHandle">A valid handle to an opened thread.</param>
            <returns>A <see cref="T:System.Diagnostics.ProcessThread"/> object from the specified handle.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.HandleManipulator.HandleToThreadId(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Converts an handle into a thread id assuming this is a thread handle.
            </summary>
            <param name="threadHandle">A valid handle to an opened thread.</param>
            <returns>A thread id from the specified handle.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.HandleManipulator.ValidateAsArgument(System.IntPtr,System.String)">
            <summary>
            Validates an handle to fit correctly as argument.
            </summary>
            <param name="handle">A handle to validate.</param>
            <param name="argumentName">The name of the argument that represents the handle in its original function.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.HandleManipulator.ValidateAsArgument(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.String)">
            <summary>
            Validates an handle to fit correctly as argument.
            </summary>
            <param name="handle">A handle to validate.</param>
            <param name="argumentName">The name of the argument that represents the handle in its original function.</param>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Helpers.Randomizer">
            <summary>
            Static helper class providing tools for generating random numbers or strings.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Helpers.Randomizer.Random">
            <summary>
            Provides random engine.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Helpers.Randomizer.AllowedChars">
            <summary>
            Allowed characters in random strings.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.Randomizer.GenerateNumber(System.Int32,System.Int32)">
            <summary>
            Returns a random number within a specified range.
            </summary>
            <param name="minValue">The inclusive lower bound of the random number returned.</param>
            <param name="maxValue">The exclusive upper bound of the random number returned. maxValue must be greater than or equal to minValue.</param>
            <returns>A 32-bit signed integer greater than or equal to minValue and less than maxValue.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.Randomizer.GenerateNumber(System.Int32)">
            <summary>
            Returns a nonnegative random number less than the specified maximum.
            </summary>
            <param name="maxValue">The exclusive upper bound of the random number to be generated. maxValue must be greater than or equal to zero.</param>
            <returns>A 32-bit signed integer greater than or equal to zero, and less than maxValue.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.Randomizer.GenerateNumber">
            <summary>
            Returns a nonnegative random number.
            </summary>
            <returns>A 32-bit signed integer greater than or equal to zero and less than <see cref="F:System.Int32.MaxValue"/>.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.Randomizer.GenerateString(System.Int32,System.Int32)">
            <summary>
            Returns a random string where its size is within a specified range.
            </summary>
            <param name="minSize">The inclusive lower bound of the size of the string returned.</param>
            <param name="maxSize">The exclusive upper bound of the size of the string returned.</param>
            <returns></returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.Randomizer.GenerateGuid">
            <summary>
            Initializes a new instance of the <see cref="T:System.Guid"/> structure.
            </summary>
            <returns>A new <see cref="T:System.Guid"/> object.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Helpers.SerializationHelper">
            <summary>
            Static helper class providing tools for serializing/deserializing objects.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.SerializationHelper.ExportToXmlFile``1(``0,System.String,System.Text.Encoding)">
            <summary>
            Serializes the specified object and writes the XML document to the specified path.
            </summary>
            <typeparam name="T">The type of the object to serialize.</typeparam>
            <param name="obj">The object to serialize.</param>
            <param name="path">The path where the file is saved.</param>
            <param name="encoding">The encoding to generate.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.SerializationHelper.ExportToXmlFile``1(``0,System.String)">
            <summary>
            Serializes the specified object and writes the XML document to the specified path using <see cref="P:System.Text.Encoding.UTF8"/> encoding.
            </summary>
            <typeparam name="T">The type of the object to serialize.</typeparam>
            <param name="obj">The object to serialize.</param>
            <param name="path">The path where the file is saved.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.SerializationHelper.ExportToXmlString``1(``0)">
            <summary>
            Serializes the specified object and returns the XML document.
            </summary>
            <typeparam name="T">The type of the object to serialize.</typeparam>
            <param name="obj">The object to serialize.</param>
            <returns>XML document of the serialized object.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.SerializationHelper.ImportFromXmlFile``1(System.String,System.Text.Encoding)">
            <summary>
            Deserializes the specified file into an object.
            </summary>
            <typeparam name="T">The type of the object to deserialize.</typeparam>
            <param name="path">The path where the object is read.</param>
            <param name="encoding">The character encoding to use. </param>
            <returns>The deserialized object.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.SerializationHelper.ImportFromXmlFile``1(System.String)">
            <summary>
            Deserializes the specified file into an object using <see cref="P:System.Text.Encoding.UTF8"/> encoding.
            </summary>
            <typeparam name="T">The type of the object to deserialize.</typeparam>
            <param name="path">The path where the object is read.</param>
            <returns>The deserialized object.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Helpers.SerializationHelper.ImportFromXmlString``1(System.String)">
            <summary>
            Deserializes the XML document to the specified object.
            </summary>
            <typeparam name="T">The type of the object to deserialize.</typeparam>
            <param name="serializedObj">The string representing the serialized object.</param>
            <returns>The deserialized object.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Helpers.Singleton`1">
            <summary>
            Static helper used to create or get a singleton from another class.
            </summary>
            <typeparam name="T">The type to create or get a singleton.</typeparam>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Helpers.Singleton`1.Instance">
            <summary>
            Gets the singleton of the given type.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Internals.IApplicableElement">
            <summary>
            Defines an element able to be activated in the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.IApplicableElement.IsEnabled">
            <summary>
            States if the element is enabled.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.IApplicableElement.Disable">
            <summary>
            Disables the element.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.IApplicableElement.Enable">
            <summary>
            Enables the element.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Internals.IDisposableState">
            <summary>
            Defines an IDisposable interface with a known state.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.IDisposableState.IsDisposed">
            <summary>
            Gets a value indicating whether the element is disposed.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.IDisposableState.MustBeDisposed">
            <summary>
            Gets a value indicating whether the element must be disposed when the Garbage Collector collects the object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Internals.IFactory">
            <summary>
            Define a factory for the library.
            </summary>
            <remarks>At the moment, the factories are just disposable.</remarks>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Internals.IMarshalledValue">
            <summary>
            Interface representing a value within the memory of a remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.IMarshalledValue.Allocated">
            <summary>
            The memory allocated where the value is fully written if needed. It can be unused.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.IMarshalledValue.Reference">
            <summary>
            The reference of the value. It can be directly the value or a pointer.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Internals.INamedElement">
            <summary>
            Defines a element with a name.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.INamedElement.Name">
            <summary>
            The name of the element.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Internals.Manager`1">
            <summary>
            Class managing objects implementing <see cref="T:Binarysharp.MemoryManagement.Internals.INamedElement"/> interface.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Internals.Manager`1.InternalItems">
            <summary>
            The collection of the elements (writable).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.Manager`1.Items">
            <summary>
            The collection of the elements.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.Manager`1.DisableAll">
            <summary>
            Disables all items in the manager.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.Manager`1.EnableAll">
            <summary>
            Enables all items in the manager.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.Manager`1.Remove(System.String)">
            <summary>
            Removes an element by its name in the manager.
            </summary>
            <param name="name">The name of the element to remove.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.Manager`1.Remove(`0)">
            <summary>
            Remove a given element.
            </summary>
            <param name="item">The element to remove.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.Manager`1.RemoveAll">
            <summary>
            Removes all the elements in the manager.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Internals.MarshalType`1">
            <summary>
            Static class providing tools for extracting information related to types.
            </summary>
            <typeparam name="T">Type to analyze.</typeparam>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.MarshalType`1.CanBeStoredInRegisters">
            <summary>
            Gets if the type can be stored in a registers (for example ACX, ECX, ...).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.MarshalType`1.IsIntPtr">
            <summary>
            State if the type is <see cref="T:System.IntPtr"/>.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.MarshalType`1.RealType">
            <summary>
            The real type.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.MarshalType`1.Size">
            <summary>
            The size of the type.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.MarshalType`1.TypeCode">
            <summary>
            The typecode of the type.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.MarshalType`1.#cctor">
            <summary>
            Initializes static information related to the specified type.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.MarshalType`1.ObjectToByteArray(`0)">
            <summary>
            Marshals a managed object to an array of bytes.
            </summary>
            <param name="obj">The object to marshal.</param>
            <returns>A array of bytes corresponding to the managed object.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.MarshalType`1.ByteArrayToObject(System.Byte[],System.Int32)">
            <summary>
            Marshals an array of byte to a managed object.
            </summary>
            <param name="byteArray">The array of bytes corresponding to a managed object.</param>
            <param name="index">[Optional] Where to start the conversion of bytes to the managed object.</param>
            <returns>A managed object.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.MarshalType`1.PtrToObject(Binarysharp.MemoryManagement.MemorySharp,System.IntPtr)">
            <summary>
            Converts a pointer to a given type. This function converts the value of the pointer or the pointed value,
            according if the data type is primitive or reference.
            </summary>
            <param name="memorySharp">The concerned process.</param>
            <param name="pointer">The pointer to convert.</param>
            <returns>The return value is the pointer converted to the given data type.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Internals.MarshalValue">
            <summary>
            The factory to create instance of the <see cref="T:Binarysharp.MemoryManagement.Internals.MarshalledValue`1"/> class.
            </summary>
            <remarks>
            A factory pattern is used because C# 5.0 constructor doesn't support type inference.
            More info from Eric Lippert here : http://stackoverflow.com/questions/3570167/why-cant-the-c-sharp-constructor-infer-type
            </remarks>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.MarshalValue.Marshal``1(Binarysharp.MemoryManagement.MemorySharp,``0)">
            <summary>
            Marshals a given value into the remote process.
            </summary>
            <typeparam name="T">The type of the value. It can be a primitive or reference data type.</typeparam>
            <param name="memorySharp">The concerned process.</param>
            <param name="value">The value to marshal.</param>
            <returns>The return value is an new instance of the <see cref="T:Binarysharp.MemoryManagement.Internals.MarshalledValue`1"/> class.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Internals.MarshalledValue`1">
            <summary>
            Class marshalling a value into the remote process.
            </summary>
            <typeparam name="T">The type of the value. It can be a primitive or reference data type.</typeparam>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.MemorySharp">
            <summary>
            The reference of the <see cref="F:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.MemorySharp"/> object.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.Allocated">
            <summary>
            The memory allocated where the value is fully written if needed. It can be unused.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.Reference">
            <summary>
            The reference of the value. It can be directly the value or a pointer.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.Value">
            <summary>
            The initial value.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.#ctor(Binarysharp.MemoryManagement.MemorySharp,`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Internals.MarshalledValue`1"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="F:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.MemorySharp"/> object.</param>
            <param name="value">The value to marshal.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.Finalize">
            <summary>
            Frees resources and perform other cleanup operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Memory.RemoteAllocation"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Internals.MarshalledValue`1.Marshal">
            <summary>
            Marshals the value into the remote process.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.MemorySharp">
            <summary>
            Class for memory editing a remote process.
            </summary>
        </member>
        <member name="E:Binarysharp.MemoryManagement.MemorySharp.OnDispose">
            <summary>
            Raises when the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object is disposed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.MemorySharp.Factories">
            <summary>
            The factories embedded inside the library.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Assembly">
            <summary>
            Factory for generating assembly code.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.IsDebugged">
            <summary>
            Gets whether the process is being debugged.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.IsRunning">
            <summary>
            State if the process is running.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Handle">
            <summary>
            The remote process handle opened with all rights.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Memory">
            <summary>
            Factory for manipulating memory space.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Modules">
            <summary>
            Factory for manipulating modules and libraries.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Native">
            <summary>
            Provide access to the opened process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Peb">
            <summary>
            The Process Environment Block of the process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Pid">
            <summary>
            Gets the unique identifier for the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Item(System.String)">
            <summary>
            Gets the specified module in the remote process.
            </summary>
            <param name="moduleName">The name of module (not case sensitive).</param>
            <returns>A new instance of a <see cref="T:Binarysharp.MemoryManagement.Modules.RemoteModule"/> class.</returns>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Item(System.IntPtr,System.Boolean)">
            <summary>
            Gets a pointer to the specified address in the remote process.
            </summary>
            <param name="address">The address pointed.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <returns>A new instance of a <see cref="T:Binarysharp.MemoryManagement.Memory.RemotePointer"/> class.</returns>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Threads">
            <summary>
            Factory for manipulating threads.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.MemorySharp.Windows">
            <summary>
            Factory for manipulating windows.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.#ctor(System.Diagnostics.Process)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> class.
            </summary>
            <param name="process">Process to open.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> class.
            </summary>
            <param name="processId">Process id of the process to open.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Finalize">
            <summary>
            Frees resources and perform other cleanup operations before it is reclaimed by garbage collection. 
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Equals(Binarysharp.MemoryManagement.MemorySharp)">
            <summary>
            Returns a value indicating whether this instance is equal to a specified object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.GetHashCode">
            <summary>
            Serves as a hash function for a particular type. 
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.MakeAbsolute(System.IntPtr)">
            <summary>
            Makes an absolute address from a relative one based on the main module.
            </summary>
            <param name="address">The relative address.</param>
            <returns>The absolute address.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.MakeRelative(System.IntPtr)">
            <summary>
            Makes a relative address from an absolute one based on the main module.
            </summary>
            <param name="address">The absolute address.</param>
            <returns>The relative address.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Read``1(System.IntPtr,System.Boolean)">
            <summary>
            Reads the value of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="address">The address where the value is read.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <returns>A value.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Read``1(System.Enum,System.Boolean)">
            <summary>
            Reads the value of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="address">The address where the value is read.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <returns>A value.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Read``1(System.IntPtr,System.Int32,System.Boolean)">
            <summary>
            Reads an array of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
            <param name="address">The address where the values is read.</param>
            <param name="count">The number of cells in the array.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <returns>An array.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Read``1(System.Enum,System.Int32,System.Boolean)">
            <summary>
            Reads an array of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
            <param name="address">The address where the values is read.</param>
            <param name="count">The number of cells in the array.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <returns>An array.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.ReadBytes(System.IntPtr,System.Int32,System.Boolean)">
            <summary>
            Reads an array of bytes in the remote process.
            </summary>
            <param name="address">The address where the array is read.</param>
            <param name="count">The number of cells.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <returns>The array of bytes.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.ReadString(System.IntPtr,System.Text.Encoding,System.Boolean,System.Int32)">
            <summary>
            Reads a string with a specified encoding in the remote process.
            </summary>
            <param name="address">The address where the string is read.</param>
            <param name="encoding">The encoding used.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <param name="maxLength">[Optional] The number of maximum bytes to read. The string is automatically cropped at this end ('\0' char).</param>
            <returns>The string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.ReadString(System.Enum,System.Text.Encoding,System.Boolean,System.Int32)">
            <summary>
            Reads a string with a specified encoding in the remote process.
            </summary>
            <param name="address">The address where the string is read.</param>
            <param name="encoding">The encoding used.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <param name="maxLength">[Optional] The number of maximum bytes to read. The string is automatically cropped at this end ('\0' char).</param>
            <returns>The string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.ReadString(System.IntPtr,System.Boolean,System.Int32)">
            <summary>
            Reads a string using the encoding UTF8 in the remote process.
            </summary>
            <param name="address">The address where the string is read.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <param name="maxLength">[Optional] The number of maximum bytes to read. The string is automatically cropped at this end ('\0' char).</param>
            <returns>The string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.ReadString(System.Enum,System.Boolean,System.Int32)">
            <summary>
            Reads a string using the encoding UTF8 in the remote process.
            </summary>
            <param name="address">The address where the string is read.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
            <param name="maxLength">[Optional] The number of maximum bytes to read. The string is automatically cropped at this end ('\0' char).</param>
            <returns>The string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Write``1(System.IntPtr,``0,System.Boolean)">
            <summary>
            Writes the values of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="address">The address where the value is written.</param>
            <param name="value">The value to write.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Write``1(System.Enum,``0,System.Boolean)">
            <summary>
            Writes the values of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="address">The address where the value is written.</param>
            <param name="value">The value to write.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Write``1(System.IntPtr,``0[],System.Boolean)">
            <summary>
            Writes an array of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
            <param name="address">The address where the values is written.</param>
            <param name="array">The array to write.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.Write``1(System.Enum,``0[],System.Boolean)">
            <summary>
            Writes an array of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
            <param name="address">The address where the values is written.</param>
            <param name="array">The array to write.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.WriteBytes(System.IntPtr,System.Byte[],System.Boolean)">
            <summary>
            Write an array of bytes in the remote process.
            </summary>
            <param name="address">The address where the array is written.</param>
            <param name="byteArray">The array of bytes to write.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.WriteString(System.IntPtr,System.String,System.Text.Encoding,System.Boolean)">
            <summary>
            Writes a string with a specified encoding in the remote process.
            </summary>
            <param name="address">The address where the string is written.</param>
            <param name="text">The text to write.</param>
            <param name="encoding">The encoding used.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.WriteString(System.Enum,System.String,System.Text.Encoding,System.Boolean)">
            <summary>
            Writes a string with a specified encoding in the remote process.
            </summary>
            <param name="address">The address where the string is written.</param>
            <param name="text">The text to write.</param>
            <param name="encoding">The encoding used.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.WriteString(System.IntPtr,System.String,System.Boolean)">
            <summary>
            Writes a string using the encoding UTF8 in the remote process.
            </summary>
            <param name="address">The address where the string is written.</param>
            <param name="text">The text to write.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.MemorySharp.WriteString(System.Enum,System.String,System.Boolean)">
            <summary>
            Writes a string using the encoding UTF8 in the remote process.
            </summary>
            <param name="address">The address where the string is written.</param>
            <param name="text">The text to write.</param>
            <param name="isRelative">[Optional] State if the address is relative to the main module.</param>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Memory.RemoteAllocation">
            <summary>
            Class representing an allocated memory in a remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.RemoteAllocation.IsDisposed">
            <summary>
            Gets a value indicating whether the element is disposed.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.RemoteAllocation.MustBeDisposed">
            <summary>
            Gets a value indicating whether the element must be disposed when the Garbage Collector collects the object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteAllocation.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.Int32,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Memory.RemoteAllocation"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="size">The size of the allocated memory.</param>
            <param name="protection">The protection of the allocated memory.</param>
            <param name="mustBeDisposed">The allocated memory will be released when the finalizer collects the object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteAllocation.Finalize">
            <summary>
            Frees resources and perform other cleanup operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteAllocation.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Memory.RemoteAllocation"/> object.
            </summary>
            <remarks>Don't use the IDisposable pattern because the class is sealed.</remarks>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Memory.MemoryCore">
            <summary>
            Static core class providing tools for memory editing.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.Allocate(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.Int32,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags)">
            <summary>
            Reserves a region of memory within the virtual address space of a specified process.
            </summary>
            <param name="processHandle">The handle to a process.</param>
            <param name="size">The size of the region of memory to allocate, in bytes.</param>
            <param name="protectionFlags">The memory protection for the region of pages to be allocated.</param>
            <param name="allocationFlags">The type of memory allocation.</param>
            <returns>The base address of the allocated region.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.CloseHandle(System.IntPtr)">
            <summary>
            Closes an open object handle.
            </summary>
            <param name="handle">A valid handle to an open object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.Free(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr)">
            <summary>
            Releases a region of memory within the virtual address space of a specified process.
            </summary>
            <param name="processHandle">A handle to a process.</param>
            <param name="address">A pointer to the starting address of the region of memory to be freed.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.NtQueryInformationProcess(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            etrieves information about the specified process.
            </summary>
            <param name="processHandle">A handle to the process to query.</param>
            <returns>A <see cref="T:Binarysharp.MemoryManagement.Native.ProcessBasicInformation"/> structure containg process information.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.OpenProcess(Binarysharp.MemoryManagement.Native.ProcessAccessFlags,System.Int32)">
            <summary>
            Opens an existing local process object.
            </summary>
            <param name="accessFlags">The access level to the process object.</param>
            <param name="processId">The identifier of the local process to be opened.</param>
            <returns>An open handle to the specified process.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.ReadBytes(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32)">
            <summary>
            Reads an array of bytes in the memory form the target process.
            </summary>
            <param name="processHandle">A handle to the process with memory that is being read.</param>
            <param name="address">A pointer to the base address in the specified process from which to read.</param>
            <param name="size">The number of bytes to be read from the specified process.</param>
            <returns>The collection of read bytes.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.ChangeProtection(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)">
            <summary>
            Changes the protection on a region of committed pages in the virtual address space of a specified process.
            </summary>
            <param name="processHandle">A handle to the process whose memory protection is to be changed.</param>
            <param name="address">A pointer to the base address of the region of pages whose access protection attributes are to be changed.</param>
            <param name="size">The size of the region whose access protection attributes are changed, in bytes.</param>
            <param name="protection">The memory protection option.</param>
            <returns>The old protection of the region in a <see cref="T:Binarysharp.MemoryManagement.Native.MemoryBasicInformation"/> structure.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.Query(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr)">
            <summary>
            Retrieves information about a range of pages within the virtual address space of a specified process.
            </summary>
            <param name="processHandle">A handle to the process whose memory information is queried.</param>
            <param name="baseAddress">A pointer to the base address of the region of pages to be queried.</param>
            <returns>A <see cref="T:Binarysharp.MemoryManagement.Native.MemoryBasicInformation"/> structures in which information about the specified page range is returned.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.Query(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.IntPtr)">
            <summary>
            Retrieves information about a range of pages within the virtual address space of a specified process.
            </summary>
            <param name="processHandle">A handle to the process whose memory information is queried.</param>
            <param name="addressFrom">A pointer to the starting address of the region of pages to be queried.</param>
            <param name="addressTo">A pointer to the ending address of the region of pages to be queried.</param>
            <returns>A collection of <see cref="T:Binarysharp.MemoryManagement.Native.MemoryBasicInformation"/> structures.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryCore.WriteBytes(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Byte[])">
            <summary>
            Writes data to an area of memory in a specified process.
            </summary>
            <param name="processHandle">A handle to the process memory to be modified.</param>
            <param name="address">A pointer to the base address in the specified process to which data is written.</param>
            <param name="byteArray">A buffer that contains data to be written in the address space of the specified process.</param>
            <returns>The number of bytes written.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory">
            <summary>
            Class representing a block of memory allocated in the local process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.Address">
            <summary>
            The address where the data is allocated.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.Size">
            <summary>
            The size of the allocated memory.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory"/> class, allocating a block of memory in the local process.
            </summary>
            <param name="size">The size to allocate.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.Finalize">
            <summary>
            Frees resources and perform other cleanup operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.Dispose">
            <summary>
            Releases the memory held by the <see cref="T:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.Read``1">
            <summary>
            Reads data from the unmanaged block of memory.
            </summary>
            <typeparam name="T">The type of data to return.</typeparam>
            <returns>The return value is the block of memory casted in the specified type.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.Read">
            <summary>
            Reads an array of bytes from the unmanaged block of memory.
            </summary>
            <returns>The return value is the block of memory.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.Write(System.Byte[],System.Int32)">
            <summary>
            Writes an array of bytes to the unmanaged block of memory.
            </summary>
            <param name="byteArray">The array of bytes to write.</param>
            <param name="index">The start position to copy bytes from.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.LocalUnmanagedMemory.Write``1(``0)">
            <summary>
            Write data to the unmanaged block of memory.
            </summary>
            <typeparam name="T">The type of data to write.</typeparam>
            <param name="data">The data to write.</param>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Memory.MemoryFactory">
            <summary>
            Class providing tools for manipulating memory space.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Memory.MemoryFactory.MemorySharp">
            <summary>
            The reference of the <see cref="F:Binarysharp.MemoryManagement.Memory.MemoryFactory.MemorySharp"/> object.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Memory.MemoryFactory.InternalRemoteAllocations">
            <summary>
            The list containing all allocated memory.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.MemoryFactory.RemoteAllocations">
            <summary>
            A collection containing all allocated memory in the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.MemoryFactory.Regions">
            <summary>
            Gets all blocks of memory allocated in the remote process.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryFactory.#ctor(Binarysharp.MemoryManagement.MemorySharp)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Memory.MemoryFactory"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="F:Binarysharp.MemoryManagement.Memory.MemoryFactory.MemorySharp"/> object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryFactory.Finalize">
            <summary>
            Frees resources and perform other cleanup operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryFactory.Allocate(System.Int32,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags,System.Boolean)">
            <summary>
            Allocates a region of memory within the virtual address space of the remote process.
            </summary>
            <param name="size">The size of the memory to allocate.</param>
            <param name="protection">The protection of the memory to allocate.</param>
            <param name="mustBeDisposed">The allocated memory will be released when the finalizer collects the object.</param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Memory.RemoteAllocation"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryFactory.Deallocate(Binarysharp.MemoryManagement.Memory.RemoteAllocation)">
            <summary>
            Deallocates a region of memory previously allocated within the virtual address space of the remote process.
            </summary>
            <param name="allocation">The allocated memory to release.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryFactory.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Memory.MemoryFactory"/> object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Memory.MemoryProtection">
            <summary>
            Class providing tools for manipulating memory protection.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Memory.MemoryProtection._memorySharp">
            <summary>
            The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.MemoryProtection.BaseAddress">
            <summary>
            The base address of the altered memory.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.MemoryProtection.MustBeDisposed">
            <summary>
            States if the <see cref="T:Binarysharp.MemoryManagement.Memory.MemoryProtection"/> object nust be disposed when it is collected.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.MemoryProtection.NewProtection">
            <summary>
            Defines the new protection applied to the memory.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.MemoryProtection.OldProtection">
            <summary>
            References the inital protection of the memory.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.MemoryProtection.Size">
            <summary>
            The size of the altered memory.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryProtection.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Memory.MemoryProtection"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="baseAddress">The base address of the memory to change the protection.</param>
            <param name="size">The size of the memory to change.</param>
            <param name="protection">The new protection to apply.</param>
            <param name="mustBeDisposed">The resource will be automatically disposed when the finalizer collects the object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryProtection.Finalize">
            <summary>
            Frees resources and perform other cleanup operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryProtection.Dispose">
            <summary>
            Restores the initial protection of the memory.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.MemoryProtection.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Memory.RemoteRegion">
            <summary>
            Represents a contiguous block of memory in the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.RemoteRegion.Information">
            <summary>
            Contains information about the memory.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.RemoteRegion.IsValid">
            <summary>
            Gets if the <see cref="T:Binarysharp.MemoryManagement.Memory.RemoteRegion"/> is valid.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteRegion.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.IntPtr)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Memory.RemoteRegion"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="baseAddress">The base address of the memory region.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteRegion.ChangeProtection(Binarysharp.MemoryManagement.Native.MemoryProtectionFlags,System.Boolean)">
            <summary>
            Changes the protection of the n next bytes in remote process.
            </summary>
            <param name="protection">The new protection to apply.</param>
            <param name="mustBeDisposed">The resource will be automatically disposed when the finalizer collects the object.</param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Memory.MemoryProtection"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteRegion.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteRegion.Equals(Binarysharp.MemoryManagement.Memory.RemoteRegion)">
            <summary>
            Returns a value indicating whether this instance is equal to a specified object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteRegion.GetHashCode">
            <summary>
            Serves as a hash function for a particular type. 
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteRegion.Release">
            <summary>
            Releases the memory used by the region.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemoteRegion.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Memory.RemotePointer">
            <summary>
            Class representing a pointer in the memory of the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.RemotePointer.BaseAddress">
            <summary>
            The address of the pointer in the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.RemotePointer.IsValid">
            <summary>
            Gets if the <see cref="T:Binarysharp.MemoryManagement.Memory.RemotePointer"/> is valid.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Memory.RemotePointer.MemorySharp">
            <summary>
            The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.IntPtr)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Memory.RemotePointer"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="address">The location where the pointer points in the remote process.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ChangeProtection(System.Int32,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags,System.Boolean)">
            <summary>
            Changes the protection of the n next bytes in remote process.
            </summary>
            <param name="size">The size of the memory to change.</param>
            <param name="protection">The new protection to apply.</param>
            <param name="mustBeDisposed">The resource will be automatically disposed when the finalizer collects the object.</param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Memory.MemoryProtection"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Equals(Binarysharp.MemoryManagement.Memory.RemotePointer)">
            <summary>
            Returns a value indicating whether this instance is equal to a specified object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Execute``1">
            <summary>
            Executes the assembly code in the remote process.
            </summary>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Execute">
            <summary>
            Executes the assembly code in the remote process.
            </summary>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Execute``1(System.Object)">
            <summary>
            Executes the assembly code in the remote process.
            </summary>
            <param name="parameter">The parameter used to execute the assembly code.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Execute(System.Object)">
            <summary>
            Executes the assembly code in the remote process.
            </summary>
            <param name="parameter">The parameter used to execute the assembly code.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Execute``1(Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions,System.Object[])">
            <summary>
            Executes the assembly code in the remote process.
            </summary>
            <param name="callingConvention">The calling convention used to execute the assembly code with the parameters.</param>
            <param name="parameters">An array of parameters used to execute the assembly code.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Execute(Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions,System.Object[])">
            <summary>
            Executes the assembly code in the remote process.
            </summary>
            <param name="callingConvention">The calling convention used to execute the assembly code with the parameters.</param>
            <param name="parameters">An array of parameters used to execute the assembly code.</param>
            <returns>The return value is the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ExecuteAsync``1">
            <summary>
            Executes asynchronously the assembly code in the remote process.
            </summary>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ExecuteAsync">
            <summary>
            Executes asynchronously the assembly code in the remote process.
            </summary>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ExecuteAsync``1(System.Object)">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="parameter">The parameter used to execute the assembly code.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ExecuteAsync(System.Object)">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="parameter">The parameter used to execute the assembly code.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ExecuteAsync``1(Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions,System.Object[])">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="callingConvention">The calling convention used to execute the assembly code with the parameters.</param>
            <param name="parameters">An array of parameters used to execute the assembly code.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ExecuteAsync(Binarysharp.MemoryManagement.Assembly.CallingConvention.CallingConventions,System.Object[])">
            <summary>
            Executes asynchronously the assembly code located in the remote process at the specified address.
            </summary>
            <param name="callingConvention">The calling convention used to execute the assembly code with the parameters.</param>
            <param name="parameters">An array of parameters used to execute the assembly code.</param>
            <returns>The return value is an asynchronous operation that return the exit code of the thread created to execute the assembly code.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.GetHashCode">
            <summary>
            Serves as a hash function for a particular type. 
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Read``1(System.Int32)">
            <summary>
            Reads the value of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="offset">The offset where the value is read from the pointer.</param>
            <returns>A value.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Read``1(System.Enum)">
            <summary>
            Reads the value of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="offset">The offset where the value is read from the pointer.</param>
            <returns>A value.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Read``1">
            <summary>
            Reads the value of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <returns>A value.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Read``1(System.Int32,System.Int32)">
            <summary>
            Reads an array of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
            <param name="offset">The offset where the values is read from the pointer.</param>
            <param name="count">The number of cells in the array.</param>
            <returns>An array.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Read``1(System.Enum,System.Int32)">
            <summary>
            Reads an array of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
            <param name="offset">The offset where the values is read from the pointer.</param>
            <param name="count">The number of cells in the array.</param>
            <returns>An array.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ReadString(System.Int32,System.Text.Encoding,System.Int32)">
            <summary>
            Reads a string with a specified encoding in the remote process.
            </summary>
            <param name="offset">The offset where the string is read from the pointer.</param>
            <param name="encoding">The encoding used.</param>
            <param name="maxLength">[Optional] The number of maximum bytes to read. The string is automatically cropped at this end ('\0' char).</param>
            <returns>The string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ReadString(System.Enum,System.Text.Encoding,System.Int32)">
            <summary>
            Reads a string with a specified encoding in the remote process.
            </summary>
            <param name="offset">The offset where the string is read from the pointer.</param>
            <param name="encoding">The encoding used.</param>
            <param name="maxLength">[Optional] The number of maximum bytes to read. The string is automatically cropped at this end ('\0' char).</param>
            <returns>The string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ReadString(System.Text.Encoding,System.Int32)">
            <summary>
            Reads a string with a specified encoding in the remote process.
            </summary>
            <param name="encoding">The encoding used.</param>
            <param name="maxLength">[Optional] The number of maximum bytes to read. The string is automatically cropped at this end ('\0' char).</param>
            <returns>The string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ReadString(System.Int32,System.Int32)">
            <summary>
            Reads a string using the encoding UTF8 in the remote process.
            </summary>
            <param name="offset">The offset where the string is read from the pointer.</param>
            <param name="maxLength">[Optional] The number of maximum bytes to read. The string is automatically cropped at this end ('\0' char).</param>
            <returns>The string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ReadString(System.Enum,System.Int32)">
            <summary>
            Reads a string using the encoding UTF8 in the remote process.
            </summary>
            <param name="offset">The offset where the string is read from the pointer.</param>
            <param name="maxLength">[Optional] The number of maximum bytes to read. The string is automatically cropped at this end ('\0' char).</param>
            <returns>The string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Write``1(System.Int32,``0)">
            <summary>
            Writes the values of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="offset">The offset where the value is written from the pointer.</param>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Write``1(System.Enum,``0)">
            <summary>
            Writes the values of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="offset">The offset where the value is written from the pointer.</param>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Write``1(``0)">
            <summary>
            Writes the values of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Write``1(System.Int32,``0[])">
            <summary>
            Writes an array of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
            <param name="offset">The offset where the values is written from the pointer.</param>
            <param name="array">The array to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Write``1(System.Enum,``0[])">
            <summary>
            Writes an array of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
            <param name="offset">The offset where the values is written from the pointer.</param>
            <param name="array">The array to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.Write``1(``0[])">
            <summary>
            Writes an array of a specified type in the remote process.
            </summary>
            <typeparam name="T">The type of the values.</typeparam>
            <param name="array">The array to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.WriteString(System.Int32,System.String,System.Text.Encoding)">
            <summary>
            Writes a string with a specified encoding in the remote process.
            </summary>
            <param name="offset">The offset where the string is written from the pointer.</param>
            <param name="text">The text to write.</param>
            <param name="encoding">The encoding used.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.WriteString(System.Enum,System.String,System.Text.Encoding)">
            <summary>
            Writes a string with a specified encoding in the remote process.
            </summary>
            <param name="offset">The offset where the string is written from the pointer.</param>
            <param name="text">The text to write.</param>
            <param name="encoding">The encoding used.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.WriteString(System.String,System.Text.Encoding)">
            <summary>
            Writes a string with a specified encoding in the remote process.
            </summary>
            <param name="text">The text to write.</param>
            <param name="encoding">The encoding used.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.WriteString(System.Int32,System.String)">
            <summary>
            Writes a string using the encoding UTF8 in the remote process.
            </summary>
            <param name="offset">The offset where the string is written from the pointer.</param>
            <param name="text">The text to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.WriteString(System.Enum,System.String)">
            <summary>
            Writes a string using the encoding UTF8 in the remote process.
            </summary>
            <param name="offset">The offset where the string is written from the pointer.</param>
            <param name="text">The text to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Memory.RemotePointer.WriteString(System.String)">
            <summary>
            Writes a string using the encoding UTF8 in the remote process.
            </summary>
            <param name="text">The text to write.</param>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Modules.InjectedModule">
            <summary>
            Class representing an injected module in a remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.InjectedModule.IsDisposed">
            <summary>
            Gets a value indicating whether the element is disposed.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.InjectedModule.MustBeDisposed">
            <summary>
            Gets a value indicating whether the element must be disposed when the Garbage Collector collects the object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.InjectedModule.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.Diagnostics.ProcessModule,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Modules.InjectedModule"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="module">The native <see cref="T:System.Diagnostics.ProcessModule"/> object corresponding to the injected module.</param>
            <param name="mustBeDisposed">The module will be ejected when the finalizer collects the object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.InjectedModule.Finalize">
            <summary>
            Frees resources and perform other cleanup operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.InjectedModule.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Modules.InjectedModule"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.InjectedModule.InternalInject(Binarysharp.MemoryManagement.MemorySharp,System.String)">
            <summary>
            Injects the specified module into the address space of the remote process.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="path">The path of the module. This can be either a library module (a .dll file) or an executable module (an .exe file).</param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Modules.InjectedModule"/>class.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Modules.ModuleCore">
            <summary>
            Static core class providing tools for manipulating modules and libraries.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleCore.GetProcAddress(System.String,System.String)">
            <summary>
            Retrieves the address of an exported function or variable from the specified dynamic-link library (DLL).
            </summary>
            <param name="moduleName">The module name (not case-sensitive).</param>
            <param name="functionName">The function or variable name, or the function's ordinal value.</param>
            <returns>The address of the exported function.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleCore.GetProcAddress(System.Diagnostics.ProcessModule,System.String)">
            <summary>
            Retrieves the address of an exported function or variable from the specified dynamic-link library (DLL).
            </summary>
            <param name="module">The <see cref="T:System.Diagnostics.ProcessModule"/> object corresponding to the module.</param>
            <param name="functionName">The function or variable name, or the function's ordinal value.</param>
            <returns>If the function succeeds, the return value is the address of the exported function.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleCore.FreeLibrary(System.String)">
            <summary>
            Frees the loaded dynamic-link library (DLL) module and, if necessary, decrements its reference count.
            </summary>
            <param name="libraryName">The name of the library to free (not case-sensitive).</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleCore.FreeLibrary(System.Diagnostics.ProcessModule)">
            <summary>
            Frees the loaded dynamic-link library (DLL) module and, if necessary, decrements its reference count.
            </summary>
            <param name="module">The <see cref="T:System.Diagnostics.ProcessModule"/> object corresponding to the library to free.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleCore.LoadLibrary(System.String)">
            <summary>
            Loads the specified module into the address space of the calling process.
            </summary>
            <param name="libraryPath">The name of the module. This can be either a library module (a .dll file) or an executable module (an .exe file).</param>
            <returns>A <see cref="T:System.Diagnostics.ProcessModule"/> corresponding to the loaded library.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Modules.ModuleFactory">
            <summary>
            Class providing tools for manipulating modules and libraries.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Modules.ModuleFactory.MemorySharp">
            <summary>
            The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Modules.ModuleFactory.InternalInjectedModules">
            <summary>
            The list containing all injected modules (writable).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.ModuleFactory.InjectedModules">
            <summary>
            A collection containing all injected modules.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.ModuleFactory.MainModule">
            <summary>
            Gets the main module for the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.ModuleFactory.RemoteModules">
            <summary>
            Gets the modules that have been loaded in the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.ModuleFactory.NativeModules">
            <summary>
            Gets the native modules that have been loaded in the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.ModuleFactory.Item(System.IntPtr)">
            <summary>
            Gets a pointer from the remote process.
            </summary>
            <param name="address">The address of the pointer.</param>
            <returns>A new instance of a <see cref="T:Binarysharp.MemoryManagement.Memory.RemotePointer"/> class.</returns>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.ModuleFactory.Item(System.String)">
            <summary>
            Gets the specified module in the remote process.
            </summary>
            <param name="moduleName">The name of module (not case sensitive).</param>
            <returns>A new instance of a <see cref="T:Binarysharp.MemoryManagement.Modules.RemoteModule"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleFactory.#ctor(Binarysharp.MemoryManagement.MemorySharp)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Modules.ModuleFactory"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleFactory.Finalize">
            <summary>
            Frees resources and perform other cleanup operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleFactory.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Modules.ModuleFactory"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleFactory.Eject(Binarysharp.MemoryManagement.Modules.RemoteModule)">
            <summary>
            Frees the loaded dynamic-link library (DLL) module and, if necessary, decrements its reference count.
            </summary>
            <param name="module">The module to eject.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleFactory.Eject(System.String)">
            <summary>
            Frees the loaded dynamic-link library (DLL) module and, if necessary, decrements its reference count.
            </summary>
            <param name="moduleName">The name of module to eject.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleFactory.FetchModule(System.String)">
            <summary>
            Fetches a module from the remote process.
            </summary>
            <param name="moduleName">A module name (not case sensitive). If the file name extension is omitted, the default library extension .dll is appended.</param>
            <returns>A new instance of a <see cref="T:Binarysharp.MemoryManagement.Modules.RemoteModule"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleFactory.FetchModule(System.Diagnostics.ProcessModule)">
            <summary>
            Fetches a module from the remote process.
            </summary>
            <param name="module">A module in the remote process.</param>
            <returns>A new instance of a <see cref="T:Binarysharp.MemoryManagement.Modules.RemoteModule"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.ModuleFactory.Inject(System.String,System.Boolean)">
            <summary>
            Injects the specified module into the address space of the remote process.
            </summary>
            <param name="path">The path of the module. This can be either a library module (a .dll file) or an executable module (an .exe file).</param>
            <param name="mustBeDisposed">The module will be ejected when the finalizer collects the object.</param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Modules.InjectedModule"/>class.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Modules.RemoteFunction">
            <summary>
            Class representing a function in the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.RemoteFunction.Name">
            <summary>
            The name of the function.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.RemoteFunction.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Modules.RemoteModule">
            <summary>
            Class repesenting a module in the remote process.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Modules.RemoteModule.CachedFunctions">
            <summary>
            The dictionary containing all cached functions of the remote module.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.RemoteModule.IsMainModule">
            <summary>
            State if this is the main module of the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.RemoteModule.IsValid">
            <summary>
            Gets if the <see cref="T:Binarysharp.MemoryManagement.Modules.RemoteModule"/> is valid.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.RemoteModule.Name">
            <summary>
            The name of the module.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.RemoteModule.Native">
            <summary>
            The native <see cref="T:System.Diagnostics.ProcessModule"/> object corresponding to this module.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.RemoteModule.Path">
            <summary>
            The full path of the module.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.RemoteModule.Size">
            <summary>
            The size of the module in the memory of the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Modules.RemoteModule.Item(System.String)">
            <summary>
            Gets the specified function in the remote module.
            </summary>
            <param name="functionName">The name of the function.</param>
            <returns>A new instance of a <see cref="T:Binarysharp.MemoryManagement.Modules.RemoteFunction"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.RemoteModule.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.Diagnostics.ProcessModule)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Modules.RemoteModule"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="module">The native <see cref="T:System.Diagnostics.ProcessModule"/> object corresponding to this module.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.RemoteModule.Eject">
            <summary>
            Ejects the loaded dynamic-link library (DLL) module.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.RemoteModule.FindFunction(System.String)">
            <summary>
            Finds the specified function in the remote module.
            </summary>
            <param name="functionName">The name of the function (case sensitive).</param>
            <returns>A new instance of a <see cref="T:Binarysharp.MemoryManagement.Modules.RemoteFunction"/> class.</returns>
            <remarks>
            Interesting article on how DLL loading works: http://msdn.microsoft.com/en-us/magazine/bb985014.aspx
            </remarks>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.RemoteModule.InternalEject(Binarysharp.MemoryManagement.MemorySharp,Binarysharp.MemoryManagement.Modules.RemoteModule)">
            <summary>
            Frees the loaded dynamic-link library (DLL) module and, if necessary, decrements its reference count.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="module">The module to eject.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Modules.RemoteModule.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ManagedPeb">
            <summary>
            Class representing the Process Environment Block of a remote process.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.ManagedPeb.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.IntPtr)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Native.ManagedPeb"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="address">The location of the peb.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.ManagedPeb.FindPeb(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Finds the Process Environment Block address of a specified process.
            </summary>
            <param name="processHandle">A handle of the process.</param>
            <returns>A <see cref="T:System.IntPtr"/> pointer of the PEB.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ManagedTeb">
            <summary>
            Class representing the Thread Environment Block of a remote thread.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.CurrentSehFrame">
            <summary>
            Current Structured Exception Handling (SEH) frame.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.TopOfStack">
            <summary>
            The top of stack.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.BottomOfStack">
            <summary>
            The current bottom of stack.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.SubSystemTeb">
            <summary>
            The TEB sub system.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.FiberData">
            <summary>
            The fiber data.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.ArbitraryDataSlot">
            <summary>
            The arbitrary data slot.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.Teb">
            <summary>
            The linear address of Thread Environment Block (TEB).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.EnvironmentPointer">
            <summary>
            The environment pointer.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.ProcessId">
            <summary>
            The process Id.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.ThreadId">
            <summary>
            The current thread Id.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.RpcHandle">
            <summary>
            The active RPC handle.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.Tls">
            <summary>
            The linear address of the thread-local storage (TLS) array.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.Peb">
            <summary>
            The linear address of Process Environment Block (PEB).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.LastErrorNumber">
            <summary>
            The last error number.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.CriticalSectionsCount">
            <summary>
            The count of owned critical sections.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.CsrClientThread">
            <summary>
            The address of CSR Client Thread.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.Win32ThreadInfo">
            <summary>
            Win32 Thread Information.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.Win32ClientInfo">
            <summary>
            Win32 client information (NT), user32 private data (Wine), 0x60 = LastError (Win95), 0x74 = LastError (WinME).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.WoW64Reserved">
            <summary>
            Reserved for Wow64. Contains a pointer to FastSysCall in Wow64.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.CurrentLocale">
            <summary>
            The current locale
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.FpSoftwareStatusRegister">
            <summary>
            The FP Software Status Register.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.SystemReserved1">
            <summary>
            Reserved for OS (NT), kernel32 private data (Wine).
            herein: FS:[0x124] 4 NT Pointer to KTHREAD (ETHREAD) structure.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.ExceptionCode">
            <summary>
            The exception code.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.ActivationContextStack">
            <summary>
            The activation context stack.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.SpareBytes">
            <summary>
            The spare bytes (NT), ntdll private data (Wine).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.SystemReserved2">
            <summary>
            Reserved for OS (NT), ntdll private data (Wine).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.GdiTebBatch">
            <summary>
            The GDI TEB Batch (OS), vm86 private data (Wine).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.GdiRegion">
            <summary>
            The GDI Region.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.GdiPen">
            <summary>
            The GDI Pen.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.GdiBrush">
            <summary>
            The GDI Brush.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.RealProcessId">
            <summary>
            The real process Id.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.RealThreadId">
            <summary>
            The real thread Id.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.GdiCachedProcessHandle">
            <summary>
            The GDI cached process handle.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.GdiClientProcessId">
            <summary>
            The GDI client process Id (PID).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.GdiClientThreadId">
            <summary>
            The GDI client thread Id (TID).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.GdiThreadLocalInfo">
            <summary>
            The GDI thread locale information.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.UserReserved1">
            <summary>
            Reserved for user application.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.GlReserved1">
            <summary>
            Reserved for GL.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.LastStatusValue">
            <summary>
            The last value status value.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.StaticUnicodeString">
            <summary>
            The static UNICODE_STRING buffer.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.DeallocationStack">
            <summary>
            The pointer to deallocation stack.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.TlsSlots">
            <summary>
            The TLS slots, 4 byte per slot.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.TlsLinks">
            <summary>
            The TLS links (LIST_ENTRY structure).
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.Vdm">
            <summary>
            Virtual DOS Machine.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.RpcReserved">
            <summary>
            Reserved for RPC.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ManagedTeb.ThreadErrorMode">
            <summary>
            The thread error mode (RtlSetThreadErrorMode).
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.ManagedTeb.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.IntPtr)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Native.ManagedTeb"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="address">The location of the teb.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.ManagedTeb.FindTeb(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Finds the Thread Environment Block address of a specified thread.
            </summary>
            <param name="threadHandle">A handle of the thread.</param>
            <returns>A <see cref="T:System.IntPtr"/> pointer of the TEB.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.FlashWindowFlags">
            <summary>
            Flash window flags list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.FlashWindowFlags.All">
            <summary>
            Flash both the window caption and taskbar button. This is equivalent to setting the <see cref="F:Binarysharp.MemoryManagement.Native.FlashWindowFlags.Caption"/> | <see cref="F:Binarysharp.MemoryManagement.Native.FlashWindowFlags.Tray"/> flags.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.FlashWindowFlags.Caption">
            <summary>
            Flash the window caption.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.FlashWindowFlags.Stop">
            <summary>
            Stop flashing. The system restores the window to its original state.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.FlashWindowFlags.Timer">
            <summary>
            Flash continuously, until the <see cref="F:Binarysharp.MemoryManagement.Native.FlashWindowFlags.Stop"/> flag is set.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.FlashWindowFlags.TimerNoForeground">
            <summary>
            Flash continuously until the window comes to the foreground.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.FlashWindowFlags.Tray">
            <summary>
            Flash the taskbar button.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.InputTypes">
            <summary>
            The types used in the function <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.SendInput(System.Int32,Binarysharp.MemoryManagement.Native.Input[],System.Int32)"/> for input events.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.KeyboardFlags">
            <summary>
            The keyboard flags list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.KeyboardFlags.ExtendedKey">
            <summary>
            If specified, the scan code was preceded by a prefix byte that has the value 0xE0 (224).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.KeyboardFlags.KeyUp">
            <summary>
            If specified, the key is being released. If not specified, the key is being pressed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.KeyboardFlags.ScanCode">
            <summary>
            If specified, <see cref="F:Binarysharp.MemoryManagement.Native.KeyboardInput.ScanCode"/> identifies the key and <see cref="F:Binarysharp.MemoryManagement.Native.KeyboardInput.VirtualKey"/> is ignored. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.KeyboardFlags.Unicode">
            <summary>
            If specified, the system synthesizes a VK_PACKET keystroke. The <see cref="F:Binarysharp.MemoryManagement.Native.KeyboardInput.VirtualKey"/> parameter must be zero. 
            This flag can only be combined with the KEYEVENTF_KEYUP flag.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.Keys">
            <summary>
            The key codes and modifiers list.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.MemoryAllocationFlags">
            <summary>
            Memory-allocation options list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryAllocationFlags.Commit">
            <summary>
            Allocates memory charges (from the overall size of memory and the paging files on disk) for the specified reserved memory pages. 
            The function also guarantees that when the caller later initially accesses the memory, the contents will be zero. 
            Actual physical pages are not allocated unless/until the virtual addresses are actually accessed.
            To reserve and commit pages in one step, call <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/> with MEM_COMMIT | MEM_RESERVE.
            The function fails if you attempt to commit a page that has not been reserved. The resulting error code is ERROR_INVALID_ADDRESS.
            An attempt to commit a page that is already committed does not cause the function to fail. 
            This means that you can commit pages without first determining the current commitment state of each page.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryAllocationFlags.Reserve">
            <summary>
            Reserves a range of the process's virtual address space without allocating any actual physical storage in memory or in the paging file on disk.
            You commit reserved pages by calling <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/> again with MEM_COMMIT. 
            To reserve and commit pages in one step, call VirtualAllocEx with MEM_COMMIT | MEM_RESERVE.
            Other memory allocation functions, such as malloc and LocalAlloc, cannot use reserved memory until it has been released.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryAllocationFlags.Reset">
            <summary>
            Indicates that data in the memory range specified by lpAddress and dwSize is no longer of interest. 
            The pages should not be read from or written to the paging file.
             However, the memory block will be used again later, so it should not be decommitted. This value cannot be used with any other value.
            Using this value does not guarantee that the range operated on with MEM_RESET will contain zeros. If you want the range to contain zeros, decommit the memory and then recommit it.
            When you use MEM_RESET, the VirtualAllocEx function ignores the value of fProtect. However, you must still set fProtect to a valid protection value, such as PAGE_NOACCESS.
            <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/> returns an error if you use MEM_RESET and the range of memory is mapped to a file. 
            A shared view is only acceptable if it is mapped to a paging file.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryAllocationFlags.ResetUndo">
            <summary>
            MEM_RESET_UNDO should only be called on an address range to which MEM_RESET was successfully applied earlier. 
            It indicates that the data in the specified memory range specified by lpAddress and dwSize is of interest to the caller and attempts to reverse the effects of MEM_RESET. 
            If the function succeeds, that means all data in the specified address range is intact. 
            If the function fails, at least some of the data in the address range has been replaced with zeroes.
            This value cannot be used with any other value. 
            If MEM_RESET_UNDO is called on an address range which was not MEM_RESET earlier, the behavior is undefined. 
            When you specify MEM_RESET, the <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/> function ignores the value of flProtect. 
            However, you must still set flProtect to a valid protection value, such as PAGE_NOACCESS.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryAllocationFlags.LargePages">
            <summary>
            Allocates memory using large page support.
            The size and alignment must be a multiple of the large-page minimum. To obtain this value, use the GetLargePageMinimum function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryAllocationFlags.Physical">
            <summary>
            Reserves an address range that can be used to map Address Windowing Extensions (AWE) pages.
            This value must be used with MEM_RESERVE and no other values.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryAllocationFlags.TopDown">
            <summary>
            Allocates memory at the highest possible address. This can be slower than regular allocations, especially when there are many allocations.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags">
            <summary>
            Memory-protection options list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.ZeroAccess">
            <summary>
            Disables all access to the committed region of pages. An attempt to read from, write to, or execute the committed region results in an access violation.
            This value is not officially present in the Microsoft's enumeration but can occur according to the MEMORY_BASIC_INFORMATION structure documentation.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.Execute">
            <summary>
            Enables execute access to the committed region of pages. An attempt to read from or write to the committed region results in an access violation.
            This flag is not supported by the CreateFileMapping function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.ExecuteRead">
            <summary>
            Enables execute or read-only access to the committed region of pages. An attempt to write to the committed region results in an access violation.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.ExecuteReadWrite">
            <summary>
            Enables execute, read-only, or read/write access to the committed region of pages.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.ExecuteWriteCopy">
            <summary>
            Enables execute, read-only, or copy-on-write access to a mapped view of a file mapping object. 
            An attempt to write to a committed copy-on-write page results in a private copy of the page being made for the process. 
            The private page is marked as PAGE_EXECUTE_READWRITE, and the change is written to the new page.
            This flag is not supported by the VirtualAlloc or <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/> functions. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.NoAccess">
            <summary>
            Disables all access to the committed region of pages. An attempt to read from, write to, or execute the committed region results in an access violation.
            This flag is not supported by the CreateFileMapping function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.ReadOnly">
            <summary>
            Enables read-only access to the committed region of pages. An attempt to write to the committed region results in an access violation. 
            If Data Execution Prevention is enabled, an attempt to execute code in the committed region results in an access violation.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.ReadWrite">
            <summary>
            Enables read-only or read/write access to the committed region of pages. 
            If Data Execution Prevention is enabled, attempting to execute code in the committed region results in an access violation.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.WriteCopy">
            <summary>
            Enables read-only or copy-on-write access to a mapped view of a file mapping object. 
            An attempt to write to a committed copy-on-write page results in a private copy of the page being made for the process. 
            The private page is marked as PAGE_READWRITE, and the change is written to the new page. 
            If Data Execution Prevention is enabled, attempting to execute code in the committed region results in an access violation.
            This flag is not supported by the VirtualAlloc or <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/> functions.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.Guard">
            <summary>
            Pages in the region become guard pages. 
            Any attempt to access a guard page causes the system to raise a STATUS_GUARD_PAGE_VIOLATION exception and turn off the guard page status. 
            Guard pages thus act as a one-time access alarm. For more information, see Creating Guard Pages.
            When an access attempt leads the system to turn off guard page status, the underlying page protection takes over.
            If a guard page exception occurs during a system service, the service typically returns a failure status indicator.
            This value cannot be used with PAGE_NOACCESS.
            This flag is not supported by the CreateFileMapping function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.NoCache">
            <summary>
            Sets all pages to be non-cachable. Applications should not use this attribute except when explicitly required for a device. 
            Using the interlocked functions with memory that is mapped with SEC_NOCACHE can result in an EXCEPTION_ILLEGAL_INSTRUCTION exception.
            The PAGE_NOCACHE flag cannot be used with the PAGE_GUARD, PAGE_NOACCESS, or PAGE_WRITECOMBINE flags.
            The PAGE_NOCACHE flag can be used only when allocating private memory with the VirtualAlloc, <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/>, or VirtualAllocExNuma functions. 
            To enable non-cached memory access for shared memory, specify the SEC_NOCACHE flag when calling the CreateFileMapping function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryProtectionFlags.WriteCombine">
            <summary>
            Sets all pages to be write-combined.
            Applications should not use this attribute except when explicitly required for a device. 
            Using the interlocked functions with memory that is mapped as write-combined can result in an EXCEPTION_ILLEGAL_INSTRUCTION exception.
            The PAGE_WRITECOMBINE flag cannot be specified with the PAGE_NOACCESS, PAGE_GUARD, and PAGE_NOCACHE flags.
            The PAGE_WRITECOMBINE flag can be used only when allocating private memory with the VirtualAlloc, <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/>, or VirtualAllocExNuma functions. 
            To enable write-combined memory access for shared memory, specify the SEC_WRITECOMBINE flag when calling the CreateFileMapping function.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.MemoryReleaseFlags">
            <summary>
            Memory-release options list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryReleaseFlags.Decommit">
            <summary>
            Decommits the specified region of committed pages. After the operation, the pages are in the reserved state.
            The function does not fail if you attempt to decommit an uncommitted page. 
            This means that you can decommit a range of pages without first determining their current commitment state.
            Do not use this value with MEM_RELEASE.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryReleaseFlags.Release">
            <summary>
            Releases the specified region of pages. After the operation, the pages are in the free state.
            If you specify this value, dwSize must be 0 (zero), and lpAddress must point to the base address returned by the VirtualAllocEx function when the region is reserved. 
            The function fails if either of these conditions is not met.
            If any pages in the region are committed currently, the function first decommits, and then releases them.
            The function does not fail if you attempt to release pages that are in different states, some reserved and some committed. 
            This means that you can release a range of pages without first determining the current commitment state.
            Do not use this value with MEM_DECOMMIT.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.MemoryStateFlags">
            <summary>
            Memory-state options list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryStateFlags.Commit">
            <summary>
            Indicates committed pages for which physical storage has been allocated, either in memory or in the paging file on disk.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryStateFlags.Free">
            <summary>
            Indicates free pages not accessible to the calling process and available to be allocated. 
            For free pages, the information in the AllocationBase, AllocationProtect, Protect, and Type members is undefined.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryStateFlags.Reserve">
            <summary>
            Indicates reserved pages where a range of the process's virtual address space is reserved without any physical storage being allocated. 
            For reserved pages, the information in the Protect member is undefined.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.MemoryTypeFlags">
            <summary>
            Memory-type options list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryTypeFlags.None">
            <summary>
            This value is not officially present in the Microsoft's enumeration but can occur after testing.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryTypeFlags.Image">
            <summary>
            Indicates that the memory pages within the region are mapped into the view of an image section.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryTypeFlags.Mapped">
            <summary>
            Indicates that the memory pages within the region are mapped into the view of a section.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryTypeFlags.Private">
            <summary>
            Indicates that the memory pages within the region are private (that is, not shared by other processes).
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.MouseFlags">
            <summary>
            The mouse flags list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.Absolute">
            <summary>
            The DeltaX and DeltaY members contain normalized absolute coordinates. If the flag is not set, DeltaX and DeltaY contain relative data 
            (the change in position since the last reported position). This flag can be set, or not set, regardless of what kind of mouse or other
            pointing device, if any, is connected to the system.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.HWheel">
            <summary>
            The wheel was moved horizontally, if the mouse has a wheel. The amount of movement is specified in MouseData.
            Windows XP/2000:  This value is not supported.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.Move">
            <summary>
            Movement occurred.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.MoveNoCoalesce">
            <summary>
            The WM_MOUSEMOVE messages will not be coalesced. The default behavior is to coalesce WM_MOUSEMOVE messages.
            Windows XP/2000:  This value is not supported.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.LeftDown">
            <summary>
            The left button was pressed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.LeftUp">
            <summary>
            The left button was released.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.RightDown">
            <summary>
            The right button was pressed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.RightUp">
            <summary>
            The right button was released.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.MiddleDown">
            <summary>
            The middle button was pressed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.MiddleUp">
            <summary>
            The middle button was released.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.VirtualDesk">
            <summary>
            Maps coordinates to the entire desktop. Must be used with <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.Absolute"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.Wheel">
            <summary>
            The wheel was moved, if the mouse has a wheel. The amount of movement is specified in MouseData. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.XDown">
            <summary>
            An X button was pressed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseFlags.XUp">
            <summary>
            An X button was released.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.PebStructure">
            <summary>
            The structure of the Process Environment Block.
            </summary>
            <remarks>
            Tested on Windows 7 x64, 2013-03-10
            Source: http://blog.rewolf.pl/blog/?p=573#.UTyBo1fJL6p
            </remarks>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.BeingDebugged">
            <summary>
            Gets if the process is being debugged.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.TlsBitmapBits">
            <summary>
            Length: 8 bytes.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.NtGlobalFlag">
            <summary>
            Length: 8 bytes.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.CriticalSectionTimeout">
            <summary>
            Length: 8 bytes (LARGE_INTEGER type).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.OsBuildNumber">
            <summary>
            Length: 2 bytes.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.OsCsdVersion">
            <summary>
            Length: 2 bytes.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.GdiHandleBuffer">
            <summary>
            Length: 0x88 bytes (0x22 * sizeof(IntPtr)).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.TlsExpansionBitmapBits">
            <summary>
            Length: 0x80 bytes (0x20 * sizeof(IntPtr))
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.AppCompatFlags">
            <summary>
            Length: 8 bytes (LARGE_INTEGER type).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.AppCompatFlagsUser">
            <summary>
            Length: 8 bytes (LARGE_INTEGER type).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.PebStructure.CsdVersion">
            <summary>
            Length: 8 bytes (UNICODE_STRING type).
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ProcessAccessFlags">
            <summary>
            Process access rights list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.AllAccess">
            <summary>
            All possible access rights for a process object.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.CreateProcess">
            <summary>
            Required to create a process.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.CreateThread">
            <summary>
            Required to create a thread.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.DupHandle">
            <summary>
            Required to duplicate a handle using DuplicateHandle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.QueryInformation">
            <summary>
            Required to retrieve certain information about a process, such as its token, exit code, and priority class (see OpenProcessToken).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.QueryLimitedInformation">
            <summary>
            Required to retrieve certain information about a process (see GetExitCodeProcess, GetPriorityClass, IsProcessInJob, QueryFullProcessImageName). 
            A handle that has the PROCESS_QUERY_INFORMATION access right is automatically granted PROCESS_QUERY_LIMITED_INFORMATION.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.SetInformation">
            <summary>
            Required to set certain information about a process, such as its priority class (see SetPriorityClass).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.SetQuota">
            <summary>
            Required to set memory limits using SetProcessWorkingSetSize.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.SuspendResume">
            <summary>
            Required to suspend or resume a process.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.Terminate">
            <summary>
            Required to terminate a process using TerminateProcess.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.VmOperation">
            <summary>
            Required to perform an operation on the address space of a process (see VirtualProtectEx and WriteProcessMemory).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.VmRead">
            <summary>
            Required to read memory in a process using <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.ReadProcessMemory(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Byte[],System.Int32,System.Int32@)"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.VmWrite">
            <summary>
            Required to write to memory in a process using WriteProcessMemory.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessAccessFlags.Synchronize">
            <summary>
            Required to wait for the process to terminate using the wait functions.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ProcessInformationClass">
            <summary>
            The type of process information to be retrieved.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessInformationClass.ProcessBasicInformation">
            <summary>
            Retrieves a pointer to a PEB structure that can be used to determine whether the specified process is being debugged, 
            and a unique value used by the system to identify the specified process. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessInformationClass.ProcessDebugPort">
            <summary>
            Retrieves a DWORD_PTR value that is the port number of the debugger for the process. 
            A nonzero value indicates that the process is being run under the control of a ring 3 debugger.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessInformationClass.ProcessWow64Information">
            <summary>
            Determines whether the process is running in the WOW64 environment (WOW64 is the x86 emulator that allows Win32-based applications to run on 64-bit Windows).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessInformationClass.ProcessImageFileName">
            <summary>
            Retrieves a UNICODE_STRING value containing the name of the image file for the process.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.SystemMetrics">
            <summary>
            The system metrics list used in the function <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetSystemMetrics(Binarysharp.MemoryManagement.Native.SystemMetrics)"/>.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.TebStructure">
            <summary>
            The structure of the Thread Environment Block.
            </summary>
            <remarks>Tested on Windows 7 x64, 2013-03-10.</remarks>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.CurrentSehFrame">
            <summary>
            Current Structured Exception Handling (SEH) frame.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.TopOfStack">
            <summary>
            The top of stack.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.BottomOfStack">
            <summary>
            The current bottom of stack.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.SubSystemTeb">
            <summary>
            The TEB sub system.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.FiberData">
            <summary>
            The fiber data.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.ArbitraryDataSlot">
            <summary>
            The arbitrary data slot.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.Teb">
            <summary>
            The linear address of Thread Environment Block (TEB).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.EnvironmentPointer">
            <summary>
            The environment pointer.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.ProcessId">
            <summary>
            The process Id.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.ThreadId">
            <summary>
            The current thread Id.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.RpcHandle">
            <summary>
            The active RPC handle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.Tls">
            <summary>
            The linear address of the thread-local storage (TLS) array.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.Peb">
            <summary>
            The linear address of Process Environment Block (PEB).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.LastErrorNumber">
            <summary>
            The last error number.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.CriticalSectionsCount">
            <summary>
            The count of owned critical sections.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.CsrClientThread">
            <summary>
            The address of CSR Client Thread.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.Win32ThreadInfo">
            <summary>
            Win32 Thread Information.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.Win32ClientInfo">
            <summary>
            Win32 client information (NT), user32 private data (Wine), 0x60 = LastError (Win95), 0x74 = LastError (WinME). (length: 124 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.WoW64Reserved">
            <summary>
            Reserved for Wow64. Contains a pointer to FastSysCall in Wow64.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.CurrentLocale">
            <summary>
            The current locale.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.FpSoftwareStatusRegister">
            <summary>
            The FP Software Status Register.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.SystemReserved1">
            <summary>
            Reserved for OS (NT), kernel32 private data (Wine). (length: 216 bytes)
            herein: FS:[0x124] 4 NT Pointer to KTHREAD (ETHREAD) structure.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.ExceptionCode">
            <summary>
            The exception code.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.ActivationContextStack">
            <summary>
            The activation context stack. (length: 18 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.SpareBytes">
            <summary>
            The spare bytes (NT), ntdll private data (Wine). (length: 24 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.SystemReserved2">
            <summary>
            Reserved for OS (NT), ntdll private data (Wine). (length: 40 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.GdiTebBatch">
            <summary>
            The GDI TEB Batch (OS), vm86 private data (Wine). (length: 1248 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.GdiRegion">
            <summary>
            The GDI Region.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.GdiPen">
            <summary>
            The GDI Pen.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.GdiBrush">
            <summary>
            The GDI Brush.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.RealProcessId">
            <summary>
            The real process Id.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.RealThreadId">
            <summary>
            The real thread Id.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.GdiCachedProcessHandle">
            <summary>
            The GDI cached process handle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.GdiClientProcessId">
            <summary>
            The GDI client process Id (PID).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.GdiClientThreadId">
            <summary>
            The GDI client thread Id (TID).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.GdiThreadLocalInfo">
            <summary>
            The GDI thread locale information.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.UserReserved1">
            <summary>
            Reserved for user application. (length: 20 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.GlReserved1">
            <summary>
            Reserved for GL. (length: 1248 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.LastStatusValue">
            <summary>
            The last value status value.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.StaticUnicodeString">
            <summary>
            The static UNICODE_STRING buffer. (length: 532 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.DeallocationStack">
            <summary>
            The pointer to deallocation stack.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.TlsSlots">
            <summary>
            The TLS slots, 4 byte per slot. (length: 256 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.TlsLinks">
            <summary>
            The TLS links (LIST_ENTRY structure). (length 8 bytes)
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.Vdm">
            <summary>
            Virtual DOS Machine.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.RpcReserved">
            <summary>
            Reserved for RPC.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TebStructure.ThreadErrorMode">
            <summary>
            The thread error mode (RtlSetThreadErrorMode).
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ThreadAccessFlags">
            <summary>
            Thread access rights list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.Synchronize">
            <summary>
            Enables the use of the thread handle in any of the wait functions.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.AllAccess">
            <summary>
            All possible access rights for a thread object.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.DirectImpersonation">
            <summary>
            Required for a server thread that impersonates a client.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.GetContext">
            <summary>
            Required to read the context of a thread using <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext@)"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.Impersonate">
            <summary>
            Required to use a thread's security information directly without calling it by using a communication mechanism that provides impersonation services.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.QueryInformation">
            <summary>
            Required to read certain information from the thread object, such as the exit code (see GetExitCodeThread).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.QueryLimitedInformation">
            <summary>
            Required to read certain information from the thread objects (see <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext@)"/>). 
            A handle that has the THREAD_QUERY_INFORMATION access right is automatically granted THREAD_QUERY_LIMITED_INFORMATION.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.SetContext">
            <summary>
            Required to write the context of a thread using <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.SetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext@)"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.SetInformation">
            <summary>
            Required to set certain information in the thread object.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.SetLimitedInformation">
            <summary>
            Required to set certain information in the thread object. A handle that has the THREAD_SET_INFORMATION access right is automatically granted THREAD_SET_LIMITED_INFORMATION.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.SetThreadToken">
            <summary>
            Required to set the impersonation token for a thread using SetThreadToken.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.SuspendResume">
            <summary>
            Required to suspend or resume a thread (see <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.SuspendThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)"/> and <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.ResumeThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)"/>).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.Terminate">
            <summary>
            Required to terminate a thread using <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.TerminateThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.Int32)"/>.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ThreadContextFlags">
            <summary>
            Determines which registers are returned or set when using <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext@)"/> or <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.SetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext@)"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Intel386">
            <summary>
            The Intel 80386 microprocessor, also known as the i386.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Intel486">
            <summary>
            The Intel 80486 microprocessor, also known as the i486.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Control">
            <summary>
            SS:SP, CS:IP, FLAGS, BP
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Integer">
            <summary>
            AX, BX, CX, DX, SI, DI
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Segments">
            <summary>
            DS, ES, FS, GS
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.FloatingPoint">
            <summary>
            387 state
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.DebugRegisters">
            <summary>
            DB 0-3,6,7
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.ExtendedRegisters">
            <summary>
            CPU specific extensions
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Full">
            <summary>
            All flags excepted FloatingPoint, DebugRegisters and ExtendedRegisters. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.All">
            <summary>
            All flags.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ThreadCreationFlags">
            <summary>
            Thread creation options list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadCreationFlags.Run">
            <summary>
            The thread runs immediately after creation.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadCreationFlags.Suspended">
            <summary>
            The thread is created in a suspended state, and does not run until the <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.ResumeThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)"/> function is called.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadCreationFlags.StackSizeParamIsAReservation">
            <summary>
            The dwStackSize parameter specifies the initial reserve size of the stack. If this flag is not specified, dwStackSize specifies the commit size.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.TranslationTypes">
            <summary>
            The translation types used in the function <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.MapVirtualKey(System.UInt32,Binarysharp.MemoryManagement.Native.TranslationTypes)"/> for the keys mapping.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TranslationTypes.VirtualKeyToScanCode">
            <summary>
            uCode is a virtual-key code and is translated into a scan code. 
            If it is a virtual-key code that does not distinguish between left- and right-hand keys, the left-hand scan code is returned. 
            If there is no translation, the function returns 0.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TranslationTypes.ScanCodeToVirtualKey">
            <summary>
            uCode is a scan code and is translated into a virtual-key code that does not distinguish between left- and right-hand keys. 
            If there is no translation, the function returns 0.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TranslationTypes.VirtualKeyToChar">
            <summary>
            uCode is a virtual-key code and is translated into an unshifted character value in the low-order word of the return value. 
            Dead keys (diacritics) are indicated by setting the top bit of the return value. 
            If there is no translation, the function returns 0.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.TranslationTypes.ScanCodeToVirtualKeyEx">
            <summary>
            uCode is a scan code and is translated into a virtual-key code that distinguishes between left- and right-hand keys. 
            If there is no translation, the function returns 0.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.WaitValues">
            <summary>
            The return values for the function <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.WaitForSingleObject(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.UInt32)"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WaitValues.Abandoned">
            <summary>
            The specified object is a mutex object that was not released by the thread that owned the mutex object before the owning thread terminated.
            Ownership of the mutex object is granted to the calling thread and the mutex state is set to nonsignaled.
            If the mutex was protecting persistent state information, you should check it for consistency.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WaitValues.Signaled">
            <summary>
            The state of the specified object is signaled. Similar to WAIT_OBJECT_0.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WaitValues.Timeout">
            <summary>
            The time-out interval elapsed, and the object's state is nonsignaled.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WaitValues.Failed">
            <summary>
            The function has failed. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.WindowsMessages">
            <summary>
            Windows Messages list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Null">
            <summary>
            The WM_NULL message performs no operation. An application sends the WM_NULL message if it wants to post a message that the recipient window will ignore.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Create">
            <summary>
            The WM_CREATE message is sent when an application requests that a window be created by calling the CreateWindowEx or CreateWindow function. (The message is sent before the function returns.) The window procedure of the new window receives this message after the window is created, but before the window becomes visible.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Destroy">
            <summary>
            The WM_DESTROY message is sent when a window is being destroyed. It is sent to the window procedure of the window being destroyed after the window is removed from the screen.
            This message is sent first to the window being destroyed and then to the child windows (if any) as they are destroyed. During the processing of the message, it can be assumed that all child windows still exist.
            /// </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Move">
            <summary>
            The WM_MOVE message is sent after a window has been moved.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Size">
            <summary>
            The WM_SIZE message is sent to a window after its size has changed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Activate">
            <summary>
            The WM_ACTIVATE message is sent to both the window being activated and the window being deactivated. If the windows use the same input queue, the message is sent synchronously, first to the window procedure of the top-level window being deactivated, then to the window procedure of the top-level window being activated. If the windows use different input queues, the message is sent asynchronously, so the window is activated immediately.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SetFocus">
            <summary>
            The WM_SETFOCUS message is sent to a window after it has gained the keyboard focus.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KillFocus">
            <summary>
            The WM_KILLFOCUS message is sent to a window immediately before it loses the keyboard focus.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Enable">
            <summary>
            The WM_ENABLE message is sent when an application changes the enabled state of a window. It is sent to the window whose enabled state is changing. This message is sent before the EnableWindow function returns, but after the enabled state (WS_DISABLED style bit) of the window has changed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SetRedraw">
            <summary>
            An application sends the WM_SETREDRAW message to a window to allow changes in that window to be redrawn or to prevent changes in that window from being redrawn.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SetText">
            <summary>
            An application sends a WM_SETTEXT message to set the text of a window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.GetText">
            <summary>
            An application sends a WM_GETTEXT message to copy the text that corresponds to a window into a buffer provided by the caller.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.GetTextLength">
            <summary>
            An application sends a WM_GETTEXTLENGTH message to determine the length, in characters, of the text associated with a window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Paint">
            <summary>
            The WM_PAINT message is sent when the system or another application makes a request to paint a portion of an application's window. The message is sent when the UpdateWindow or RedrawWindow function is called, or by the DispatchMessage function when the application obtains a WM_PAINT message by using the GetMessage or PeekMessage function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Close">
            <summary>
            The WM_CLOSE message is sent as a signal that a window or an application should terminate.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.QueryEndSession">
            <summary>
            The WM_QUERYENDSESSION message is sent when the user chooses to end the session or when an application calls one of the system shutdown functions. If any application returns zero, the session is not ended. The system stops sending WM_QUERYENDSESSION messages as soon as one application returns zero.
            After processing this message, the system sends the WM_ENDSESSION message with the wParam parameter set to the results of the WM_QUERYENDSESSION message.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.QueryOpen">
            <summary>
            The WM_QUERYOPEN message is sent to an icon when the user requests that the window be restored to its previous size and position.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.EndSession">
            <summary>
            The WM_ENDSESSION message is sent to an application after the system processes the results of the WM_QUERYENDSESSION message. The WM_ENDSESSION message informs the application whether the session is ending.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Quit">
            <summary>
            The WM_QUIT message indicates a request to terminate an application and is generated when the application calls the PostQuitMessage function. It causes the GetMessage function to return zero.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.EraseBkgnd">
            <summary>
            The WM_ERASEBKGND message is sent when the window background must be erased (for example, when a window is resized). The message is sent to prepare an invalidated portion of a window for painting.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SysColorChange">
            <summary>
            This message is sent to all top-level windows when a change is made to a system color setting.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ShowWindow">
            <summary>
            The WM_SHOWWINDOW message is sent to a window when the window is about to be hidden or shown.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.WinInitChange">
            <summary>
            An application sends the WM_WININICHANGE message to all top-level windows after making a change to the WIN.INI file. The SystemParametersInfo function sends this message after an application uses the function to change a setting in WIN.INI.
            Note  The WM_WININICHANGE message is provided only for compatibility with earlier versions of the system. Applications should use the WM_SETTINGCHANGE message.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SettingChange">
            <summary>
            An application sends the WM_WININICHANGE message to all top-level windows after making a change to the WIN.INI file. The SystemParametersInfo function sends this message after an application uses the function to change a setting in WIN.INI.
            Note  The WM_WININICHANGE message is provided only for compatibility with earlier versions of the system. Applications should use the WM_SETTINGCHANGE message.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DevModeChange">
            <summary>
            The WM_DEVMODECHANGE message is sent to all top-level windows whenever the user changes device-mode settings.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ActivateApp">
            <summary>
            The WM_ACTIVATEAPP message is sent when a window belonging to a different application than the active window is about to be activated. The message is sent to the application whose window is being activated and to the application whose window is being deactivated.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.FontChange">
            <summary>
            An application sends the WM_FONTCHANGE message to all top-level windows in the system after changing the pool of font resources.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.TimeChange">
            <summary>
            A message that is sent whenever there is a change in the system time.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CancelMode">
            <summary>
            The WM_CANCELMODE message is sent to cancel certain modes, such as mouse capture. For example, the system sends this message to the active window when a dialog box or message box is displayed. Certain functions also send this message explicitly to the specified window regardless of whether it is the active window. For example, the EnableWindow function sends this message when disabling the specified window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SetCursor">
            <summary>
            The WM_SETCURSOR message is sent to a window if the mouse causes the cursor to move within a window and mouse input is not captured.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MouseActivate">
            <summary>
            The WM_MOUSEACTIVATE message is sent when the cursor is in an inactive window and the user presses a mouse button. The parent window receives this message only if the child window passes it to the DefWindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ChildActivate">
            <summary>
            The WM_CHILDACTIVATE message is sent to a child window when the user clicks the window's title bar or when the window is activated, moved, or sized.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.QueueSync">
            <summary>
            The WM_QUEUESYNC message is sent by a computer-based training (CBT) application to separate user-input messages from other messages sent through the WH_JOURNALPLAYBACK Hook procedure.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.GetMinmaxInfo">
            <summary>
            The WM_GETMINMAXINFO message is sent to a window when the size or position of the window is about to change. An application can use this message to override the window's default maximized size and position, or its default minimum or maximum tracking size.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.PaintIcon">
            <summary>
            Windows NT 3.51 and earlier: The WM_PAINTICON message is sent to a minimized window when the icon is to be painted. This message is not sent by newer versions of Microsoft Windows, except in unusual circumstances explained in the Remarks.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.IconeRaseBkgnd">
            <summary>
            Windows NT 3.51 and earlier: The WM_ICONERASEBKGND message is sent to a minimized window when the background of the icon must be filled before painting the icon. A window receives this message only if a class icon is defined for the window; otherwise, WM_ERASEBKGND is sent. This message is not sent by newer versions of Windows.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NextDlgCtl">
            <summary>
            The WM_NEXTDLGCTL message is sent to a dialog box procedure to set the keyboard focus to a different control in the dialog box.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SpoolerStatus">
            <summary>
            The WM_SPOOLERSTATUS message is sent from Print Manager whenever a job is added to or removed from the Print Manager queue.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DrawItem">
            <summary>
            The WM_DRAWITEM message is sent to the parent window of an owner-drawn button, combo box, list box, or menu when a visual aspect of the button, combo box, list box, or menu has changed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MeasureItem">
            <summary>
            The WM_MEASUREITEM message is sent to the owner window of a combo box, list box, list view control, or menu item when the control or menu is created.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DeleteItem">
            <summary>
            Sent to the owner of a list box or combo box when the list box or combo box is destroyed or when items are removed by the LB_DELETESTRING, LB_RESETCONTENT, CB_DELETESTRING, or CB_RESETCONTENT message. The system sends a WM_DELETEITEM message for each deleted item. The system sends the WM_DELETEITEM message for any deleted list box or combo box item with nonzero item data.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.VkeyToItem">
            <summary>
            Sent by a list box with the LBS_WANTKEYBOARDINPUT style to its owner in response to a WM_KEYDOWN message.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CharToItem">
            <summary>
            Sent by a list box with the LBS_WANTKEYBOARDINPUT style to its owner in response to a WM_CHAR message.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SetFont">
            <summary>
            An application sends a WM_SETFONT message to specify the font that a control is to use when drawing text.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.GetFont">
            <summary>
            An application sends a WM_GETFONT message to a control to retrieve the font with which the control is currently drawing its text.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SetHotKey">
            <summary>
            An application sends a WM_SETHOTKEY message to a window to associate a hot key with the window. When the user presses the hot key, the system activates the window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.GetHotKey">
            <summary>
            An application sends a WM_GETHOTKEY message to determine the hot key associated with a window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.QueryDragIcon">
            <summary>
            The WM_QUERYDRAGICON message is sent to a minimized (iconic) window. The window is about to be dragged by the user but does not have an icon defined for its class. An application can return a handle to an icon or cursor. The system displays this cursor or icon while the user drags the icon.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CompareItem">
            <summary>
            The system sends the WM_COMPAREITEM message to determine the relative position of a new item in the sorted list of an owner-drawn combo box or list box. Whenever the application adds a new item, the system sends this message to the owner of a combo box or list box created with the CBS_SORT or LBS_SORT style.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.GetObject">
            <summary>
            Active Accessibility sends the WM_GETOBJECT message to obtain information about an accessible object contained in a server application.
            Applications never send this message directly. It is sent only by Active Accessibility in response to calls to AccessibleObjectFromPoint, AccessibleObjectFromEvent, or AccessibleObjectFromWindow. However, server applications handle this message.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Compacting">
            <summary>
            The WM_COMPACTING message is sent to all top-level windows when the system detects more than 12.5 percent of system time over a 30- to 60-second interval is being spent compacting memory. This indicates that system memory is low.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CommNotify">
            <summary>
            WM_COMMNOTIFY is Obsolete for Win32-Based Applications
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.WindowPosChanging">
            <summary>
            The WM_WINDOWPOSCHANGING message is sent to a window whose size, position, or place in the Z order is about to change as a result of a call to the SetWindowPos function or another window-management function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.WindowPosChanged">
            <summary>
            The WM_WINDOWPOSCHANGED message is sent to a window whose size, position, or place in the Z order has changed as a result of a call to the SetWindowPos function or another window-management function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Power">
            <summary>
            Notifies applications that the system, typically a battery-powered personal computer, is about to enter a suspended mode.
            Use: POWERBROADCAST
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CopyData">
            <summary>
            An application sends the WM_COPYDATA message to pass data to another application.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CancelJournal">
            <summary>
            The WM_CANCELJOURNAL message is posted to an application when a user cancels the application's journaling activities. The message is posted with a NULL window handle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Notify">
            <summary>
            Sent by a common control to its parent window when an event has occurred or the control requires some information.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.InputLangChangeRequest">
            <summary>
            The WM_INPUTLANGCHANGEREQUEST message is posted to the window with the focus when the user chooses a new input language, either with the hotkey (specified in the Keyboard control panel application) or from the indicator on the system taskbar. An application can accept the change by passing the message to the DefWindowProc function or reject the change (and prevent it from taking place) by returning immediately.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.InputLangChange">
            <summary>
            The WM_INPUTLANGCHANGE message is sent to the topmost affected window after an application's input language has been changed. You should make any application-specific settings and pass the message to the DefWindowProc function, which passes the message to all first-level child windows. These child windows can pass the message to DefWindowProc to have it pass the message to their child windows, and so on.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Tcard">
            <summary>
            Sent to an application that has initiated a training card with Microsoft Windows Help. The message informs the application when the user clicks an authorable button. An application initiates a training card by specifying the HELP_TCARD command in a call to the WinHelp function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Help">
            <summary>
            Indicates that the user pressed the F1 key. If a menu is active when F1 is pressed, WM_HELP is sent to the window associated with the menu; otherwise, WM_HELP is sent to the window that has the keyboard focus. If no window has the keyboard focus, WM_HELP is sent to the currently active window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.UserChanged">
            <summary>
            The WM_USERCHANGED message is sent to all windows after the user has logged on or off. When the user logs on or off, the system updates the user-specific settings. The system sends this message immediately after updating the settings.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NotifyFormat">
            <summary>
            Determines if a window accepts ANSI or Unicode structures in the WM_NOTIFY notification message. WM_NOTIFYFORMAT messages are sent from a common control to its parent window and from the parent window to the common control.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ContextMenu">
            <summary>
            The WM_CONTEXTMENU message notifies a window that the user clicked the right mouse button (right-clicked) in the window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.StyleChanging">
            <summary>
            The WM_STYLECHANGING message is sent to a window when the SetWindowLong function is about to change one or more of the window's styles.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.StyleChanged">
            <summary>
            The WM_STYLECHANGED message is sent to a window after the SetWindowLong function has changed one or more of the window's styles
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DisplayChange">
            <summary>
            The WM_DISPLAYCHANGE message is sent to all windows when the display resolution has changed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.GetIcon">
            <summary>
            The WM_GETICON message is sent to a window to retrieve a handle to the large or small icon associated with a window. The system displays the large icon in the ALT+TAB dialog, and the small icon in the window caption.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SetIcon">
            <summary>
            An application sends the WM_SETICON message to associate a new large or small icon with a window. The system displays the large icon in the ALT+TAB dialog box, and the small icon in the window caption.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcCreate">
            <summary>
            The WM_NCCREATE message is sent prior to the WM_CREATE message when a window is first created.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcDestroy">
            <summary>
            The WM_NCDESTROY message informs a window that its nonclient area is being destroyed. The DestroyWindow function sends the WM_NCDESTROY message to the window following the WM_DESTROY message. WM_DESTROY is used to free the allocated memory object associated with the window.
            The WM_NCDESTROY message is sent after the child windows have been destroyed. In contrast, WM_DESTROY is sent before the child windows are destroyed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcCalcSize">
            <summary>
            The WM_NCCALCSIZE message is sent when the size and position of a window's client area must be calculated. By processing this message, an application can control the content of the window's client area when the size or position of the window changes.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcHitTest">
            <summary>
            The WM_NCHITTEST message is sent to a window when the cursor moves, or when a mouse button is pressed or released. If the mouse is not captured, the message is sent to the window beneath the cursor. Otherwise, the message is sent to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcPaint">
            <summary>
            The WM_NCPAINT message is sent to a window when its frame must be painted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcActivate">
            <summary>
            The WM_NCACTIVATE message is sent to a window when its nonclient area needs to be changed to indicate an active or inactive state.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.GetDlgCode">
            <summary>
            The WM_GETDLGCODE message is sent to the window procedure associated with a control. By default, the system handles all keyboard input to the control; the system interprets certain types of keyboard input as dialog box navigation keys. To override this default behavior, the control can respond to the WM_GETDLGCODE message to indicate the types of input it wants to process itself.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SyncPaint">
            <summary>
            The WM_SYNCPAINT message is used to synchronize painting while avoiding linking independent GUI threads.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcMouseMove">
            <summary>
            The WM_NCMOUSEMOVE message is posted to a window when the cursor is moved within the nonclient area of the window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcLButtonDown">
            <summary>
            The WM_NCLBUTTONDOWN message is posted when the user presses the left mouse button while the cursor is within the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcLButtonUp">
            <summary>
            The WM_NCLBUTTONUP message is posted when the user releases the left mouse button while the cursor is within the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcLButtonDblClk">
            <summary>
            The WM_NCLBUTTONDBLCLK message is posted when the user double-clicks the left mouse button while the cursor is within the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcRButtonDown">
            <summary>
            The WM_NCRBUTTONDOWN message is posted when the user presses the right mouse button while the cursor is within the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcRButtonUp">
            <summary>
            The WM_NCRBUTTONUP message is posted when the user releases the right mouse button while the cursor is within the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcRButtonDblClk">
            <summary>
            The WM_NCRBUTTONDBLCLK message is posted when the user double-clicks the right mouse button while the cursor is within the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcMButtonDown">
            <summary>
            The WM_NCMBUTTONDOWN message is posted when the user presses the middle mouse button while the cursor is within the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcMButtonUp">
            <summary>
            The WM_NCMBUTTONUP message is posted when the user releases the middle mouse button while the cursor is within the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcMButtonDblClk">
            <summary>
            The WM_NCMBUTTONDBLCLK message is posted when the user double-clicks the middle mouse button while the cursor is within the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcXButtonDown">
            <summary>
            The WM_NCXBUTTONDOWN message is posted when the user presses the first or second X button while the cursor is in the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcXButtonUp">
            <summary>
            The WM_NCXBUTTONUP message is posted when the user releases the first or second X button while the cursor is in the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcXButtonDblClk">
            <summary>
            The WM_NCXBUTTONDBLCLK message is posted when the user double-clicks the first or second X button while the cursor is in the nonclient area of a window. This message is posted to the window that contains the cursor. If a window has captured the mouse, this message is not posted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.InputDeviceChange">
            <summary>
            The WM_INPUT_DEVICE_CHANGE message is sent to the window that registered to receive raw input. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Input">
            <summary>
            The WM_INPUT message is sent to the window that is getting raw input.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyFirst">
            <summary>
            This message filters for keyboard messages.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyDown">
            <summary>
            The WM_KEYDOWN message is posted to the window with the keyboard focus when a nonsystem key is pressed. A nonsystem key is a key that is pressed when the ALT key is not pressed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyUp">
            <summary>
            The WM_KEYUP message is posted to the window with the keyboard focus when a nonsystem key is released. A nonsystem key is a key that is pressed when the ALT key is not pressed, or a keyboard key that is pressed when a window has the keyboard focus.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Char">
            <summary>
            The WM_CHAR message is posted to the window with the keyboard focus when a WM_KEYDOWN message is translated by the TranslateMessage function. The WM_CHAR message contains the character code of the key that was pressed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DeadChar">
            <summary>
            The WM_DEADCHAR message is posted to the window with the keyboard focus when a WM_KEYUP message is translated by the TranslateMessage function. WM_DEADCHAR specifies a character code generated by a dead key. A dead key is a key that generates a character, such as the umlaut (double-dot), that is combined with another character to form a composite character. For example, the umlaut-O character (Ö) is generated by typing the dead key for the umlaut character, and then typing the O key.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SysKeyDown">
            <summary>
            The WM_SYSKEYDOWN message is posted to the window with the keyboard focus when the user presses the F10 key (which activates the menu bar) or holds down the ALT key and then presses another key. It also occurs when no window currently has the keyboard focus; in this case, the WM_SYSKEYDOWN message is sent to the active window. The window that receives the message can distinguish between these two contexts by checking the context code in the lParam parameter.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SysKeyUp">
            <summary>
            The WM_SYSKEYUP message is posted to the window with the keyboard focus when the user releases a key that was pressed while the ALT key was held down. It also occurs when no window currently has the keyboard focus; in this case, the WM_SYSKEYUP message is sent to the active window. The window that receives the message can distinguish between these two contexts by checking the context code in the lParam parameter.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SysChar">
            <summary>
            The WM_SYSCHAR message is posted to the window with the keyboard focus when a WM_SYSKEYDOWN message is translated by the TranslateMessage function. It specifies the character code of a system character key — that is, a character key that is pressed while the ALT key is down.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SysDeadChar">
            <summary>
            The WM_SYSDEADCHAR message is sent to the window with the keyboard focus when a WM_SYSKEYDOWN message is translated by the TranslateMessage function. WM_SYSDEADCHAR specifies the character code of a system dead key — that is, a dead key that is pressed while holding down the ALT key.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.UniChar">
            <summary>
            The WM_UNICHAR message is posted to the window with the keyboard focus when a WM_KEYDOWN message is translated by the TranslateMessage function. The WM_UNICHAR message contains the character code of the key that was pressed.
            The WM_UNICHAR message is equivalent to WM_CHAR, but it uses Unicode Transformation Format (UTF)-32, whereas WM_CHAR uses UTF-16. It is designed to send or post Unicode characters to ANSI windows and it can can handle Unicode Supplementary Plane characters.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyLast">
            <summary>
            This message filters for keyboard messages.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeStartComposition">
            <summary>
            Sent immediately before the IME generates the composition string as a result of a keystroke. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeEndComposition">
            <summary>
            Sent to an application when the IME ends composition. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeComposition">
            <summary>
            Sent to an application when the IME changes composition status as a result of a keystroke. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeKeyLast">
            <summary>
            Signifies the last keyboard-related input msg (when including IME composition).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.InitDialog">
            <summary>
            The WM_INITDIALOG message is sent to the dialog box procedure immediately before a dialog box is displayed. Dialog box procedures typically use this message to initialize controls and carry out any other initialization tasks that affect the appearance of the dialog box.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Command">
            <summary>
            The WM_COMMAND message is sent when the user selects a command item from a menu, when a control sends a notification message to its parent window, or when an accelerator keystroke is translated.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SysCommand">
            <summary>
            A window receives this message when the user chooses a command from the Window menu, clicks the maximize button, minimize button, restore button, close button, or moves the form. You can stop the form from moving by filtering this out.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Timer">
            <summary>
            The WM_TIMER message is posted to the installing thread's message queue when a timer expires. The message is posted by the GetMessage or PeekMessage function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.HScroll">
            <summary>
            The WM_HSCROLL message is sent to a window when a scroll event occurs in the window's standard horizontal scroll bar. This message is also sent to the owner of a horizontal scroll bar control when a scroll event occurs in the control.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.VScroll">
            <summary>
            The WM_VSCROLL message is sent to a window when a scroll event occurs in the window's standard vertical scroll bar. This message is also sent to the owner of a vertical scroll bar control when a scroll event occurs in the control.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.InitMenu">
            <summary>
            The WM_INITMENU message is sent when a menu is about to become active. It occurs when the user clicks an item on the menu bar or presses a menu key. This allows the application to modify the menu before it is displayed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.InitMenuPopup">
            <summary>
            The WM_INITMENUPOPUP message is sent when a drop-down menu or submenu is about to become active. This allows an application to modify the menu before it is displayed, without changing the entire menu.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MenuSelect">
            <summary>
            The WM_MENUSELECT message is sent to a menu's owner window when the user selects a menu item.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MenuChar">
            <summary>
            The WM_MENUCHAR message is sent when a menu is active and the user presses a key that does not correspond to any mnemonic or accelerator key. This message is sent to the window that owns the menu.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.EnterIdle">
            <summary>
            The WM_ENTERIDLE message is sent to the owner window of a modal dialog box or menu that is entering an idle state. A modal dialog box or menu enters an idle state when no messages are waiting in its queue after it has processed one or more previous messages.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MenuRButtonUp">
            <summary>
            The WM_MENURBUTTONUP message is sent when the user releases the right mouse button while the cursor is on a menu item.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MenuDrag">
            <summary>
            The WM_MENUDRAG message is sent to the owner of a drag-and-drop menu when the user drags a menu item.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MenuGetObject">
            <summary>
            The WM_MENUGETOBJECT message is sent to the owner of a drag-and-drop menu when the mouse cursor enters a menu item or moves from the center of the item to the top or bottom of the item.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.UnInitMenuPopup">
            <summary>
            The WM_UNINITMENUPOPUP message is sent when a drop-down menu or submenu has been destroyed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MenuCommand">
            <summary>
            The WM_MENUCOMMAND message is sent when the user makes a selection from a menu.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ChangeUiState">
            <summary>
            An application sends the WM_CHANGEUISTATE message to indicate that the user interface (UI) state should be changed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.UpdateUiState">
            <summary>
            An application sends the WM_UPDATEUISTATE message to change the user interface (UI) state for the specified window and all its child windows.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.QueryUiState">
            <summary>
            An application sends the WM_QUERYUISTATE message to retrieve the user interface (UI) state for a window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CtlColorMsgBox">
            <summary>
            The WM_CTLCOLORMSGBOX message is sent to the owner window of a message box before Windows draws the message box. By responding to this message, the owner window can set the text and background colors of the message box by using the given display device context handle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CtlColorEdit">
            <summary>
            An edit control that is not read-only or disabled sends the WM_CTLCOLOREDIT message to its parent window when the control is about to be drawn. By responding to this message, the parent window can use the specified device context handle to set the text and background colors of the edit control.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CtlColorListBox">
            <summary>
            Sent to the parent window of a list box before the system draws the list box. By responding to this message, the parent window can set the text and background colors of the list box by using the specified display device context handle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CtlColorBtn">
            <summary>
            The WM_CTLCOLORBTN message is sent to the parent window of a button before drawing the button. The parent window can change the button's text and background colors. However, only owner-drawn buttons respond to the parent window processing this message.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CtlColorDlg">
            <summary>
            The WM_CTLCOLORDLG message is sent to a dialog box before the system draws the dialog box. By responding to this message, the dialog box can set its text and background colors using the specified display device context handle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CtlColorScrollbar">
            <summary>
            The WM_CTLCOLORSCROLLBAR message is sent to the parent window of a scroll bar control when the control is about to be drawn. By responding to this message, the parent window can use the display context handle to set the background color of the scroll bar control.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CtlColorStatic">
            <summary>
            A static control, or an edit control that is read-only or disabled, sends the WM_CTLCOLORSTATIC message to its parent window when the control is about to be drawn. By responding to this message, the parent window can use the specified device context handle to set the text and background colors of the static control.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MouseFirst">
            <summary>
            Use WM_MOUSEFIRST to specify the first mouse message. Use the PeekMessage() Function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MouseMove">
            <summary>
            The WM_MOUSEMOVE message is posted to a window when the cursor moves. If the mouse is not captured, the message is posted to the window that contains the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.LButtonDown">
            <summary>
            The WM_LBUTTONDOWN message is posted when the user presses the left mouse button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.LButtonUp">
            <summary>
            The WM_LBUTTONUP message is posted when the user releases the left mouse button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.LButtonDblClk">
            <summary>
            The WM_LBUTTONDBLCLK message is posted when the user double-clicks the left mouse button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.RButtonDown">
            <summary>
            The WM_RBUTTONDOWN message is posted when the user presses the right mouse button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.RButtonUp">
            <summary>
            The WM_RBUTTONUP message is posted when the user releases the right mouse button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.RButtonDblClk">
            <summary>
            The WM_RBUTTONDBLCLK message is posted when the user double-clicks the right mouse button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MButtonDown">
            <summary>
            The WM_MBUTTONDOWN message is posted when the user presses the middle mouse button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MButtonUp">
            <summary>
            The WM_MBUTTONUP message is posted when the user releases the middle mouse button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MButtonDblClk">
            <summary>
            The WM_MBUTTONDBLCLK message is posted when the user double-clicks the middle mouse button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MouseWheel">
            <summary>
            The WM_MOUSEWHEEL message is sent to the focus window when the mouse wheel is rotated. The DefWindowProc function propagates the message to the window's parent. There should be no internal forwarding of the message, since DefWindowProc propagates it up the parent chain until it finds a window that processes it.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.XButtonDown">
            <summary>
            The WM_XBUTTONDOWN message is posted when the user presses the first or second X button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.XButtonUp">
            <summary>
            The WM_XBUTTONUP message is posted when the user releases the first or second X button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.XButtonDblClk">
            <summary>
            The WM_XBUTTONDBLCLK message is posted when the user double-clicks the first or second X button while the cursor is in the client area of a window. If the mouse is not captured, the message is posted to the window beneath the cursor. Otherwise, the message is posted to the window that has captured the mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MouseHWheel">
            <summary>
            The WM_MOUSEHWHEEL message is sent to the focus window when the mouse's horizontal scroll wheel is tilted or rotated. The DefWindowProc function propagates the message to the window's parent. There should be no internal forwarding of the message, since DefWindowProc propagates it up the parent chain until it finds a window that processes it.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MouseLast">
            <summary>
            Use WM_MOUSELAST to specify the last mouse message. Used with PeekMessage() Function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ParentNotify">
            <summary>
            The WM_PARENTNOTIFY message is sent to the parent of a child window when the child window is created or destroyed, or when the user clicks a mouse button while the cursor is over the child window. When the child window is being created, the system sends WM_PARENTNOTIFY just before the CreateWindow or CreateWindowEx function that creates the window returns. When the child window is being destroyed, the system sends the message before any processing to destroy the window takes place.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.EnterMenuLoop">
            <summary>
            The WM_ENTERMENULOOP message informs an application's main window procedure that a menu modal loop has been entered.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ExitMenuLoop">
            <summary>
            The WM_EXITMENULOOP message informs an application's main window procedure that a menu modal loop has been exited.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NextMenu">
            <summary>
            The WM_NEXTMENU message is sent to an application when the right or left arrow key is used to switch between the menu bar and the system menu.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Sizing">
            <summary>
            The WM_SIZING message is sent to a window that the user is resizing. By processing this message, an application can monitor the size and position of the drag rectangle and, if needed, change its size or position.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CaptureChanged">
            <summary>
            The WM_CAPTURECHANGED message is sent to the window that is losing the mouse capture.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Moving">
            <summary>
            The WM_MOVING message is sent to a window that the user is moving. By processing this message, an application can monitor the position of the drag rectangle and, if needed, change its position.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.PowerBroadcast">
            <summary>
            Notifies applications that a power-management event has occurred.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DeviceChange">
            <summary>
            Notifies an application of a change to the hardware configuration of a device or the computer.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiCreate">
            <summary>
            An application sends the WM_MDICREATE message to a multiple-document interface (MDI) client window to create an MDI child window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiDestroy">
            <summary>
            An application sends the WM_MDIDESTROY message to a multiple-document interface (MDI) client window to close an MDI child window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiActivate">
            <summary>
            An application sends the WM_MDIACTIVATE message to a multiple-document interface (MDI) client window to instruct the client window to activate a different MDI child window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiRestore">
            <summary>
            An application sends the WM_MDIRESTORE message to a multiple-document interface (MDI) client window to restore an MDI child window from maximized or minimized size.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiNext">
            <summary>
            An application sends the WM_MDINEXT message to a multiple-document interface (MDI) client window to activate the next or previous child window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiMaximize">
            <summary>
            An application sends the WM_MDIMAXIMIZE message to a multiple-document interface (MDI) client window to maximize an MDI child window. The system resizes the child window to make its client area fill the client window. The system places the child window's window menu icon in the rightmost position of the frame window's menu bar, and places the child window's restore icon in the leftmost position. The system also appends the title bar text of the child window to that of the frame window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiTile">
            <summary>
            An application sends the WM_MDITILE message to a multiple-document interface (MDI) client window to arrange all of its MDI child windows in a tile format.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiCascade">
            <summary>
            An application sends the WM_MDICASCADE message to a multiple-document interface (MDI) client window to arrange all its child windows in a cascade format.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiIconArrange">
            <summary>
            An application sends the WM_MDIICONARRANGE message to a multiple-document interface (MDI) client window to arrange all minimized MDI child windows. It does not affect child windows that are not minimized.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiGetActive">
            <summary>
            An application sends the WM_MDIGETACTIVE message to a multiple-document interface (MDI) client window to retrieve the handle to the active MDI child window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiSetMenu">
            <summary>
            An application sends the WM_MDISETMENU message to a multiple-document interface (MDI) client window to replace the entire menu of an MDI frame window, to replace the window menu of the frame window, or both.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.EnterSizeMove">
            <summary>
            The WM_ENTERSIZEMOVE message is sent one time to a window after it enters the moving or sizing modal loop. The window enters the moving or sizing modal loop when the user clicks the window's title bar or sizing border, or when the window passes the WM_SYSCOMMAND message to the DefWindowProc function and the wParam parameter of the message specifies the SC_MOVE or SC_SIZE value. The operation is complete when DefWindowProc returns.
            The system sends the WM_ENTERSIZEMOVE message regardless of whether the dragging of full windows is enabled.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ExitSizeMove">
            <summary>
            The WM_EXITSIZEMOVE message is sent one time to a window, after it has exited the moving or sizing modal loop. The window enters the moving or sizing modal loop when the user clicks the window's title bar or sizing border, or when the window passes the WM_SYSCOMMAND message to the DefWindowProc function and the wParam parameter of the message specifies the SC_MOVE or SC_SIZE value. The operation is complete when DefWindowProc returns.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DropFiles">
            <summary>
            Sent when the user drops a file on the window of an application that has registered itself as a recipient of dropped files.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MdiRefreshMenu">
            <summary>
            An application sends the WM_MDIREFRESHMENU message to a multiple-document interface (MDI) client window to refresh the window menu of the MDI frame window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeSetContext">
            <summary>
            Sent to an application when a window is activated. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeNotify">
            <summary>
            Sent to an application to notify it of changes to the IME window. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeControl">
            <summary>
            Sent by an application to direct the IME window to carry out the requested command. The application uses this message to control the IME window that it has created. To send this message, the application calls the SendMessage function with the following parameters.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeCompositionFull">
            <summary>
            Sent to an application when the IME window finds no space to extend the area for the composition window. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeSelect">
            <summary>
            Sent to an application when the operating system is about to change the current IME. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeChar">
            <summary>
            Sent to an application when the IME gets a character of the conversion result. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeRequest">
            <summary>
            Sent to an application to provide commands and request information. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeKeyDown">
            <summary>
            Sent to an application by the IME to notify the application of a key press and to keep message order. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ImeKeyUp">
            <summary>
            Sent to an application by the IME to notify the application of a key release and to keep message order. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MouseHover">
            <summary>
            The WM_MOUSEHOVER message is posted to a window when the cursor hovers over the client area of the window for the period of time specified in a prior call to TrackMouseEvent.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.MouseLeave">
            <summary>
            The WM_MOUSELEAVE message is posted to a window when the cursor leaves the client area of the window specified in a prior call to TrackMouseEvent.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcMouseHover">
            <summary>
            The WM_NCMOUSEHOVER message is posted to a window when the cursor hovers over the nonclient area of the window for the period of time specified in a prior call to TrackMouseEvent.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.NcMouseLeave">
            <summary>
            The WM_NCMOUSELEAVE message is posted to a window when the cursor leaves the nonclient area of the window specified in a prior call to TrackMouseEvent.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.WtsSessionChange">
            <summary>
            The WM_WTSSESSION_CHANGE message notifies applications of changes in session state.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Cut">
            <summary>
            An application sends a WM_CUT message to an edit control or combo box to delete (cut) the current selection, if any, in the edit control and copy the deleted text to the clipboard in CF_TEXT format.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Copy">
            <summary>
            An application sends the WM_COPY message to an edit control or combo box to copy the current selection to the clipboard in CF_TEXT format.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Paste">
            <summary>
            An application sends a WM_PASTE message to an edit control or combo box to copy the current content of the clipboard to the edit control at the current caret position. Data is inserted only if the clipboard contains data in CF_TEXT format.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Clear">
            <summary>
            An application sends a WM_CLEAR message to an edit control or combo box to delete (clear) the current selection, if any, from the edit control.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Undo">
            <summary>
            An application sends a WM_UNDO message to an edit control to undo the last operation. When this message is sent to an edit control, the previously deleted text is restored or the previously added text is deleted.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.RenderFormat">
            <summary>
            The WM_RENDERFORMAT message is sent to the clipboard owner if it has delayed rendering a specific clipboard format and if an application has requested data in that format. The clipboard owner must render data in the specified format and place it on the clipboard by calling the SetClipboardData function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.RenderAllFormats">
            <summary>
            The WM_RENDERALLFORMATS message is sent to the clipboard owner before it is destroyed, if the clipboard owner has delayed rendering one or more clipboard formats. For the content of the clipboard to remain available to other applications, the clipboard owner must render data in all the formats it is capable of generating, and place the data on the clipboard by calling the SetClipboardData function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DestroyClipboard">
            <summary>
            The WM_DESTROYCLIPBOARD message is sent to the clipboard owner when a call to the EmptyClipboard function empties the clipboard.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DrawClipboard">
            <summary>
            The WM_DRAWCLIPBOARD message is sent to the first window in the clipboard viewer chain when the content of the clipboard changes. This enables a clipboard viewer window to display the new content of the clipboard.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.PaintClipboard">
            <summary>
            The WM_PAINTCLIPBOARD message is sent to the clipboard owner by a clipboard viewer window when the clipboard contains data in the CF_OWNERDISPLAY format and the clipboard viewer's client area needs repainting.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.VScrollClipboard">
            <summary>
            The WM_VSCROLLCLIPBOARD message is sent to the clipboard owner by a clipboard viewer window when the clipboard contains data in the CF_OWNERDISPLAY format and an event occurs in the clipboard viewer's vertical scroll bar. The owner should scroll the clipboard image and update the scroll bar values.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SizeClipboard">
            <summary>
            The WM_SIZECLIPBOARD message is sent to the clipboard owner by a clipboard viewer window when the clipboard contains data in the CF_OWNERDISPLAY format and the clipboard viewer's client area has changed size.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.AskCbFormatName">
            <summary>
            The WM_ASKCBFORMATNAME message is sent to the clipboard owner by a clipboard viewer window to request the name of a CF_OWNERDISPLAY clipboard format.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ChangeCbChain">
            <summary>
            The WM_CHANGECBCHAIN message is sent to the first window in the clipboard viewer chain when a window is being removed from the chain.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.HScrollClipboard">
            <summary>
            The WM_HSCROLLCLIPBOARD message is sent to the clipboard owner by a clipboard viewer window. This occurs when the clipboard contains data in the CF_OWNERDISPLAY format and an event occurs in the clipboard viewer's horizontal scroll bar. The owner should scroll the clipboard image and update the scroll bar values.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.QueryNewPalette">
            <summary>
            This message informs a window that it is about to receive the keyboard focus, giving the window the opportunity to realize its logical palette when it receives the focus.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.PaletteIsChanging">
            <summary>
            The WM_PALETTEISCHANGING message informs applications that an application is going to realize its logical palette.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.PaletteChanged">
            <summary>
            This message is sent by the OS to all top-level and overlapped windows after the window with the keyboard focus realizes its logical palette.
            This message enables windows that do not have the keyboard focus to realize their logical palettes and update their client areas.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.HotKey">
            <summary>
            The WM_HOTKEY message is posted when the user presses a hot key registered by the RegisterHotKey function. The message is placed at the top of the message queue associated with the thread that registered the hot key.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.Print">
            <summary>
            The WM_PRINT message is sent to a window to request that it draw itself in the specified device context, most commonly in a printer device context.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.PrintClient">
            <summary>
            The WM_PRINTCLIENT message is sent to a window to request that it draw its client area in the specified device context, most commonly in a printer device context.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.AppCommand">
            <summary>
            The WM_APPCOMMAND message notifies a window that the user generated an application command event, for example, by clicking an application command button using the mouse or typing an application command key on the keyboard.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ThemeChanged">
            <summary>
            The WM_THEMECHANGED message is broadcast to every window following a theme change event. Examples of theme change events are the activation of a theme, the deactivation of a theme, or a transition from one theme to another.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.ClipboardUpdate">
            <summary>
            Sent when the contents of the clipboard have changed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DwmCompositionChanged">
            <summary>
            The system will send a window the WM_DWMCOMPOSITIONCHANGED message to indicate that the availability of desktop composition has changed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DwmNCrenderingChanged">
            <summary>
            WM_DWMNCRENDERINGCHANGED is called when the non-client area rendering status of a window has changed. Only windows that have set the flag DWM_BLURBEHIND.fTransitionOnMaximized to true will get this message.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DwmColorizationColorChanged">
            <summary>
            Sent to all top-level windows when the colorization color has changed.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.DwmWindowMaximizedChange">
            <summary>
            WM_DWMWINDOWMAXIMIZEDCHANGE will let you know when a DWM composed window is maximized. You also have to register for this message as well. You'd have other windowd go opaque when this message is sent.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.GetTitleBarInfoEx">
            <summary>
            Sent to request extended title bar information. A window receives this message through its WindowProc function.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.HandheldFirst">
            <summary>
            Specifies the first hand-held msg.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.HandheldLast">
            <summary>
            Specifies the last hand-held msg.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.AfxFirst">
            <summary>
            Specifies the first afx msg.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.AfxLast">
            <summary>
            Specifies the last afx msg.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.PenwinFirst">
            <summary>
            Specifies the first penwin msg 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.PenwinLast">
            <summary>
            Specifies the last penwin msg 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.App">
            <summary>
            The WM_APP constant is used by applications to help define private messages, usually of the form WM_APP+X, where X is an integer value.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.User">
            <summary>
            The WM_USER constant is used by applications to help define private messages for use by private window classes, usually of the form WM_USER+X, where X is an integer value.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CplLaunch">
            <summary>
            An application sends the WM_CPL_LAUNCH message to Windows Control Panel to request that a Control Panel application be started.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.CplLaunched">
            <summary>
            The WM_CPL_LAUNCHED message is sent when a Control Panel application, started by the WM_CPL_LAUNCH message, has closed. The WM_CPL_LAUNCHED message is sent to the window identified by the wParam parameter of the WM_CPL_LAUNCH message that started the application.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowsMessages.SysTimer">
            <summary>
            WM_SYSTIMER is a well-known yet still undocumented message. Windows uses WM_SYSTIMER for internal actions like scrolling.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.WindowStates">
            <summary>
            Window states list.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.Hide">
            <summary>Hides the window and activates another window.</summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.ShowNormal">
            <summary>
            Activates and displays a window. If the window is minimized or maximized, the system restores it to its original size and position. 
            An application should specify this flag when displaying the window for the first time.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.ShowMinimized">
            <summary>
            Activates the window and displays it as a minimized window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.ShowMaximized">
            <summary>
            Activates the window and displays it as a maximized window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.Maximize">
            <summary>
            Maximizes the specified window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.ShowNormalNoActivate">
            <summary>
            Displays a window in its most recent size and position.
            This value is similar to <see cref="F:Binarysharp.MemoryManagement.Native.WindowStates.ShowNormal"/>, except the window is not actived.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.Show">
            <summary>
            Activates the window and displays it in its current size and position.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.Minimize">
            <summary>
            Minimizes the specified window and activates the next top-level window in the Z order.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.ShowMinNoActive">
            <summary>
            Displays the window as a minimized window. This value is similar to <see cref="F:Binarysharp.MemoryManagement.Native.WindowStates.ShowMinimized"/>, except the window is not activated.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.ShowNoActivate">
            <summary>
            Displays the window in its current size and position. This value is similar to <see cref="F:Binarysharp.MemoryManagement.Native.WindowStates.Show"/>, except the window is not activated.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.Restore">
            <summary>
            Activates and displays the window. If the window is minimized or maximized, the system restores it to its original size and position. 
            An application should specify this flag when restoring a minimized window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.ShowDefault">
            <summary>
            Sets the show state based on the <see cref="T:Binarysharp.MemoryManagement.Native.WindowStates"/> value specified in the STARTUPINFO structure passed to the CreateProcess 
            function by the program that started the application.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowStates.ForceMinimized">
            <summary>
            Windows 2000/XP: Minimizes a window, even if the thread that owns the window is hung. 
            This flag should only be used when minimizing windows from a different thread.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.SafeMemoryHandle">
            <summary>
            Represents a Win32 handle safely managed.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.SafeMemoryHandle.#ctor">
            <summary>
            Parameterless constructor for handles built by the system (like <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.OpenProcess(Binarysharp.MemoryManagement.Native.ProcessAccessFlags,System.Boolean,System.Int32)"/>).
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.SafeMemoryHandle.#ctor(System.IntPtr)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Native.SafeMemoryHandle"/> class, specifying the handle to keep in safe.
            </summary>
            <param name="handle">The handle to keep in safe.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.SafeMemoryHandle.ReleaseHandle">
            <summary>
            Executes the code required to free the handle.
            </summary>
            <returns>True if the handle is released successfully; otherwise, in the event of a catastrophic failure, false. In this case, it generates a releaseHandleFailed MDA Managed Debugging Assistant.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.NativeMethods">
            <summary>
            Static class referencing all P/Invoked functions used by the library.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.CloseHandle(System.IntPtr)">
            <summary>
            Closes an open object handle.
            </summary>
            <param name="hObject">A valid handle to an open object.</param>
            <returns>
            If the function succeeds, the return value is nonzero. 
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.CreateRemoteThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.UInt32,System.IntPtr,System.IntPtr,Binarysharp.MemoryManagement.Native.ThreadCreationFlags,System.Int32@)">
            <summary>
            Creates a thread that runs in the virtual address space of another process.
            Use the CreateRemoteThreadEx function to create a thread that runs in the virtual address space of another processor and optionally specify extended attributes.
            </summary>
            <param name="hProcess">
            A handle to the process in which the thread is to be created. The handle must have the PROCESS_CREATE_THREAD, PROCESS_QUERY_INFORMATION, 
            PROCESS_VM_OPERATION, PROCESS_VM_WRITE, and PROCESS_VM_READ access rights, and may fail without these rights on certain platforms. 
            For more information, see Process Security and Access Rights.
            </param>
            <param name="lpThreadAttributes">
            A pointer to a SECURITY_ATTRIBUTES structure that specifies a security descriptor for the new thread and determines whether child processes can inherit the returned handle. 
            If lpThreadAttributes is NULL, the thread gets a default security descriptor and the handle cannot be inherited. 
            The access control lists (ACL) in the default security descriptor for a thread come from the primary token of the creator.
            </param>
            <param name="dwStackSize">
            The initial size of the stack, in bytes. The system rounds this value to the nearest page. If this parameter is 0 (zero), the new thread uses the default size for the executable. 
            For more information, see Thread Stack Size.</param>
            <param name="lpStartAddress">
            A pointer to the application-defined function of type LPTHREAD_START_ROUTINE to be executed by the thread and represents the starting address of the thread in the remote process. 
            The function must exist in the remote process. For more information, see ThreadProc.
            </param>
            <param name="lpParameter">A pointer to a variable to be passed to the thread function.</param>
            <param name="dwCreationFlags">The flags that control the creation of the thread.</param>
            <param name="lpThreadId">A pointer to a variable that receives the thread identifier. If this parameter is NULL, the thread identifier is not returned.</param>
            <returns>
            If the function succeeds, the return value is a handle to the new thread. 
            If the function fails, the return value is NULL. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.FreeLibrary(System.IntPtr)">
            <summary>
            Frees the loaded dynamic-link library (DLL) module and, if necessary, decrements its reference count. 
            When the reference count reaches zero, the module is unloaded from the address space of the calling process and the handle is no longer valid.
            </summary>
            <param name="hModule">A handle to the loaded library module. The <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.LoadLibrary(System.String)"/> , LoadLibraryEx, GetModuleHandle, or GetModuleHandleEx function returns this handle.</param>
            <returns>
            If the function succeeds, the return value is nonzero. 
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetClassName(System.IntPtr,System.Text.StringBuilder,System.Int32)">
            <summary>
            Retrieves the name of the class to which the specified window belongs.
            </summary>
            <param name="hWnd">A handle to the window and, indirectly, the class to which the window belongs.</param>
            <param name="lpClassName">The class name string.</param>
            <param name="nMaxCount">
            The length of the lpClassName buffer, in characters. 
            The buffer must be large enough to include the terminating null character; otherwise, the class name string is truncated to nMaxCount-1 characters.
            </param>
            <returns>
            If the function succeeds, the return value is the number of characters copied to the buffer, not including the terminating null character.
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>. 
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetForegroundWindow">
            <summary>
            Retrieves a handle to the foreground window (the window with which the user is currently working). 
            The system assigns a slightly higher priority to the thread that creates the foreground window than it does to other threads. 
            </summary>
            <returns>The return value is a handle to the foreground window. The foreground window can be NULL in certain circumstances, such as when a window is losing activation.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetExitCodeThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr@)">
            <summary>
            Retrieves the termination status of the specified thread.
            </summary>
            <param name="hThread">
            A handle to the thread. 
            The handle must have the <see cref="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.QueryInformation"/> or <see cref="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.QueryLimitedInformation"/> access right. 
            For more information, see Thread Security and Access Rights.
            </param>
            <param name="lpExitCode">A pointer to a variable to receive the thread termination status. For more information, see Remarks.</param>
            <returns>
            If the function succeeds, the return value is nonzero.
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetProcAddress(System.IntPtr,System.String)">
            <summary>
            Retrieves the address of an exported function or variable from the specified dynamic-link library (DLL).
            </summary>
            <param name="hModule">
            A handle to the DLL module that contains the function or variable. The LoadLibrary, LoadLibraryEx, LoadPackagedLibrary, or GetModuleHandle function returns this handle. 
            The GetProcAddress function does not retrieve addresses from modules that were loaded using the LOAD_LIBRARY_AS_DATAFILE flag. For more information, see LoadLibraryEx.
            </param>
            <param name="procName">
            The function or variable name, or the function's ordinal value. 
            If this parameter is an ordinal value, it must be in the low-order word; the high-order word must be zero.
            </param>
            <returns>
            If the function succeeds, the return value is the address of the exported function or variable. 
            If the function fails, the return value is NULL. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetProcessId(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Retrieves the process identifier of the specified process.
            </summary>
            <param name="hProcess">
            A handle to the process. The handle must have the PROCESS_QUERY_INFORMATION or PROCESS_QUERY_LIMITED_INFORMATION access right. 
            For more information, see Process Security and Access Rights.
            </param>
            <returns>
            If the function succeeds, the return value is a process id to the specified handle. 
            If the function fails, the return value is NULL. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetSystemMetrics(Binarysharp.MemoryManagement.Native.SystemMetrics)">
            <summary>
            Retrieves the specified system metric or system configuration setting.
            Note that all dimensions retrieved by <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetSystemMetrics(Binarysharp.MemoryManagement.Native.SystemMetrics)"/> are in pixels.
            </summary>
            <param name="metric">The system metric or configuration setting to be retrieved.</param>
            <returns>
            If the function succeeds, the return value is the requested system metric or configuration setting.
            If the function fails, the return value is 0. <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/> does not provide extended error information. 
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext@)">
            <summary>
            Retrieves the context of the specified thread. A 64-bit application can retrieve the context of a WOW64 thread using the Wow64GetThreadContext function.
            </summary>
            <param name="hThread">
            A handle to the thread whose context is to be retrieved. The handle must have <see cref="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.GetContext"/> access to the thread. 
            For more information, see Thread Security and Access Rights.
            WOW64: The handle must also have <see cref="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.QueryInformation"/> access.
            </param>
            <param name="lpContext">
            [Ref] A pointer to a <see cref="T:Binarysharp.MemoryManagement.Native.ThreadContext"/> structure that receives the appropriate context of the specified thread. 
            The value of the ContextFlags member of this structure specifies which portions of a thread's context are retrieved. 
            The <see cref="T:Binarysharp.MemoryManagement.Native.ThreadContext"/> structure is highly processor specific.
            Refer to the WinNT.h header file for processor-specific definitions of this structures and any alignment requirements.
            </param>
            <returns>
            If the function succeeds, the return value is nonzero.
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetThreadSelectorEntry(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.UInt32,Binarysharp.MemoryManagement.Native.LdtEntry@)">
            <summary>
            Retrieves a descriptor table entry for the specified selector and thread.
            </summary>
            <param name="hThread">
            A handle to the thread containing the specified selector.
            The handle must have <see cref="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.QueryInformation"/> access.
            </param>
            <param name="dwSelector">The global or local selector value to look up in the thread's descriptor tables.</param>
            <param name="lpSelectorEntry">
            A pointer to an <see cref="T:Binarysharp.MemoryManagement.Native.LdtEntry"/> structure that receives a copy of the descriptor table entry 
            if the specified selector has an entry in the specified thread's descriptor table. 
            This information can be used to convert a segment-relative address to a linear virtual address.
            </param>
            <returns>
            If the function succeeds, the return value is nonzero. 
            In that case, the structure pointed to by the lpSelectorEntry parameter receives a copy of the specified descriptor table entry.
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetThreadId(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Retrieves the thread identifier of the specified thread.
            </summary>
            <param name="hThread">
            A handle to the thread. The handle must have the THREAD_QUERY_INFORMATION or THREAD_QUERY_LIMITED_INFORMATION access right. 
            For more information about access rights, see Thread Security and Access Rights.
            </param>
            <returns>
            If the function succeeds, the return value is a thread id to the specified handle.
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetWindowPlacement(System.IntPtr,Binarysharp.MemoryManagement.Native.WindowPlacement@)">
            <summary>
            Retrieves the show state and the restored, minimized, and maximized positions of the specified window.
            </summary>
            <param name="hWnd">A handle to the window.</param>
            <param name="lpwndpl">A pointer to the <see cref="T:Binarysharp.MemoryManagement.Native.WindowPlacement"/> structure that receives the show state and position information. 
            Before calling <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetWindowPlacement(System.IntPtr,Binarysharp.MemoryManagement.Native.WindowPlacement@)"/>, set the <see cref="F:Binarysharp.MemoryManagement.Native.WindowPlacement.Length"/> member.</param>
            <returns>
            If the function succeeds, the return value is nonzero.
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>. 
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetWindowText(System.IntPtr,System.Text.StringBuilder,System.Int32)">
            <summary>
            Copies the text of the specified window's title bar (if it has one) into a buffer. If the specified window is a control, the text of the control is copied.
            </summary>
            <param name="hWnd">A handle to the window or control containing the text.</param>
            <param name="lpString">The buffer that will receive the text. If the string is as long or longer than the buffer, the string is truncated and terminated with a null character.</param>
            <param name="nMaxCount">The maximum number of characters to copy to the buffer, including the null character. If the text exceeds this limit, it is truncated.</param>
            <returns>
            If the function succeeds, the return value is the length, in characters, of the copied string, not including the terminating null character. 
            If the window has no title bar or text, if the title bar is empty, or if the window or control handle is invalid, the return value is zero. 
            To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>. 
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetWindowTextLength(System.IntPtr)">
            <summary>
            Retrieves the length, in characters, of the specified window's title bar text (if the window has a title bar). 
            If the specified window is a control, the function retrieves the length of the text within the control.
            </summary>
            <param name="hWnd">A handle to the window or control.</param>
            <returns>
            If the function succeeds, the return value is the length, in characters, of the text. 
            Under certain conditions, this value may actually be greater than the length of the text.
            If the window has no text, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>. 
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetWindowThreadProcessId(System.IntPtr,System.Int32@)">
            <summary>
            Retrieves the identifier of the thread that created the specified window and, optionally, the identifier of the process that created the window.
            </summary>
            <param name="hWnd">A handle to the window.</param>
            <param name="lpdwProcessId">
            [Out] A pointer to a variable that receives the process identifier.
            If this parameter is not <c>NULL</c>, <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetWindowThreadProcessId(System.IntPtr,System.Int32@)"/> copies the identifier of the process to the variable; otherwise, it does not.
            </param>
            <returns>The return value is the identifier of the thread that created the window.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.EnumChildWindows(System.IntPtr,Binarysharp.MemoryManagement.Native.EnumWindowsProc,System.IntPtr)">
            <summary>
            Enumerates the child windows that belong to the specified parent window by passing the handle to each child window, in turn, to an application-defined callback function.
            EnumChildWindows continues until the last child window is enumerated or the callback function returns <c>False</c>.
            </summary>
            <param name="hwndParent">
            A handle to the parent window whose child windows are to be enumerated.
            If this parameter is <see cref="F:System.IntPtr.Zero"/>, this function is equivalent to EnumWindows.
            </param>
            <param name="lpEnumFunc">
            A pointer to an application-defined callback function.
            For more information, see <see cref="T:Binarysharp.MemoryManagement.Native.EnumWindowsProc"/>.
            </param>
            <param name="lParam">An application-defined value to be passed to the callback function.</param>
            <returns>The return value is not used.</returns>
            <remarks>If a child window has created child windows of its own, <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.EnumChildWindows(System.IntPtr,Binarysharp.MemoryManagement.Native.EnumWindowsProc,System.IntPtr)"/> enumerates those windows as well.</remarks>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.FlashWindow(System.IntPtr,System.Boolean)">
            <summary>
            Flashes the specified window one time. It does not change the active state of the window.
            To flash the window a specified number of times, use the <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.FlashWindowEx(Binarysharp.MemoryManagement.Native.FlashInfo@)"/> function.
            </summary>
            <param name="hwnd">A handle to the window to be flashed. The window can be either open or minimized.</param>
            <param name="bInvert">
            If this parameter is <c>True</c>, the window is flashed from one state to the other. 
            If it is <c>False</c>, the window is returned to its original state (either active or inactive).
            When an application is minimized and this parameter is <c>True</c>, the taskbar window button flashes active/inactive. 
            If it is <c>False</c>, the taskbar window button flashes inactive, meaning that it does not change colors. 
            It flashes, as if it were being redrawn, but it does not provide the visual invert clue to the user.
            </param>
            <returns>
            The return value specifies the window's state before the call to the <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.FlashWindow(System.IntPtr,System.Boolean)"/> function. 
            If the window caption was drawn as active before the call, the return value is nonzero. Otherwise, the return value is zero.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.FlashWindowEx(Binarysharp.MemoryManagement.Native.FlashInfo@)">
            <summary>
            Flashes the specified window. It does not change the active state of the window.
            </summary>
            <param name="pwfi">A pointer to a <see cref="T:Binarysharp.MemoryManagement.Native.FlashInfo"/> structure.</param>
            <returns>
            The return value specifies the window's state before the call to the <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.FlashWindowEx(Binarysharp.MemoryManagement.Native.FlashInfo@)"/> function. 
            If the window caption was drawn as active before the call, the return value is nonzero. Otherwise, the return value is zero.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.LoadLibrary(System.String)">
            <summary>
            Loads the specified module into the address space of the calling process. The specified module may cause other modules to be loaded.
            </summary>
            <param name="lpFileName">
            The name of the module. This can be either a library module (a .dll file) or an executable module (an .exe file). 
            The name specified is the file name of the module and is not related to the name stored in the library module itself, 
            as specified by the LIBRARY keyword in the module-definition (.def) file.
            If the string specifies a full path, the function searches only that path for the module.
            If the string specifies a relative path or a module name without a path, the function uses a standard search strategy to find the module; for more information, see the Remarks.
            If the function cannot find the module, the function fails. When specifying a path, be sure to use backslashes (\), not forward slashes (/). 
            For more information about paths, see Naming a File or Directory.
            If the string specifies a module name without a path and the file name extension is omitted, the function appends the default library extension .dll to the module name. 
            To prevent the function from appending .dll to the module name, include a trailing point character (.) in the module name string.
            </param>
            <returns>
            If the function succeeds, the return value is a handle to the module. 
            If the function fails, the return value is NULL. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.MapVirtualKey(System.UInt32,Binarysharp.MemoryManagement.Native.TranslationTypes)">
            <summary>
            Translates (maps) a virtual-key code into a scan code or character value, or translates a scan code into a virtual-key code.
            To specify a handle to the keyboard layout to use for translating the specified code, use the MapVirtualKeyEx function.
            </summary>
            <param name="key">
            The virtual key code or scan code for a key. How this value is interpreted depends on the value of the uMapType parameter.
            </param>
            <param name="translation">
            The translation to be performed. The value of this parameter depends on the value of the uCode parameter.
            </param>
            <returns>
            The return value is either a scan code, a virtual-key code, or a character value, depending on the value of uCode and uMapType. 
            If there is no translation, the return value is zero.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.NtQueryInformationProcess(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ProcessInformationClass,Binarysharp.MemoryManagement.Native.ProcessBasicInformation@,System.Int32,System.IntPtr)">
            <summary>
            Retrieves information about the specified process.
            </summary>
            <param name="processHandle">A handle to the process for which information is to be retrieved.</param>
            <param name="infoclass">The type of process information to be retrieved.</param>
            <param name="processinfo">A pointer to a buffer supplied by the calling application into which the function writes the requested information.</param>
            <param name="length">The size of the buffer pointed to by the ProcessInformation parameter, in bytes.</param>
            <param name="bytesread">
            [Optional] A pointer to a variable in which the function returns the size of the requested information.
            If the function was successful, this is the size of the information written to the buffer pointed to by the ProcessInformation parameter,
            but if the buffer was too small, this is the minimum size of buffer needed to receive the information successfully.
            </param>
            <returns>Returns an NTSTATUS success or error code. (STATUS_SUCCESS = 0x0).</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.NtQueryInformationThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.UInt32,Binarysharp.MemoryManagement.Native.ThreadBasicInformation@,System.Int32,System.IntPtr)">
            <summary>
            Retrieves information about the specified thread.
            </summary>
            <param name="hwnd">A handle to the thread about which information is being requested.</param>
            <param name="infoclass">
            Usually equals to 0 to dump all the structure correctly.
            
            If this parameter is the ThreadIsIoPending value of the THREADINFOCLASS enumeration, the function determines whether the thread has any I/O operations pending.
            If this parameter is the ThreadQuerySetWin32StartAddress value of the THREADINFOCLASS enumeration, the function returns the start address of the thread. 
            Note that on versions of Windows prior to Windows Vista, the returned start address is only reliable before the thread starts running.
            </param>
            <param name="threadinfo">
            A pointer to a buffer in which the function writes the requested information. 
            If ThreadIsIoPending is specified for the ThreadInformationClass parameter, this buffer must be large enough to hold a ULONG value, 
            which indicates whether the specified thread has I/O requests pending. 
            If this value is equal to zero, then there are no I/O operations pending; otherwise, if the value is nonzero, then the thread does have I/O operations pending.
            
            If ThreadQuerySetWin32StartAddress is specified for the ThreadInformationClass parameter, 
            this buffer must be large enough to hold a PVOID value, which is the start address of the thread.
            </param>
            <param name="length">The size of the buffer pointed to by the ThreadInformation parameter, in bytes.</param>
            <param name="bytesread">
            [Optional] A pointer to a variable in which the function returns the size of the requested information. 
            If the function was successful, this is the size of the information written to the buffer pointed to by the ThreadInformation parameter, 
            but if the buffer was too small, this is the minimum size of buffer required to receive the information successfully.
            </param>
            <returns>Returns an NTSTATUS success or error code. (STATUS_SUCCESS = 0x0).</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.OpenProcess(Binarysharp.MemoryManagement.Native.ProcessAccessFlags,System.Boolean,System.Int32)">
            <summary>
            Opens an existing local process object.
            </summary>
            <param name="dwDesiredAccess">
            [Flags] he access to the process object. This access right is checked against the security descriptor for the process. This parameter can be one or more of the process access rights. 
            If the caller has enabled the SeDebugPrivilege privilege, the requested access is granted regardless of the contents of the security descriptor.
            </param>
            <param name="bInheritHandle">If this value is TRUE, processes created by this process will inherit the handle. Otherwise, the processes do not inherit this handle.</param>
            <param name="dwProcessId">The identifier of the local process to be opened.</param>
            <returns>
            If the function succeeds, the return value is an open handle to the specified process. 
            If the function fails, the return value is NULL. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.OpenThread(Binarysharp.MemoryManagement.Native.ThreadAccessFlags,System.Boolean,System.Int32)">
            <summary>
            Opens an existing thread object.
            </summary>
            <param name="dwDesiredAccess">
            The access to the thread object. This access right is checked against the security descriptor for the thread. This parameter can be one or more of the thread access rights. 
            If the caller has enabled the SeDebugPrivilege privilege, the requested access is granted regardless of the contents of the security descriptor.
            </param>
            <param name="bInheritHandle">If this value is TRUE, processes created by this process will inherit the handle. Otherwise, the processes do not inherit this handle.</param>
            <param name="dwThreadId">The identifier of the thread to be opened.</param>
            <returns>
            If the function succeeds, the return value is an open handle to the specified thread. 
            If the function fails, the return value is NULL. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.PostMessage(System.IntPtr,System.UInt32,System.UIntPtr,System.UIntPtr)">
            <summary>
            Places (posts) a message in the message queue associated with the thread that created the specified window and returns without waiting for the thread to process the message.
            To post a message in the message queue associated with a thread, use the PostThreadMessage function.
            </summary>
            <param name="hWnd">A handle to the window whose window procedure is to receive the message. The following values have special meanings.</param>
            <param name="msg">The message to be posted.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
            <returns>
            If the function succeeds, the return value is nonzero.
            If the function fails, the return value is zero. 
            To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>. <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/> returns ERROR_NOT_ENOUGH_QUOTA when the limit is hit. 
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.ReadProcessMemory(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Reads data from an area of memory in a specified process. The entire area to be read must be accessible or the operation fails.
            </summary>
            <param name="hProcess">A handle to the process with memory that is being read. The handle must have PROCESS_VM_READ access to the process.</param>
            <param name="lpBaseAddress">
            A pointer to the base address in the specified process from which to read. Before any data transfer occurs, 
            the system verifies that all data in the base address and memory of the specified size is accessible for read access, 
            and if it is not accessible the function fails.
            </param>
            <param name="lpBuffer">[Out] A pointer to a buffer that receives the contents from the address space of the specified process.</param>
            <param name="dwSize">The number of bytes to be read from the specified process.</param>
            <param name="lpNumberOfBytesRead">
            [Out] A pointer to a variable that receives the number of bytes transferred into the specified buffer. If lpNumberOfBytesRead is NULL, the parameter is ignored.
            </param>
            <returns>
            If the function succeeds, the return value is nonzero. 
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.ResumeThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Decrements a thread's suspend count. When the suspend count is decremented to zero, the execution of the thread is resumed.
            </summary>
            <param name="hThread">
            A handle to the thread to be restarted. 
            This handle must have the <see cref="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.SuspendResume"/> access right. For more information, see Thread Security and Access Rights.
            </param>
            <returns>
            If the function succeeds, the return value is the thread's previous suspend count. 
            If the function fails, the return value is (DWORD) -1. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.SendInput(System.Int32,Binarysharp.MemoryManagement.Native.Input[],System.Int32)">
            <summary>
            Synthesizes keystrokes, mouse motions, and button clicks.
            </summary>
            <param name="nInputs">The number of structures in the pInputs array.</param>
            <param name="pInputs">
            An array of <see cref="T:Binarysharp.MemoryManagement.Native.Input"/> structures. Each structure represents an event to be inserted into the keyboard or mouse input stream.
            </param>
            <param name="cbSize">
            The size, in bytes, of an <see cref="T:Binarysharp.MemoryManagement.Native.Input"/> structure. If <see cref="!:cbSize"/> is not the size of an <see cref="T:Binarysharp.MemoryManagement.Native.Input"/> structure, the function fails.
            </param>
            <returns>
            The function returns the number of events that it successfully inserted into the keyboard or mouse input stream.
            If the function returns zero, the input was already blocked by another thread. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            This function fails when it is blocked by UIPI.
            Note that neither <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/> nor the return value will indicate the failure was caused by UIPI blocking.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.SendMessage(System.IntPtr,System.UInt32,System.UIntPtr,System.IntPtr)">
            <summary>
            Sends the specified message to a window or windows.
            The SendMessage function calls the window procedure for the specified window and does not return until the window procedure has processed the message.
            </summary>
            <param name="hWnd">A handle to the window whose window procedure will receive the message.</param>
            <param name="msg">The message to be sent.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
            <returns>The return value specifies the result of the message processing; it depends on the message sent.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.SetForegroundWindow(System.IntPtr)">
            <summary>
            Brings the thread that created the specified window into the foreground and activates the window. 
            Keyboard input is directed to the window, and various visual cues are changed for the user. 
            The system assigns a slightly higher priority to the thread that created the foreground window than it does to other threads. 
            </summary>
            <param name="hWnd">A handle to the window that should be activated and brought to the foreground.</param>
            <returns>
            If the window was brought to the foreground, the return value is nonzero.
            If the window was not brought to the foreground, the return value is zero.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.SetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext@)">
            <summary>
            Sets the context for the specified thread. A 64-bit application can set the context of a WOW64 thread using the Wow64SetThreadContext function.
            </summary>
            <param name="hThread">
            A handle to the thread whose context is to be set. The handle must have the <see cref="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.SetContext"/> access right to the thread. 
            For more information, see Thread Security and Access Rights.</param>
            <param name="lpContext">
            A pointer to a <see cref="T:Binarysharp.MemoryManagement.Native.ThreadContext"/> structure that contains the context to be set in the specified thread. 
            The value of the ContextFlags member of this structure specifies which portions of a thread's context to set. 
            Some values in the <see cref="T:Binarysharp.MemoryManagement.Native.ThreadContext"/> structure that cannot be specified are silently set to the correct value. 
            This includes bits in the CPU status register that specify the privileged processor mode, global enabling bits in the debugging register, 
            and other states that must be controlled by the operating system.
            </param>
            <returns>
            If the context was set, the return value is nonzero.
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.SetWindowPlacement(System.IntPtr,Binarysharp.MemoryManagement.Native.WindowPlacement@)">
            <summary>
            Sets the show state and the restored, minimized, and maximized positions of the specified window.
            </summary>
            <param name="hWnd">A handle to the window.</param>
            <param name="lpwndpl">A pointer to the <see cref="T:Binarysharp.MemoryManagement.Native.WindowPlacement"/> structure that specifies the new show state and window positions.</param>
            <returns>
            If the function succeeds, the return value is nonzero.
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>. 
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.SetWindowText(System.IntPtr,System.String)">
            <summary>
            Changes the text of the specified window's title bar (if it has one). If the specified window is a control, the text of the control is changed.
            </summary>
            <param name="hwnd">A handle to the window or control whose text is to be changed.</param>
            <param name="lpString">The new title or control text.</param>
            <returns>
            If the function succeeds, the return value is nonzero.
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>. 
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.ShowWindow(System.IntPtr,Binarysharp.MemoryManagement.Native.WindowStates)">
            <summary>
            Sets the specified window's show state.
            </summary>
            <param name="hWnd">A handle to the window.</param>
            <param name="nCmdShow">
            Controls how the window is to be shown. 
            This parameter is ignored the first time an application calls ShowWindow, if the program that launched the application provides a STARTUPINFO structure. 
            Otherwise, the first time ShowWindow is called, the value should be the value obtained by the WinMain function in its nCmdShow parameter. 
            In subsequent calls, this parameter can be one of the following values.
            </param>
            <returns>
            If the window was previously visible, the return value is nonzero. 
            If the window was previously hidden, the return value is zero. 
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.SuspendThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Suspends the specified thread. A 64-bit application can suspend a WOW64 thread using the Wow64SuspendThread function.
            </summary>
            <param name="hThread">A handle to the thread that is to be suspended. 
            The handle must have the THREAD_SUSPEND_RESUME access right. For more information, see Thread Security and Access Rights.
            </param>
            <returns>
            If the function succeeds, the return value is the thread's previous suspend count; otherwise, it is (DWORD) -1. 
            To get extended error information, use <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.TerminateThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.Int32)">
            <summary>
            Terminates a thread.
            </summary>
            <param name="hThread">
            A handle to the thread to be terminated. 
            The handle must have the <see cref="F:Binarysharp.MemoryManagement.Native.ThreadAccessFlags.Terminate"/> access right. For more information, see Thread Security and Access Rights.
            </param>
            <param name="dwExitCode">The exit code for the thread. Use the GetExitCodeThread function to retrieve a thread's exit value.</param>
            <returns>
            If the function succeeds, the return value is nonzero. 
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)">
            <summary>
            Reserves or commits a region of memory within the virtual address space of a specified process. The function initializes the memory it allocates to zero, unless MEM_RESET is used.
            To specify the NUMA node for the physical memory, see VirtualAllocExNuma.
            </summary>
            <param name="hProcess">
            The handle to a process. The function allocates memory within the virtual address space of this process. 
            The handle must have the PROCESS_VM_OPERATION access right. For more information, see Process Security and Access Rights.
            </param>
            <param name="lpAddress">
            The pointer that specifies a desired starting address for the region of pages that you want to allocate. 
            If you are reserving memory, the function rounds this address down to the nearest multiple of the allocation granularity. 
            If you are committing memory that is already reserved, the function rounds this address down to the nearest page boundary. 
            To determine the size of a page and the allocation granularity on the host computer, use the GetSystemInfo function.
            </param>
            <param name="dwSize">
            The size of the region of memory to allocate, in bytes. 
            If lpAddress is NULL, the function rounds dwSize up to the next page boundary. 
            If lpAddress is not NULL, the function allocates all pages that contain one or more bytes in the range from lpAddress to lpAddress+dwSize. 
            This means, for example, that a 2-byte range that straddles a page boundary causes the function to allocate both pages.
            </param>
            <param name="flAllocationType">[Flags] The type of memory allocation.</param>
            <param name="flProtect">[Flags] The memory protection for the region of pages to be allocated.</param>
            <returns>
            If the function succeeds, the return value is the base address of the allocated region of pages. 
            If the function fails, the return value is NULL. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualFreeEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryReleaseFlags)">
            <summary>
            Releases, decommits, or releases and decommits a region of memory within the virtual address space of a specified process.
            </summary>
            <param name="hProcess">A handle to a process. The function frees memory within the virtual address space of the process. 
            The handle must have the PROCESS_VM_OPERATION access right. For more information, see Process Security and Access Rights.
            </param>
            <param name="lpAddress">
            A pointer to the starting address of the region of memory to be freed. 
            If the dwFreeType parameter is MEM_RELEASE, lpAddress must be the base address returned by the <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/> function when the region is reserved.
            </param>
            <param name="dwSize">
            The size of the region of memory to free, in bytes. 
            If the dwFreeType parameter is MEM_RELEASE, dwSize must be 0 (zero). 
            The function frees the entire region that is reserved in the initial allocation call to <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/>. 
            If dwFreeType is MEM_DECOMMIT, the function decommits all memory pages that contain one or more bytes in the range from the lpAddress parameter to (lpAddress+dwSize). 
            This means, for example, that a 2-byte region of memory that straddles a page boundary causes both pages to be decommitted. 
            If lpAddress is the base address returned by VirtualAllocEx and dwSize is 0 (zero), the function decommits the entire region that is allocated by <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/>. 
            After that, the entire region is in the reserved state.
            </param>
            <param name="dwFreeType">[Flags] The type of free operation.</param>
            <returns>
            If the function succeeds, the return value is a nonzero value. 
            If the function fails, the return value is 0 (zero). To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualProtectEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags@)">
            <summary>
            Changes the protection on a region of committed pages in the virtual address space of a specified process.
            </summary>
            <param name="hProcess">
            A handle to the process whose memory protection is to be changed. The handle must have the PROCESS_VM_OPERATION access right. 
            For more information, see Process Security and Access Rights.
            </param>
            <param name="lpAddress">
            A pointer to the base address of the region of pages whose access protection attributes are to be changed. 
            All pages in the specified region must be within the same reserved region allocated when calling the VirtualAlloc or VirtualAllocEx function using MEM_RESERVE. 
            The pages cannot span adjacent reserved regions that were allocated by separate calls to VirtualAlloc or <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualAllocEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Int32,Binarysharp.MemoryManagement.Native.MemoryAllocationFlags,Binarysharp.MemoryManagement.Native.MemoryProtectionFlags)"/> using MEM_RESERVE.
            </param>
            <param name="dwSize">
            The size of the region whose access protection attributes are changed, in bytes. 
            The region of affected pages includes all pages containing one or more bytes in the range from the lpAddress parameter to (lpAddress+dwSize). 
            This means that a 2-byte range straddling a page boundary causes the protection attributes of both pages to be changed.
            </param>
            <param name="flNewProtect">
            The memory protection option. This parameter can be one of the memory protection constants. 
            For mapped views, this value must be compatible with the access protection specified when the view was mapped (see MapViewOfFile, MapViewOfFileEx, and MapViewOfFileExNuma).
            </param>
            <param name="lpflOldProtect">
            A pointer to a variable that receives the previous access protection of the first page in the specified region of pages. 
            If this parameter is NULL or does not point to a valid variable, the function fails.
            </param>
            <returns>
            If the function succeeds, the return value is nonzero. 
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualQueryEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,Binarysharp.MemoryManagement.Native.MemoryBasicInformation@,System.Int32)">
            <summary>
            Retrieves information about a range of pages within the virtual address space of a specified process.
            </summary>
            <param name="hProcess">
            A handle to the process whose memory information is queried. 
            The handle must have been opened with the PROCESS_QUERY_INFORMATION access right, which enables using the handle to read information from the process object. 
            For more information, see Process Security and Access Rights.
            </param>
            <param name="lpAddress">
            A pointer to the base address of the region of pages to be queried. 
            This value is rounded down to the next page boundary. 
            To determine the size of a page on the host computer, use the GetSystemInfo function. 
            If lpAddress specifies an address above the highest memory address accessible to the process, the function fails with ERROR_INVALID_PARAMETER.
            </param>
            <param name="lpBuffer">[Out] A pointer to a <see cref="T:Binarysharp.MemoryManagement.Native.MemoryBasicInformation"/> structure in which information about the specified page range is returned.</param>
            <param name="dwLength">The size of the buffer pointed to by the lpBuffer parameter, in bytes.</param>
            <returns>
            The return value is the actual number of bytes returned in the information buffer. 
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.WaitForSingleObject(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.UInt32)">
            <summary>
            Waits until the specified object is in the signaled state or the time-out interval elapses.
            To enter an alertable wait state, use the WaitForSingleObjectEx function. To wait for multiple objects, use the WaitForMultipleObjects.
            </summary>
            <param name="hHandle">
            A handle to the object. For a list of the object types whose handles can be specified, see the following Remarks section.
            If this handle is closed while the wait is still pending, the function's behavior is undefined.
            The handle must have the SYNCHRONIZE access right. For more information, see Standard Access Rights.
            </param>
            <param name="dwMilliseconds">
            The time-out interval, in milliseconds. If a nonzero value is specified, the function waits until the object is signaled or the interval elapses. 
            If dwMilliseconds is zero, the function does not enter a wait state if the object is not signaled; it always returns immediately. 
            If dwMilliseconds is INFINITE, the function will return only when the object is signaled.
            </param>
            <returns>If the function succeeds, the return value indicates the event that caused the function to return.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.NativeMethods.WriteProcessMemory(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Writes data to an area of memory in a specified process. The entire area to be written to must be accessible or the operation fails.
            </summary>
            <param name="hProcess">A handle to the process memory to be modified. The handle must have PROCESS_VM_WRITE and PROCESS_VM_OPERATION access to the process.</param>
            <param name="lpBaseAddress">
            A pointer to the base address in the specified process to which data is written. Before data transfer occurs, the system verifies that 
            all data in the base address and memory of the specified size is accessible for write access, and if it is not accessible, the function fails.
            </param>
            <param name="lpBuffer">A pointer to the buffer that contains data to be written in the address space of the specified process.</param>
            <param name="nSize">The number of bytes to be written to the specified process.</param>
            <param name="lpNumberOfBytesWritten">
            A pointer to a variable that receives the number of bytes transferred into the specified process. 
            This parameter is optional. If lpNumberOfBytesWritten is NULL, the parameter is ignored.
            </param>
            <returns>
            If the function succeeds, the return value is nonzero. 
            If the function fails, the return value is zero. To get extended error information, call <see cref="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error"/>.
            </returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.EnumWindowsProc">
            <summary>
            An application-defined callback function used with the EnumWindows or <c>EnumDesktopWindows</c> function. It receives top-level window handles. 
            The <c>WNDENUMPROC</c> type defines a pointer to this callback function. <see cref="T:Binarysharp.MemoryManagement.Native.EnumWindowsProc"/> is a placeholder for the application-defined function name. 
            </summary>
            <param name="hWnd">A handle to a top-level window.</param>
            <param name="lParam">The application-defined value given in EnumWindows or EnumDesktopWindows.</param>
            <returns>To continue enumeration, the callback function must return <c>True</c>; to stop enumeration, it must return <c>False</c>.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.FlashInfo">
            <summary>
            Contains the flash status for a window and the number of times the system should flash the window.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.HardwareInput">
            <summary>
            Contains information about a simulated message generated by an input device other than a keyboard or mouse. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.HardwareInput.Message">
            <summary>
            The message generated by the input hardware. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.HardwareInput.WParamL">
            <summary>
            The low-order word of the lParam parameter for <see cref="F:Binarysharp.MemoryManagement.Native.HardwareInput.Message"/>. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.HardwareInput.WParamH">
            <summary>
            The high-order word of the lParam parameter for <see cref="F:Binarysharp.MemoryManagement.Native.HardwareInput.Message"/>. 
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.Input">
            <summary>
            Used by <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.SendInput(System.Int32,Binarysharp.MemoryManagement.Native.Input[],System.Int32)"/> to store information for synthesizing input events such as keystrokes, mouse movement, and mouse clicks.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.Input.#ctor(Binarysharp.MemoryManagement.Native.InputTypes)">
            <summary>
            Constructor that specifies a type.
            </summary>
            <param name="type">The type if the input event.</param>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Input.Type">
            <summary>
            The type of the input event.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Input.Mouse">
            <summary>
            The information about a simulated mouse event.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Input.Keyboard">
            <summary>
            The information about a simulated keyboard event.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Input.Hardware">
            <summary>
            The information about a simulated hardware event.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.KeyboardInput">
            <summary>
            Contains information about a simulated keyboard event.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.KeyboardInput.VirtualKey">
            <summary>
            A virtual-key code. The code must be a value in the range 1 to 254. If the <see cref="F:Binarysharp.MemoryManagement.Native.KeyboardInput.Flags"/> member specifies KEYEVENTF_UNICODE, wVk must be 0. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.KeyboardInput.ScanCode">
            <summary>
            A hardware scan code for the key.
            If <see cref="F:Binarysharp.MemoryManagement.Native.KeyboardInput.Flags"/> specifies KEYEVENTF_UNICODE, wScan specifies a Unicode character which is to be sent to the foreground application.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.KeyboardInput.Flags">
            <summary>
            Specifies various aspects of a keystroke.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.KeyboardInput.Time">
            <summary>
            The time stamp for the event, in milliseconds. If this parameter is zero, the system will provide its own time stamp.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.KeyboardInput.ExtraInfo">
            <summary>
            An additional value associated with the keystroke. Use the GetMessageExtraInfo function to obtain this information.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.LdtEntry">
            <summary>
            Describes an entry in the descriptor table. This structure is valid only on x86-based systems.
            </summary>
            <remarks>This is a simplified version of the original structure.</remarks>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.LdtEntry.LimitLow">
            <summary>
            The low-order part of the address of the last byte in the segment.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.LdtEntry.BaseLow">
            <summary>
            The low-order part of the base address of the segment.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.LdtEntry.BaseMid">
            <summary>
            Middle bits (16–23) of the base address of the segment.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.LdtEntry.Flag1">
            <summary>
            Values of the Type, Dpl, and Pres members in the Bits structure (not implemented).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.LdtEntry.Flag2">
            <summary>
            Values of the LimitHi, Sys, Reserved_0, Default_Big, and Granularity members in the Bits structure (not implemented).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.LdtEntry.BaseHi">
            <summary>
            High bits (24–31) of the base address of the segment.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.MemoryBasicInformation">
            <summary>
            Contains information about a range of pages in the virtual address space of a process. The VirtualQuery and <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.VirtualQueryEx(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,Binarysharp.MemoryManagement.Native.MemoryBasicInformation@,System.Int32)"/> functions use this structure.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryBasicInformation.BaseAddress">
            <summary>
            A pointer to the base address of the region of pages.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryBasicInformation.AllocationBase">
            <summary>
            A pointer to the base address of a range of pages allocated by the VirtualAlloc function. The page pointed to by the BaseAddress member is contained within this allocation range.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryBasicInformation.AllocationProtect">
            <summary>
            The memory protection option when the region was initially allocated. This member can be one of the memory protection constants or 0 if the caller does not have access.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryBasicInformation.RegionSize">
            <summary>
            The size of the region beginning at the base address in which all pages have identical attributes, in bytes.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryBasicInformation.State">
            <summary>
            The state of the pages in the region.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryBasicInformation.Protect">
            <summary>
            The access protection of the pages in the region. This member is one of the values listed for the AllocationProtect member.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MemoryBasicInformation.Type">
            <summary>
            The type of pages in the region.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.MouseInput">
            <summary>
            Contains information about a simulated mouse event.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseInput.DeltaX">
            <summary>
            The absolute position of the mouse, or the amount of motion since the last mouse event was generated, depending on the value of the <see cref="F:Binarysharp.MemoryManagement.Native.MouseInput.Flags"/> member. 
            Absolute data is specified as the x coordinate of the mouse; relative data is specified as the number of pixels moved. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseInput.DeltaY">
            <summary>
            The absolute position of the mouse, or the amount of motion since the last mouse event was generated, depending on the value of the <see cref="F:Binarysharp.MemoryManagement.Native.MouseInput.Flags"/> member.
            Absolute data is specified as the y coordinate of the mouse; relative data is specified as the number of pixels moved. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseInput.MouseData">
            <summary>
            If <see cref="F:Binarysharp.MemoryManagement.Native.MouseInput.Flags"/> contains <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.Wheel"/>, then mouseData specifies the amount of wheel movement.
            A positive value indicates that the wheel was rotated forward, away from the user; a negative value indicates that the wheel was rotated backward, toward the user.
            One wheel click is defined as WHEEL_DELTA, which is 120.
            
            Windows Vista: If dwFlags contains <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.HWheel"/>, then dwData specifies the amount of wheel movement.
            A positive value indicates that the wheel was rotated to the right; a negative value indicates that the wheel was rotated to the left.
            One wheel click is defined as WHEEL_DELTA, which is 120.
            
            If dwFlags does not contain <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.Wheel"/>, <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.XDown"/>, or <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.XUp"/>, then mouseData should be zero.
            
            If dwFlags contains <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.XDown"/> or <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.XUp"/>, then mouseData specifies which X buttons were pressed or released.
            This value may be any combination of the following flags. 
            XBUTTON1 = 0x1
            XBUTTON2 = 0x2
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseInput.Flags">
            <summary>
            A set of bit flags that specify various aspects of mouse motion and button clicks.
            The bits in this member can be any reasonable combination of the following values.
            
            The bit flags that specify mouse button status are set to indicate changes in status, not ongoing conditions. 
            For example, if the left mouse button is pressed and held down, <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.LeftDown"/> is set when the left 
            button is first pressed, but not for subsequent motions. Similarly, <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.LeftUp"/> is set only when the button is first released.
            
            You cannot specify both the <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.Wheel"/> flag and either <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.XDown"/> or <see cref="F:Binarysharp.MemoryManagement.Native.MouseFlags.XUp"/> flags 
            simultaneously in the dwFlags parameter, because they both require use of the mouseData field. 
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseInput.Time">
            <summary>
            The time stamp for the event, in milliseconds. If this parameter is 0, the system will provide its own time stamp.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.MouseInput.ExtraInfo">
            <summary>
            An additional value associated with the mouse event. An application calls GetMessageExtraInfo to obtain this extra information. 
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.Point">
            <summary>
            The <see cref="T:Binarysharp.MemoryManagement.Native.Point"/> structure defines the x and y coordinates of a point.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Point.X">
            <summary>
            The x-coordinate of the point.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Point.Y">
            <summary>
            The y-coordinate of the point.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.Point.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ProcessBasicInformation">
            <summary>
            Structure containing basic information about a process.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessBasicInformation.ExitStatus">
            <summary>
            The exit status.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessBasicInformation.PebBaseAddress">
            <summary>
            The base address of Process Environment Block.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessBasicInformation.AffinityMask">
            <summary>
            The affinity mask.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessBasicInformation.BasePriority">
            <summary>
            The base priority.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessBasicInformation.ProcessId">
            <summary>
            The process id.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ProcessBasicInformation.InheritedFromUniqueProcessId">
            <summary>
            The process id of the parent process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.ProcessBasicInformation.Size">
            <summary>
            The size of this structure.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ThreadBasicInformation">
            <summary>
            Structure containing basic information about a thread.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadBasicInformation.ExitStatus">
            <summary>
            the exit status.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadBasicInformation.TebBaseAdress">
            <summary>
            The base address of Thread Environment Block.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadBasicInformation.ProcessId">
            <summary>
            The process id which owns the thread.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadBasicInformation.ThreadId">
            <summary>
            The thread id.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadBasicInformation.AffinityMask">
            <summary>
            The affinity mask.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadBasicInformation.Priority">
            <summary>
            The priority.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadBasicInformation.BasePriority">
            <summary>
            The base priority.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.ThreadContext">
            <summary>
            Represents a thread context.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags">
            <summary>
            Determines which registers are returned or set when using <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.GetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext@)"/> or <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.SetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext@)"/>.
            
            If the context record is used as an INPUT parameter, then for each portion of the context record controlled by a flag whose value is set, it is assumed that portion of the 
            context record contains valid context. If the context record is being used to modify a threads context, then only that portion of the threads context will be modified.
            
            If the context record is used as an INPUT/OUTPUT parameter to capture the context of a thread, then only those portions of the thread's context corresponding to set flags will be returned.
            
            The context record is never used as an OUTPUT only parameter.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Dr0">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.DebugRegisters"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Dr1">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.DebugRegisters"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Dr2">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.DebugRegisters"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Dr3">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.DebugRegisters"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Dr6">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.DebugRegisters"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Dr7">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.DebugRegisters"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.FloatingSave">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.FloatingPoint"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.SegGs">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Segments"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.SegFs">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Segments"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.SegEs">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Segments"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.SegDs">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Segments"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Edi">
            <summary>
            This register is specified/returned if the ContextFlags word contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Integer"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Esi">
            <summary>
            This register is specified/returned if the ContextFlags word contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Integer"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Ebx">
            <summary>
            This register is specified/returned if the ContextFlags word contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Integer"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Edx">
            <summary>
            This register is specified/returned if the ContextFlags word contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Integer"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Ecx">
            <summary>
            This register is specified/returned if the ContextFlags word contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Integer"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Eax">
            <summary>
            This register is specified/returned if the ContextFlags word contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Integer"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Ebp">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Control"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Eip">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Control"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.SegCs">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Control"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.EFlags">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Control"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.Esp">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Control"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.SegSs">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.Control"/>.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.ThreadContext.ExtendedRegisters">
            <summary>
            This is specified/returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContext.ContextFlags"/> contains the flag <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.ExtendedRegisters"/>.
            The format and contexts are processor specific.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.FloatingSaveArea">
            <summary>
            Returned if <see cref="F:Binarysharp.MemoryManagement.Native.ThreadContextFlags.FloatingPoint"/> flag is set.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.Rectangle">
            <summary>
            The <see cref="T:Binarysharp.MemoryManagement.Native.Rectangle"/> structure defines the coordinates of the upper-left and lower-right corners of a rectangle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Rectangle.Left">
            <summary>
            The x-coordinate of the upper-left corner of the rectangle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Rectangle.Top">
            <summary>
            The y-coordinate of the upper-left corner of the rectangle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Rectangle.Right">
            <summary>
            The x-coordinate of the lower-right corner of the rectangle.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.Rectangle.Bottom">
            <summary>
            The y-coordinate of the lower-right corner of the rectangle.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.Rectangle.Height">
            <summary>
            Gets or sets the height of the element.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Native.Rectangle.Width">
            <summary>
            Gets or sets the width of the element.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Native.Rectangle.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Native.WindowPlacement">
            <summary>
            Contains information about the placement of a window on the screen.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowPlacement.Length">
            <summary>
            The length of the structure, in bytes. Before calling the GetWindowPlacement or SetWindowPlacement functions, set this member to sizeof(WINDOWPLACEMENT).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowPlacement.Flags">
            <summary>
            Specifies flags that control the position of the minimized window and the method by which the window is restored.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowPlacement.ShowCmd">
            <summary>
            The current show state of the window.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowPlacement.MinPosition">
            <summary>
            The coordinates of the window's upper-left corner when the window is minimized.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowPlacement.MaxPosition">
            <summary>
            The coordinates of the window's upper-left corner when the window is maximized.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Native.WindowPlacement.NormalPosition">
            <summary>
            The window's coordinates when the window is in the restored position.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Threading.SegmentRegisters">
            <summary>
            List of segment registers.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.SegmentRegisters.Cs">
            <summary>
            The code segment.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.SegmentRegisters.Ds">
            <summary>
            The Data segment.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.SegmentRegisters.Es">
            <summary>
            The extra data segment.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.SegmentRegisters.Fs">
            <summary>
            The points to Thread Information Block (TIB).
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.SegmentRegisters.Gs">
            <summary>
            The extra data segment.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.SegmentRegisters.Ss">
            <summary>
            The stack segment.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Threading.FrozenThread">
            <summary>
            Class containing a frozen thread. If an instance of this class is disposed, its associated thread is resumed.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.FrozenThread.Thread">
            <summary>
            The frozen thread.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.FrozenThread.#ctor(Binarysharp.MemoryManagement.Threading.RemoteThread)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.FrozenThread"/> class.
            </summary>
            <param name="thread">The frozen thread.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.FrozenThread.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.FrozenThread.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Threading.RemoteThread">
            <summary>
            Class repesenting a thread in the remote process.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.RemoteThread.MemorySharp">
            <summary>
            The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.RemoteThread._parameter">
            <summary>
            The parameter passed to the thread when it was created.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.RemoteThread._parameterCleaner">
            <summary>
            The task involved in cleaning the parameter memory when the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> object is collected.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.RemoteThread.Context">
            <summary>
            Gets or sets the full context of the thread.
            If the thread is not already suspended, performs a <see cref="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Suspend"/> and <see cref="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Resume"/> call on the thread.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.RemoteThread.Handle">
            <summary>
            The remote thread handle opened with all rights.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.RemoteThread.Id">
            <summary>
            Gets the unique identifier of the thread.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.RemoteThread.IsAlive">
            <summary>
            Gets if the thread is alive.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.RemoteThread.IsMainThread">
            <summary>
            Gets if the thread is the main one in the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.RemoteThread.IsSuspended">
            <summary>
            Gets if the thread is suspended.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.RemoteThread.IsTerminated">
            <summary>
            Gets if the thread is terminated.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.RemoteThread.Native">
            <summary>
            The native <see cref="T:System.Diagnostics.ProcessThread"/> object corresponding to this thread.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.RemoteThread.Teb">
            <summary>
            The Thread Environment Block of the thread.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.Diagnostics.ProcessThread)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="thread">The native <see cref="T:System.Diagnostics.ProcessThread"/> object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.Diagnostics.ProcessThread,Binarysharp.MemoryManagement.Internals.IMarshalledValue)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="thread">The native <see cref="T:System.Diagnostics.ProcessThread"/> object.</param>
            <param name="parameter">The parameter passed to the thread when it was created.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Finalize">
            <summary>
            Frees resources and perform other cleanup operations before it is reclaimed by garbage collection. 
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Equals(Binarysharp.MemoryManagement.Threading.RemoteThread)">
            <summary>
            Returns a value indicating whether this instance is equal to a specified object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.GetExitCode``1">
            <summary>
            Gets the termination status of the thread.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.GetHashCode">
            <summary>
            Serves as a hash function for a particular type. 
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.GetRealSegmentAddress(Binarysharp.MemoryManagement.Threading.SegmentRegisters)">
            <summary>
            Gets the linear address of a specified segment.
            </summary>
            <param name="segment">The segment to get.</param>
            <returns>A <see cref="T:System.IntPtr"/> pointer corresponding to the linear address of the segment.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Refresh">
            <summary>
            Discards any information about this thread that has been cached inside the process component.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Join">
            <summary>
            Blocks the calling thread until the thread terminates.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Join(System.TimeSpan)">
            <summary>
            Blocks the calling thread until a thread terminates or the specified time elapses.
            </summary>
            <param name="time">The timeout.</param>
            <returns>The return value is a flag that indicates if the thread terminated or if the time elapsed.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Resume">
            <summary>
            Resumes a thread that has been suspended.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Suspend">
            <summary>
            Either suspends the thread, or if the thread is already suspended, has no effect.
            </summary>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.FrozenThread"/> class. If this object is disposed, the thread is resumed.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.Terminate(System.Int32)">
            <summary>
            Terminates the thread.
            </summary>
            <param name="exitCode">The exit code of the thread to close.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.RemoteThread.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Threading.ThreadCore">
            <summary>
            Static core class providing tools for manipulating threads.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.CreateRemoteThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.IntPtr,System.IntPtr,Binarysharp.MemoryManagement.Native.ThreadCreationFlags)">
            <summary>
            Creates a thread that runs in the virtual address space of another process.
            </summary>
            <param name="processHandle">A handle to the process in which the thread is to be created.</param>
            <param name="startAddress">A pointer to the application-defined function to be executed by the thread and represents the starting address of the thread in the remote process.</param>
            <param name="parameter">A pointer to a variable to be passed to the thread function.</param>
            <param name="creationFlags">The flags that control the creation of the thread.</param>
            <returns>A handle to the new thread.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.GetExitCodeThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Retrieves the termination status of the specified thread.
            </summary>
            <param name="threadHandle">A handle to the thread.</param>
            <returns>Nullable type: the return value is A pointer to a variable to receive the thread termination status or <code>null</code> if it is running.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.GetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContextFlags)">
            <summary>
            Retrieves the context of the specified thread.
            </summary>
            <param name="threadHandle">A handle to the thread whose context is to be retrieved.</param>
            <param name="contextFlags">Determines which registers are returned or set.</param>
            <returns>A <see cref="T:Binarysharp.MemoryManagement.Native.ThreadContext"/> structure that receives the appropriate context of the specified thread.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.GetThreadSelectorEntry(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.UInt32)">
            <summary>
            Retrieves a descriptor table entry for the specified selector and thread.
            </summary>
            <param name="threadHandle">A handle to the thread containing the specified selector.</param>
            <param name="selector">The global or local selector value to look up in the thread's descriptor tables.</param>
            <returns>A pointer to an <see cref="T:Binarysharp.MemoryManagement.Native.LdtEntry"/> structure that receives a copy of the descriptor table entry.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.OpenThread(Binarysharp.MemoryManagement.Native.ThreadAccessFlags,System.Int32)">
            <summary>
            Opens an existing thread object.
            </summary>
            <param name="accessFlags">The access to the thread object.</param>
            <param name="threadId">The identifier of the thread to be opened.</param>
            <returns>An open handle to the specified thread.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.NtQueryInformationThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Retrieves information about the specified thread.
            </summary>
            <param name="threadHandle">A handle to the thread to query.</param>
            <returns>A <see cref="T:Binarysharp.MemoryManagement.Native.ThreadBasicInformation"/> structure containg thread information.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.ResumeThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Decrements a thread's suspend count. When the suspend count is decremented to zero, the execution of the thread is resumed.
            </summary>
            <param name="threadHandle">A handle to the thread to be restarted.</param>
            <returns>The thread's previous suspend count.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.SetThreadContext(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,Binarysharp.MemoryManagement.Native.ThreadContext)">
            <summary>
            Sets the context for the specified thread.
            </summary>
            <param name="threadHandle">A handle to the thread whose context is to be set.</param>
            <param name="context">A pointer to a <see cref="T:Binarysharp.MemoryManagement.Native.ThreadContext"/> structure that contains the context to be set in the specified thread.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.SuspendThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Suspends the specified thread.
            </summary>
            <param name="threadHandle">A handle to the thread that is to be suspended.</param>
            <returns>The thread's previous suspend count.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.TerminateThread(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.Int32)">
            <summary>
            Terminates a thread.
            </summary>
            <param name="threadHandle">A handle to the thread to be terminated.</param>
            <param name="exitCode">The exit code for the thread.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.WaitForSingleObject(Binarysharp.MemoryManagement.Native.SafeMemoryHandle,System.Nullable{System.TimeSpan})">
            <summary>
            Waits until the specified object is in the signaled state or the time-out interval elapses.
            </summary>
            <param name="handle">A handle to the object.</param>
            <param name="timeout">The time-out interval. If this parameter is NULL, the function does not enter a wait state if the object is not signaled; it always returns immediately.</param>
            <returns>Indicates the <see cref="T:Binarysharp.MemoryManagement.Native.WaitValues"/> event that caused the function to return.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadCore.WaitForSingleObject(Binarysharp.MemoryManagement.Native.SafeMemoryHandle)">
            <summary>
            Waits an infinite amount of time for the specified object to become signaled.
            </summary>
            <param name="handle">A handle to the object.</param>
            <returns>If the function succeeds, the return value indicates the event that caused the function to return.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Threading.ThreadFactory">
            <summary>
            Class providing tools for manipulating threads.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Threading.ThreadFactory.MemorySharp">
            <summary>
            The reference of the <see cref="F:Binarysharp.MemoryManagement.Threading.ThreadFactory.MemorySharp"/> object.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.ThreadFactory.MainThread">
            <summary>
            Gets the main thread of the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.ThreadFactory.NativeThreads">
            <summary>
            Gets the native threads from the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.ThreadFactory.RemoteThreads">
            <summary>
            Gets the threads from the remote process.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Threading.ThreadFactory.Item(System.Int32)">
            <summary>
            Gets the thread corresponding to an id.
            </summary>
            <param name="threadId">The unique identifier of the thread to get.</param>
            <returns>A new instance of a <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadFactory.#ctor(Binarysharp.MemoryManagement.MemorySharp)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.ThreadFactory"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="F:Binarysharp.MemoryManagement.Threading.ThreadFactory.MemorySharp"/> object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadFactory.Create(System.IntPtr,System.Object,System.Boolean)">
            <summary>
            Creates a thread that runs in the remote process.
            </summary>
            <param name="address">
            A pointer to the application-defined function to be executed by the thread and represents 
            the starting address of the thread in the remote process.
            </param>
            <param name="parameter">A variable to be passed to the thread function.</param>
            <param name="isStarted">Sets if the thread must be started just after being created.</param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadFactory.Create(System.IntPtr,System.Boolean)">
            <summary>
            Creates a thread that runs in the remote process.
            </summary>
            <param name="address">
            A pointer to the application-defined function to be executed by the thread and represents 
            the starting address of the thread in the remote process.
            </param>
            <param name="isStarted">Sets if the thread must be started just after being created.</param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadFactory.CreateAndJoin(System.IntPtr,System.Object)">
            <summary>
            Creates a thread in the remote process and blocks the calling thread until the thread terminates.
            </summary>
            <param name="address">
            A pointer to the application-defined function to be executed by the thread and represents 
            the starting address of the thread in the remote process.
            </param>
            <param name="parameter">A variable to be passed to the thread function.</param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadFactory.CreateAndJoin(System.IntPtr)">
            <summary>
            Creates a thread in the remote process and blocks the calling thread until the thread terminates.
            </summary>
            <param name="address">
            A pointer to the application-defined function to be executed by the thread and represents 
            the starting address of the thread in the remote process.
            </param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadFactory.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Threading.ThreadFactory"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadFactory.GetThreadById(System.Int32)">
            <summary>
            Gets a thread by its id in the remote process.
            </summary>
            <param name="id">The id of the thread.</param>
            <returns>A new instance of the <see cref="T:Binarysharp.MemoryManagement.Threading.RemoteThread"/> class.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadFactory.ResumeAll">
            <summary>
            Resumes all threads.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Threading.ThreadFactory.SuspendAll">
            <summary>
            Suspends all threads.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard">
            <summary>
            Abstract class defining a virtual keyboard.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard.Window">
            <summary>
            The reference of the <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/> object.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard.PressedKeys">
            <summary>
            The collection storing the current pressed keys.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard.#ctor(Binarysharp.MemoryManagement.Windows.RemoteWindow)">
            <summary>
            Initializes a new instance of a child of the <see cref="T:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard"/> class.
            </summary>
            <param name="window">The reference of the <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/> object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard.Press(Binarysharp.MemoryManagement.Native.Keys)">
            <summary>
            Presses the specified virtual key to the window.
            </summary>
            <param name="key">The virtual key to press.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard.Write(System.Char)">
            <summary>
            Writes the specified character to the window.
            </summary>
            <param name="character">The character to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard.Release(Binarysharp.MemoryManagement.Native.Keys)">
            <summary>
            Releases the specified virtual key to the window.
            </summary>
            <param name="key">The virtual key to release.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard.Press(Binarysharp.MemoryManagement.Native.Keys,System.TimeSpan)">
            <summary>
            Presses the specified virtual key to the window at a specified interval.
            </summary>
            <param name="key">The virtual key to press.</param>
            <param name="interval">The interval between the key activations.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard.PressRelease(Binarysharp.MemoryManagement.Native.Keys)">
            <summary>
            Presses and releaes the specified virtual key to the window.
            </summary>
            <param name="key">The virtual key to press and release.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.BaseKeyboard.Write(System.String,System.Object[])">
            <summary>
            Writes the text representation of the specified array of objects to the window using the specified format information.
            </summary>
            <param name="text">A composite format string.</param>
            <param name="args">An array of objects to write using format.</param>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Windows.Keyboard.MessageKeyboard">
            <summary>
            Class defining a virtual keyboard using the API Message.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.MessageKeyboard.Press(Binarysharp.MemoryManagement.Native.Keys)">
            <summary>
            Presses the specified virtual key to the window.
            </summary>
            <param name="key">The virtual key to press.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.MessageKeyboard.Release(Binarysharp.MemoryManagement.Native.Keys)">
            <summary>
            Releases the specified virtual key to the window.
            </summary>
            <param name="key">The virtual key to release.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.MessageKeyboard.Write(System.Char)">
            <summary>
            Writes the specified character to the window.
            </summary>
            <param name="character">The character to write.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.MessageKeyboard.MakeKeyParameter(Binarysharp.MemoryManagement.Native.Keys,System.Boolean,System.Boolean,System.UInt32,System.Boolean,System.Boolean)">
            <summary>
            Makes the lParam for a key depending on several settings.
            </summary>
            <param name="key">
            [16-23 bits] The virtual key.
            </param>
            <param name="keyUp">
            [31 bit] The transition state.
            The value is always 0 for a <see cref="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyDown"/> message.
            The value is always 1 for a <see cref="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyUp"/> message.
            </param>
            <param name="fRepeat">
            [30 bit] The previous key state.
            The value is 1 if the key is down before the message is sent, or it is zero if the key is up.
            The value is always 1 for a <see cref="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyUp"/> message.
            </param>
            <param name="cRepeat">
            [0-15 bits] The repeat count for the current message. 
            The value is the number of times the keystroke is autorepeated as a result of the user holding down the key.
            If the keystroke is held long enough, multiple messages are sent. However, the repeat count is not cumulative.
            The repeat count is always 1 for a <see cref="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyUp"/> message.
            </param>
            <param name="altDown">
            [29 bit] The context code.
            The value is always 0 for a <see cref="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyDown"/> message.
            The value is always 0 for a <see cref="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyUp"/> message.</param>
            <param name="fExtended">
            [24 bit] Indicates whether the key is an extended key, such as the right-hand ALT and CTRL keys that appear on 
            an enhanced 101- or 102-key keyboard. The value is 1 if it is an extended key; otherwise, it is 0.
            </param>
            <returns>The return value is the lParam when posting or sending a message regarding key press.</returns>
            <remarks>
            KeyDown resources: http://msdn.microsoft.com/en-us/library/windows/desktop/ms646280%28v=vs.85%29.aspx
            KeyUp resources:  http://msdn.microsoft.com/en-us/library/windows/desktop/ms646281%28v=vs.85%29.aspx
            </remarks>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Keyboard.MessageKeyboard.MakeKeyParameter(Binarysharp.MemoryManagement.Native.Keys,System.Boolean)">
            <summary>
            Makes the lParam for a key depending on several settings.
            </summary>
            <param name="key">The virtual key.</param>
            <param name="keyUp">
            The transition state.
            The value is always 0 for a <see cref="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyDown"/> message.
            The value is always 1 for a <see cref="F:Binarysharp.MemoryManagement.Native.WindowsMessages.KeyUp"/> message.
            </param>
            <returns>The return value is the lParam when posting or sending a message regarding key press.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse">
            <summary>
            Abstract class defining a virtual mouse.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.Window">
            <summary>
            The reference of the <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.#ctor(Binarysharp.MemoryManagement.Windows.RemoteWindow)">
            <summary>
            Initializes a new instance of a child of the <see cref="T:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse"/> class.
            </summary>
            <param name="window">The reference of the <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/> object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.MoveToAbsolute(System.Int32,System.Int32)">
            <summary>
            Moves the cursor at the specified coordinate.
            </summary>
            <param name="x">The x-coordinate.</param>
            <param name="y">The y-coordinate.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.PressLeft">
            <summary>
            Presses the left button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.PressMiddle">
            <summary>
            Presses the middle button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.PressRight">
            <summary>
            Presses the right button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.ReleaseLeft">
            <summary>
            Releases the left button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.ReleaseMiddle">
            <summary>
            Releases the middle button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.ReleaseRight">
            <summary>
            Releases the right button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.ScrollHorizontally(System.Int32)">
            <summary>
            Scrolls horizontally using the wheel of the mouse at the current cursor position.
            </summary>
            <param name="delta">The amount of wheel movement.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.ScrollVertically(System.Int32)">
            <summary>
            Scrolls vertically using the wheel of the mouse at the current cursor position.
            </summary>
            <param name="delta">The amount of wheel movement.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.ClickLeft">
            <summary>
            Clicks the left button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.ClickMiddle">
            <summary>
            Clicks the middle button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.ClickRight">
            <summary>
            Clicks the right button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.DoubleClickLeft">
            <summary>
            Double clicks the left button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.BaseMouse.MoveTo(System.Int32,System.Int32)">
            <summary>
            Moves the cursor at the specified coordinate from the position of the window.
            </summary>
            <param name="x">The x-coordinate.</param>
            <param name="y">The y-coordinate.</param>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse">
            <summary>
            Class defining a virtual mouse using the API SendInput.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.#ctor(Binarysharp.MemoryManagement.Windows.RemoteWindow)">
            <summary>
            Initializes a new instance of a child of the <see cref="T:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse"/> class.
            </summary>
            <param name="window">The reference of the <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/> object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.MoveToAbsolute(System.Int32,System.Int32)">
            <summary>
            Moves the cursor at the specified coordinate.
            </summary>
            <param name="x">The x-coordinate.</param>
            <param name="y">The y-coordinate.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.PressLeft">
            <summary>
            Presses the left button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.PressMiddle">
            <summary>
            Presses the middle button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.PressRight">
            <summary>
            Presses the right button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.ReleaseLeft">
            <summary>
            Releases the left button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.ReleaseMiddle">
            <summary>
            Releases the middle button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.ReleaseRight">
            <summary>
            Releases the right button of the mouse at the current cursor position.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.ScrollHorizontally(System.Int32)">
            <summary>
            Scrolls horizontally using the wheel of the mouse at the current cursor position.
            </summary>
            <param name="delta">The amount of wheel movement.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.ScrollVertically(System.Int32)">
            <summary>
            Scrolls vertically using the wheel of the mouse at the current cursor position.
            </summary>
            <param name="delta">The amount of wheel movement.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.CalculateAbsoluteCoordinateX(System.Int32)">
            <summary>
            Calculates the x-coordinate with the system metric.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.CalculateAbsoluteCoordinateY(System.Int32)">
            <summary>
            Calculates the y-coordinate with the system metric.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.Mouse.SendInputMouse.CreateInput">
            <summary>
            Create an <see cref="T:Binarysharp.MemoryManagement.Native.Input"/> structure for mouse event.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Windows.RemoteWindow">
            <summary>
            Class repesenting a window in the remote process.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Windows.RemoteWindow.MemorySharp">
            <summary>
            The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Children">
            <summary>
            Gets all the child windows of this window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.ChildrenHandles">
            <summary>
            Gets all the child window handles of this window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.ClassName">
            <summary>
            Gets the class name of the window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Handle">
            <summary>
            The handle of the window.
            </summary>
            <remarks>
            The type here is not <see cref="T:Binarysharp.MemoryManagement.Native.SafeMemoryHandle"/> because a window cannot be closed by calling <see cref="M:Binarysharp.MemoryManagement.Native.NativeMethods.CloseHandle(System.IntPtr)"/>.
            For more information, see: http://stackoverflow.com/questions/8507307/why-cant-i-close-the-window-handle-in-my-code.
            </remarks>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Height">
            <summary>
            Gets or sets the height of the element.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.IsActivated">
            <summary>
            Gets if the window is currently activated.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.IsMainWindow">
            <summary>
            Gets if this is the main window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Keyboard">
            <summary>
            Tools for managing a virtual keyboard in the window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Mouse">
            <summary>
            Tools for managing a virtual mouse in the window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Placement">
            <summary>
            Gets or sets the placement of the window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.State">
            <summary>
            Gets or sets the specified window's show state.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Title">
            <summary>
            Gets or sets the title of the window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Thread">
            <summary>
            Gets the thread of the window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Width">
            <summary>
            Gets or sets the width of the element.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.X">
            <summary>
            Gets or sets the x-coordinate of the window.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.RemoteWindow.Y">
            <summary>
            Gets or sets the y-coordinate of the window.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.#ctor(Binarysharp.MemoryManagement.MemorySharp,System.IntPtr)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
            <param name="handle">The handle of a window.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.Activate">
            <summary>
            Activates the window.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.Close">
            <summary>
            Closes the window.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.Equals(Binarysharp.MemoryManagement.Windows.RemoteWindow)">
            <summary>
            Returns a value indicating whether this instance is equal to a specified object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.Flash">
            <summary>
            Flashes the window one time. It does not change the active state of the window.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.Flash(System.UInt32,System.TimeSpan,Binarysharp.MemoryManagement.Native.FlashWindowFlags)">
            <summary>
            Flashes the window. It does not change the active state of the window.
            </summary>
            <param name="count">The number of times to flash the window.</param>
            <param name="timeout">The rate at which the window is to be flashed.</param>
            <param name="flags">The flash status.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.GetHashCode">
            <summary>
            Serves as a hash function for a particular type. 
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.PostMessage(Binarysharp.MemoryManagement.Native.WindowsMessages,System.UIntPtr,System.UIntPtr)">
            <summary>
            Places (posts) a message in the message queue associated with the thread that created the window and returns without waiting for the thread to process the message.
            </summary>
            <param name="message">The message to be posted.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.PostMessage(System.UInt32,System.UIntPtr,System.UIntPtr)">
            <summary>
            Places (posts) a message in the message queue associated with the thread that created the window and returns without waiting for the thread to process the message.
            </summary>
            <param name="message">The message to be posted.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.SendMessage(Binarysharp.MemoryManagement.Native.WindowsMessages,System.UIntPtr,System.IntPtr)">
            <summary>
            Sends the specified message to a window or windows.
            The SendMessage function calls the window procedure for the specified window and does not return until the window procedure has processed the message.
            </summary>
            <param name="message">The message to be sent.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
            <returns>The return value specifies the result of the message processing; it depends on the message sent.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.SendMessage(System.UInt32,System.UIntPtr,System.IntPtr)">
            <summary>
            Sends the specified message to a window or windows.
            The SendMessage function calls the window procedure for the specified window and does not return until the window procedure has processed the message.
            </summary>
            <param name="message">The message to be sent.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
            <returns>The return value specifies the result of the message processing; it depends on the message sent.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.RemoteWindow.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Windows.WindowCore">
            <summary>
            Static core class providing tools for managing windows.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.GetClassName(System.IntPtr)">
            <summary>
            Retrieves the name of the class to which the specified window belongs.
            </summary>
            <param name="windowHandle">A handle to the window and, indirectly, the class to which the window belongs.</param>
            <returns>The return values is the class name string.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.GetForegroundWindow">
            <summary>
            Retrieves a handle to the foreground window (the window with which the user is currently working).
            </summary>
            <returns>A handle to the foreground window. The foreground window can be <c>IntPtr.Zero</c> in certain circumstances, such as when a window is losing activation.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.GetSystemMetrics(Binarysharp.MemoryManagement.Native.SystemMetrics)">
            <summary>
            Retrieves the specified system metric or system configuration setting.
            </summary>
            <param name="metric">The system metric or configuration setting to be retrieved.</param>
            <returns>The return value is the requested system metric or configuration setting.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.GetWindowText(System.IntPtr)">
            <summary>
            Gets the text of the specified window's title bar.
            </summary>
            <param name="windowHandle">A handle to the window containing the text.</param>
            <returns>The return value is the window's title bar.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.GetWindowPlacement(System.IntPtr)">
            <summary>
            Retrieves the show state and the restored, minimized, and maximized positions of the specified window.
            </summary>
            <param name="windowHandle">A handle to the window.</param>
            <returns>The return value is a <see cref="T:Binarysharp.MemoryManagement.Native.WindowPlacement"/> structure that receives the show state and position information.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.GetWindowProcessId(System.IntPtr)">
            <summary>
            Retrieves the identifier of the process that created the window. 
            </summary>
            <param name="windowHandle">A handle to the window.</param>
            <returns>The return value is the identifier of the process that created the window.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.GetWindowThreadId(System.IntPtr)">
            <summary>
            Retrieves the identifier of the thread that created the specified window.
            </summary>
            <param name="windowHandle">A handle to the window.</param>
            <returns>The return value is the identifier of the thread that created the window.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.EnumAllWindows">
            <summary>
            Enumerates all the windows on the screen.
            </summary>
            <returns>A collection of handles of all the windows.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.EnumChildWindows(System.IntPtr)">
            <summary>
            Enumerates recursively all the child windows that belong to the specified parent window.
            </summary>
            <param name="parentHandle">The parent window handle.</param>
            <returns>A collection of handles of the child windows.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.EnumTopLevelWindows">
            <summary>
            Enumerates all top-level windows on the screen. This function does not search child windows. 
            </summary>
            <returns>A collection of handles of top-level windows.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.FlashWindow(System.IntPtr)">
            <summary>
            Flashes the specified window one time. It does not change the active state of the window.
            To flash the window a specified number of times, use the <see cref="M:Binarysharp.MemoryManagement.Windows.WindowCore.FlashWindowEx(System.IntPtr,Binarysharp.MemoryManagement.Native.FlashWindowFlags,System.UInt32,System.TimeSpan)"/> function.
            </summary>
            <param name="windowHandle">A handle to the window to be flashed. The window can be either open or minimized.</param>
            <returns>
            The return value specifies the window's state before the call to the <see cref="M:Binarysharp.MemoryManagement.Windows.WindowCore.FlashWindow(System.IntPtr)"/> function. 
            If the window caption was drawn as active before the call, the return value is nonzero. Otherwise, the return value is zero.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.FlashWindowEx(System.IntPtr,Binarysharp.MemoryManagement.Native.FlashWindowFlags,System.UInt32,System.TimeSpan)">
            <summary>
            Flashes the specified window. It does not change the active state of the window.
            </summary>
            <param name="windowHandle">A handle to the window to be flashed. The window can be either opened or minimized.</param>
            <param name="flags">The flash status.</param>
            <param name="count">The number of times to flash the window.</param>
            <param name="timeout">The rate at which the window is to be flashed.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.FlashWindowEx(System.IntPtr,Binarysharp.MemoryManagement.Native.FlashWindowFlags,System.UInt32)">
            <summary>
            Flashes the specified window. It does not change the active state of the window. The function uses the default cursor blink rate.
            </summary>
            <param name="windowHandle">A handle to the window to be flashed. The window can be either opened or minimized.</param>
            <param name="flags">The flash status.</param>
            <param name="count">The number of times to flash the window.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.FlashWindowEx(System.IntPtr,Binarysharp.MemoryManagement.Native.FlashWindowFlags)">
            <summary>
            Flashes the specified window. It does not change the active state of the window. The function uses the default cursor blink rate.
            </summary>
            <param name="windowHandle">A handle to the window to be flashed. The window can be either opened or minimized.</param>
            <param name="flags">The flash status.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.MapVirtualKey(System.UInt32,Binarysharp.MemoryManagement.Native.TranslationTypes)">
            <summary>
            Translates (maps) a virtual-key code into a scan code or character value, or translates a scan code into a virtual-key code.
            To specify a handle to the keyboard layout to use for translating the specified code, use the MapVirtualKeyEx function.
            </summary>
            <param name="key">
            The virtual key code or scan code for a key. How this value is interpreted depends on the value of the uMapType parameter.
            </param>
            <param name="translation">
            The translation to be performed. The value of this parameter depends on the value of the uCode parameter.
            </param>
            <returns>
            The return value is either a scan code, a virtual-key code, or a character value, depending on the value of uCode and uMapType. 
            If there is no translation, the return value is zero.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.MapVirtualKey(Binarysharp.MemoryManagement.Native.Keys,Binarysharp.MemoryManagement.Native.TranslationTypes)">
            <summary>
            Translates (maps) a virtual-key code into a scan code or character value, or translates a scan code into a virtual-key code.
            To specify a handle to the keyboard layout to use for translating the specified code, use the MapVirtualKeyEx function.
            </summary>
            <param name="key">
            The virtual key code for a key. How this value is interpreted depends on the value of the uMapType parameter.
            </param>
            <param name="translation">
            The translation to be performed. The value of this parameter depends on the value of the uCode parameter.
            </param>
            <returns>
            The return value is either a scan code, a virtual-key code, or a character value, depending on the value of uCode and uMapType. 
            If there is no translation, the return value is zero.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.PostMessage(System.IntPtr,System.UInt32,System.UIntPtr,System.UIntPtr)">
            <summary>
            Places (posts) a message in the message queue associated with the thread that created the specified window and returns without waiting for the thread to process the message.
            </summary>
            <param name="windowHandle">A handle to the window whose window procedure is to receive the message. The following values have special meanings.</param>
            <param name="message">The message to be posted.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.PostMessage(System.IntPtr,Binarysharp.MemoryManagement.Native.WindowsMessages,System.UIntPtr,System.UIntPtr)">
            <summary>
            Places (posts) a message in the message queue associated with the thread that created the specified window and returns without waiting for the thread to process the message.
            </summary>
            <param name="windowHandle">A handle to the window whose window procedure is to receive the message. The following values have special meanings.</param>
            <param name="message">The message to be posted.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.SendInput(Binarysharp.MemoryManagement.Native.Input[])">
            <summary>
            Synthesizes keystrokes, mouse motions, and button clicks.
            </summary>
            <param name="inputs">An array of <see cref="T:Binarysharp.MemoryManagement.Native.Input"/> structures. Each structure represents an event to be inserted into the keyboard or mouse input stream.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.SendInput(Binarysharp.MemoryManagement.Native.Input)">
            <summary>
            Synthesizes keystrokes, mouse motions, and button clicks.
            </summary>
            <param name="input">A structure represents an event to be inserted into the keyboard or mouse input stream.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.SendMessage(System.IntPtr,System.UInt32,System.UIntPtr,System.IntPtr)">
            <summary>
            Sends the specified message to a window or windows.
            The SendMessage function calls the window procedure for the specified window and does not return until the window procedure has processed the message.
            </summary>
            <param name="windowHandle">A handle to the window whose window procedure will receive the message.</param>
            <param name="message">The message to be sent.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
            <returns>The return value specifies the result of the message processing; it depends on the message sent.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.SendMessage(System.IntPtr,Binarysharp.MemoryManagement.Native.WindowsMessages,System.UIntPtr,System.IntPtr)">
            <summary>
            Sends the specified message to a window or windows.
            The SendMessage function calls the window procedure for the specified window and does not return until the window procedure has processed the message.
            </summary>
            <param name="windowHandle">A handle to the window whose window procedure will receive the message.</param>
            <param name="message">The message to be sent.</param>
            <param name="wParam">Additional message-specific information.</param>
            <param name="lParam">Additional message-specific information.</param>
            <returns>The return value specifies the result of the message processing; it depends on the message sent.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.SetForegroundWindow(System.IntPtr)">
            <summary>
            Brings the thread that created the specified window into the foreground and activates the window. 
            The window is restored if minimized. Performs no action if the window is already activated.
            </summary>
            <param name="windowHandle">A handle to the window that should be activated and brought to the foreground.</param>
            <returns>
            If the window was brought to the foreground, the return value is <c>true</c>, otherwise the return value is <c>false</c>.
            </returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.SetWindowPlacement(System.IntPtr,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Sets the current position and size of the specified window.
            </summary>
            <param name="windowHandle">A handle to the window.</param>
            <param name="left">The x-coordinate of the upper-left corner of the window.</param>
            <param name="top">The y-coordinate of the upper-left corner of the window.</param>
            <param name="height">The height of the window.</param>
            <param name="width">The width of the window.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.SetWindowPlacement(System.IntPtr,Binarysharp.MemoryManagement.Native.WindowPlacement)">
            <summary>
            Sets the show state and the restored, minimized, and maximized positions of the specified window.
            </summary>
            <param name="windowHandle">A handle to the window.</param>
            <param name="placement">A pointer to the <see cref="T:Binarysharp.MemoryManagement.Native.WindowPlacement"/> structure that specifies the new show state and window positions.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.SetWindowText(System.IntPtr,System.String)">
            <summary>
            Sets the text of the specified window's title bar.
            </summary>
            <param name="windowHandle">A handle to the window whose text is to be changed.</param>
            <param name="title">The new title text.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowCore.ShowWindow(System.IntPtr,Binarysharp.MemoryManagement.Native.WindowStates)">
            <summary>
            Sets the specified window's show state.
            </summary>
            <param name="windowHandle">A handle to the window.</param>
            <param name="state">Controls how the window is to be shown.</param>
            <returns>If the window was previously visible, the return value is <c>true</c>, otherwise the return value is <c>false</c>.</returns>
        </member>
        <member name="T:Binarysharp.MemoryManagement.Windows.WindowFactory">
            <summary>
            Class providing tools for manipulating windows.
            </summary>
        </member>
        <member name="F:Binarysharp.MemoryManagement.Windows.WindowFactory._memorySharp">
            <summary>
            The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.WindowFactory.ChildWindows">
            <summary>
            Gets all the child windows that belong to the application.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.WindowFactory.ChildWindowHandles">
            <summary>
            Gets all the child window handles that belong to the application.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.WindowFactory.MainWindow">
            <summary>
            Gets the main window of the application.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.WindowFactory.MainWindowHandle">
            <summary>
            Gets the main window handle of the application.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.WindowFactory.Item(System.String)">
            <summary>
            Gets all the windows that have the same specified title.
            </summary>
            <param name="windowTitle">The window title string.</param>
            <returns>A collection of <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/>.</returns>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.WindowFactory.RemoteWindows">
            <summary>
            Gets all the windows that belong to the application.
            </summary>
        </member>
        <member name="P:Binarysharp.MemoryManagement.Windows.WindowFactory.WindowHandles">
            <summary>
            Gets all the window handles that belong to the application.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowFactory.#ctor(Binarysharp.MemoryManagement.MemorySharp)">
            <summary>
            Initializes a new instance of the <see cref="T:Binarysharp.MemoryManagement.Windows.WindowFactory"/> class.
            </summary>
            <param name="memorySharp">The reference of the <see cref="T:Binarysharp.MemoryManagement.MemorySharp"/> object.</param>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowFactory.Dispose">
            <summary>
            Releases all resources used by the <see cref="T:Binarysharp.MemoryManagement.Windows.WindowFactory"/> object.
            </summary>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowFactory.GetWindowsByClassName(System.String)">
            <summary>
            Gets all the windows that have the specified class name.
            </summary>
            <param name="className">The class name string.</param>
            <returns>A collection of <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/>.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowFactory.GetWindowsByTitle(System.String)">
            <summary>
            Gets all the windows that have the same specified title.
            </summary>
            <param name="windowTitle">The window title string.</param>
            <returns>A collection of <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/>.</returns>
        </member>
        <member name="M:Binarysharp.MemoryManagement.Windows.WindowFactory.GetWindowsByTitleContains(System.String)">
            <summary>
            Gets all the windows that contain the specified title.
            </summary>
            <param name="windowTitle">A part a window title string.</param>
            <returns>A collection of <see cref="T:Binarysharp.MemoryManagement.Windows.RemoteWindow"/>.</returns>
        </member>
    </members>
</doc>
