using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace AIMemoryReader
{
    public class Memory
    {
        // P/Invoke declarations
        const int PROCESS_VM_READ = 0x0010;
        const int PROCESS_VM_WRITE = 0x0020;
        const int PROCESS_VM_OPERATION = 0x0008;

        [DllImport("kernel32.dll")]
        public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesWritten);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool CloseHandle(IntPtr hObject);

        private IntPtr processHandle;
        private Process process;

        public Process Process
        {
            get { return process; }
        }

        public Memory(string processName)
        {
            Process[] processes = Process.GetProcessesByName(processName.Replace(".exe", ""));
            if (processes.Length > 0)
            {
                process = processes[0];
                processHandle = OpenProcess(PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION, false, process.Id);
                Assembly = new AssemblyExecutor(this);
            }
            else
            {
                throw new ArgumentException("Process not found");
            }
        }

        ~Memory()
        {
            if (processHandle != IntPtr.Zero)
            {
                CloseHandle(processHandle);
            }
        }

        public T Read<T>(IntPtr address) where T : struct
        {
            int size = Marshal.SizeOf(typeof(T));
            byte[] buffer = new byte[size];
            int bytesRead;
            ReadProcessMemory(processHandle, address, buffer, size, out bytesRead);

            GCHandle handle = GCHandle.Alloc(buffer, GCHandleType.Pinned);
            T data = (T)Marshal.PtrToStructure(handle.AddrOfPinnedObject(), typeof(T));
            handle.Free();

            return data;
        }

        public string ReadString(IntPtr address, int size, Encoding encoding)
        {
            byte[] buffer = new byte[size];
            int bytesRead;
            ReadProcessMemory(processHandle, address, buffer, size, out bytesRead);
            string text = encoding.GetString(buffer);
            if (text.Contains("\0"))
            {
                text = text.Substring(0, text.IndexOf('\0'));
            }
            return text;
        }

        public byte[] ReadBytes(IntPtr address, int count)
        {
            byte[] buffer = new byte[count];
            int bytesRead;
            ReadProcessMemory(processHandle, address, buffer, count, out bytesRead);
            return buffer;
        }

        public void Write<T>(IntPtr address, T value) where T : struct
        {
            int size = Marshal.SizeOf(typeof(T));
            byte[] buffer = new byte[size];

            IntPtr ptr = Marshal.AllocHGlobal(size);
            Marshal.StructureToPtr(value, ptr, true);
            Marshal.Copy(ptr, buffer, 0, size);
            Marshal.FreeHGlobal(ptr);

            int bytesWritten;
            WriteProcessMemory(processHandle, address, buffer, size, out bytesWritten);
        }

        public void WriteString(IntPtr address, string text, Encoding encoding)
        {
            byte[] buffer = encoding.GetBytes(text + "\0");
            int bytesWritten;
            WriteProcessMemory(processHandle, address, buffer, buffer.Length, out bytesWritten);
        }

        public void WriteBytes(IntPtr address, byte[] bytes)
        {
            int bytesWritten;
            WriteProcessMemory(processHandle, address, bytes, bytes.Length, out bytesWritten);
        }

        public AssemblyExecutor Assembly { get; private set; }
    }

    public class AssemblyExecutor
    {
        private readonly Memory memory;

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool VirtualFreeEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint dwFreeType);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateRemoteThread(IntPtr hProcess, IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, out IntPtr lpThreadId);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern uint WaitForSingleObject(IntPtr hHandle, uint dwMilliseconds);

        [DllImport("kernel32.dll")]
        static extern bool GetThreadContext(IntPtr hThread, ref CONTEXT lpContext);

        public AssemblyExecutor(Memory memory)
        {
            this.memory = memory;
        }

        public T Execute<T>(IntPtr address, params object[] parameters) where T : struct
        {
            return Execute<T>(address, CallingConvention.Cdecl, parameters);
        }

        public T Execute<T>(IntPtr address, CallingConvention callingConvention, params object[] parameters) where T : struct
        {
            List<IntPtr> allocatedMemory = new List<IntPtr>();
            IntPtr asmAddress = IntPtr.Zero;

            try
            {
                var processedParameters = new List<object>();
                foreach (var parameter in parameters.Reverse())
                {
                    if (parameter is string s)
                    {
                        byte[] stringBytes = Encoding.UTF8.GetBytes(s + "\0");
                        IntPtr stringAddress = VirtualAllocEx(memory.Process.Handle, IntPtr.Zero, (uint)stringBytes.Length, 0x1000, 0x40);
                        allocatedMemory.Add(stringAddress);
                        memory.WriteBytes(stringAddress, stringBytes);
                        processedParameters.Add(stringAddress);
                    }
                    else
                    {
                        processedParameters.Add(parameter);
                    }
                }

                byte[] asm = GenerateAsm(address, processedParameters, callingConvention);

                asmAddress = VirtualAllocEx(memory.Process.Handle, IntPtr.Zero, (uint)asm.Length, 0x1000, 0x40);
                memory.WriteBytes(asmAddress, asm);

                IntPtr threadHandle = CreateRemoteThread(memory.Process.Handle, IntPtr.Zero, 0, asmAddress, IntPtr.Zero, 0, out IntPtr threadId);
                WaitForSingleObject(threadHandle, 0xFFFFFFFF);

                CONTEXT context = new CONTEXT();
                context.ContextFlags = CONTEXT_FLAGS.CONTEXT_INTEGER;
                GetThreadContext(threadHandle, ref context);

                return memory.Read<T>((IntPtr)context.Eax);
            }
            finally
            {
                if (asmAddress != IntPtr.Zero)
                {
                    VirtualFreeEx(memory.Process.Handle, asmAddress, 0, 0x8000);
                }
                foreach (var ptr in allocatedMemory)
                {
                    VirtualFreeEx(memory.Process.Handle, ptr, 0, 0x8000);
                }
            }
        }

        private byte[] GenerateAsm(IntPtr functionAddress, List<object> parameters, CallingConvention callingConvention)
        {
            var asm = new List<byte>();

            foreach (var parameter in parameters)
            {
                asm.Add(0x68); // push
                if (parameter is IntPtr ptr)
                {
                    asm.AddRange(BitConverter.GetBytes(ptr.ToInt32()));
                }
                else if (parameter is int i)
                {
                    asm.AddRange(BitConverter.GetBytes(i));
                }
                else
                {
                    throw new ArgumentException("Unsupported parameter type: " + parameter.GetType());
                }
            }

            asm.Add(0xB8); // mov eax, address
            asm.AddRange(BitConverter.GetBytes(functionAddress.ToInt32()));

            asm.Add(0xFF); // call eax
            asm.Add(0xD0);

            if (callingConvention == CallingConvention.Cdecl)
            {
                asm.Add(0x83); // add esp, n
                asm.Add(0xC4);
                asm.Add((byte)(parameters.Count * 4));
            }

            asm.Add(0xC3); // ret

            return asm.ToArray();
        }

        [Flags]
        public enum CONTEXT_FLAGS : uint
        {
            CONTEXT_i386 = 0x10000,
            CONTEXT_i486 = 0x10000,   //  same as i386
            CONTEXT_CONTROL = (CONTEXT_i386 | 0x00000001), // SS:SP, CS:IP, EFLAGS, EBP
            CONTEXT_INTEGER = (CONTEXT_i386 | 0x00000002), // EAX, ECX, EDX, EBX, ESI, EDI
            CONTEXT_SEGMENTS = (CONTEXT_i386 | 0x00000004), // DS, ES, FS, GS
            CONTEXT_FLOATING_POINT = (CONTEXT_i386 | 0x00000008), // 387 state
            CONTEXT_DEBUG_REGISTERS = (CONTEXT_i386 | 0x00000010), // DB 0-3, 6, 7
            CONTEXT_EXTENDED_REGISTERS = (CONTEXT_i386 | 0x00000020), // cpu specific extensions
            CONTEXT_FULL = (CONTEXT_CONTROL | CONTEXT_INTEGER | CONTEXT_SEGMENTS), // all of the above
            CONTEXT_ALL = (CONTEXT_CONTROL | CONTEXT_INTEGER | CONTEXT_SEGMENTS | CONTEXT_FLOATING_POINT | CONTEXT_DEBUG_REGISTERS | CONTEXT_EXTENDED_REGISTERS)
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct CONTEXT
        {
            public CONTEXT_FLAGS ContextFlags; //set this to CONTEXT_CONTROL
            public uint Dr0;
            public uint Dr1;
            public uint Dr2;
            public uint Dr3;
            public uint Dr6;
            public uint Dr7;
            public FLOATING_SAVE_AREA FloatSave;
            public uint SegGs;
            public uint SegFs;
            public uint SegEs;
            public uint SegDs;
            public uint Edi;
            public uint Esi;
            public uint Ebx;
            public uint Edx;
            public uint Ecx;
            public uint Eax;
            public uint Ebp;
            public uint Eip;
            public uint SegCs;              // MUST BE SANITIZED
            public uint EFlags;             // MUST BE SANITIZED
            public uint Esp;
            public uint SegSs;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 512)]
            public byte[] ExtendedRegisters;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct FLOATING_SAVE_AREA
        {
            public uint ControlWord;
            public uint StatusWord;
            public uint TagWord;
            public uint ErrorOffset;
            public uint ErrorSelector;
            public uint DataOffset;
            public uint DataSelector;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 80)]
            public byte[] RegisterArea;
            public uint Cr0NpxState;
        }
    }
}
