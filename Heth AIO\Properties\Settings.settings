﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Heth_AIO.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="winName" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">KalAIO Online</Value>
    </Setting>
    <Setting Name="monsterName" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">High Class</Value>
    </Setting>
    <Setting Name="macroBox" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Your macro</Value>
    </Setting>
    <Setting Name="GMNames" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Admin</Value>
    </Setting>
    <Setting Name="txtMacroName" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Default</Value>
    </Setting>
    <Setting Name="mPreset1" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="mPreset2" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="mPreset3" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="mPreset4" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="mPreset5" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="engineBox" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="manaBox" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="healthBox" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="nAntiAfkCD" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">5000</Value>
    </Setting>
    <Setting Name="nAntiAfkX" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">150</Value>
    </Setting>
    <Setting Name="nAntiAfkY" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">150</Value>
    </Setting>
    <Setting Name="presetBox" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="txtPreset1" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Preset 1</Value>
    </Setting>
    <Setting Name="txtPreset2" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Preset 2</Value>
    </Setting>
    <Setting Name="txtPreset3" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Preset 3</Value>
    </Setting>
    <Setting Name="txtPreset4" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Preset 4</Value>
    </Setting>
    <Setting Name="txtPreset5" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Preset 5</Value>
    </Setting>
    <Setting Name="licenseKey" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">xxxx-xxxx-xxxx-xxxx-xxxx</Value>
    </Setting>
    <Setting Name="GMNamesBox" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Admin,&lt;A&gt;Admin</Value>
    </Setting>
    <Setting Name="extraMacro" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)">Your Macro...</Value>
    </Setting>
    <Setting Name="ctrlMacro" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtMapX1" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtMapX2" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtMapX3" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtMapX4" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtMapY1" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtMapY2" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtMapY3" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtMapY4" Provider="Bluegrams.Application.PortableSettingsProvider" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
  </Settings>
</SettingsFile>