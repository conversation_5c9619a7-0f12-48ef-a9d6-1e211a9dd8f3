﻿
namespace Heth_AIO
{
    partial class MainApp
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainApp));
            this.panelMenu = new System.Windows.Forms.Panel();
            this.lblWorkingCheat = new System.Windows.Forms.Label();
            this.panelLeft = new System.Windows.Forms.Panel();
            this.btnCheats = new System.Windows.Forms.Button();
            this.lblWorkingChecker = new System.Windows.Forms.Label();
            this.btnCheckers = new System.Windows.Forms.Button();
            this.lblWorkingAuto = new System.Windows.Forms.Label();
            this.btnAuto = new System.Windows.Forms.Button();
            this.lblWorkingMacro = new System.Windows.Forms.Label();
            this.btnMacro = new System.Windows.Forms.Button();
            this.panelStart = new System.Windows.Forms.Panel();
            this.NewReaderCheckBox = new System.Windows.Forms.CheckBox();
            this.safeModeCheckBox = new System.Windows.Forms.CheckBox();
            this.lblMap = new System.Windows.Forms.Label();
            this.lblMana = new System.Windows.Forms.Label();
            this.lblHealth = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.comboEngineType = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.txtWinName = new System.Windows.Forms.TextBox();
            this.btnReset = new System.Windows.Forms.Button();
            this.lblName = new System.Windows.Forms.Label();
            this.lblExpire = new System.Windows.Forms.Label();
            this.btnInject = new System.Windows.Forms.Button();
            this.panelControl = new System.Windows.Forms.Panel();
            this.hotkeys = new System.Windows.Forms.CheckBox();
            this.lblRunInfo = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.minBTN = new System.Windows.Forms.Button();
            this.extBTN = new System.Windows.Forms.Button();
            this.panelMacro = new System.Windows.Forms.Panel();
            this.panelMacroBox = new System.Windows.Forms.Panel();
            this.label15 = new System.Windows.Forms.Label();
            this.richMacroBox = new System.Windows.Forms.RichTextBox();
            this.panelMacroCtrl = new System.Windows.Forms.Panel();
            this.label33 = new System.Windows.Forms.Label();
            this.richMacroCtrlBox = new System.Windows.Forms.RichTextBox();
            this.panelMacroMisc = new System.Windows.Forms.Panel();
            this.btnSave = new System.Windows.Forms.Button();
            this.btnImport = new System.Windows.Forms.Button();
            this.lblMouseTrackerPosition = new System.Windows.Forms.Label();
            this.lblInfoMT = new System.Windows.Forms.Label();
            this.btnMouseTracker = new System.Windows.Forms.Button();
            this.label6 = new System.Windows.Forms.Label();
            this.btnNewMacro = new System.Windows.Forms.Button();
            this.panelMacroPresets = new System.Windows.Forms.Panel();
            this.btnPresetSave = new System.Windows.Forms.Button();
            this.comboPresets = new System.Windows.Forms.ComboBox();
            this.txtBoxPreset = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.panelMacroController = new System.Windows.Forms.Panel();
            this.btnMacroHelpCMD = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnStartBot = new System.Windows.Forms.Button();
            this.btnStopBot = new System.Windows.Forms.Button();
            this.panelInject = new System.Windows.Forms.Panel();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.panelAuto = new System.Windows.Forms.Panel();
            this.panelCheckerOther = new System.Windows.Forms.Panel();
            this.label10 = new System.Windows.Forms.Label();
            this.quickSlotPageNum = new System.Windows.Forms.ComboBox();
            this.checkQuickSlot = new System.Windows.Forms.CheckBox();
            this.label39 = new System.Windows.Forms.Label();
            this.panelAutoAntiAFK = new System.Windows.Forms.Panel();
            this.label37 = new System.Windows.Forms.Label();
            this.label36 = new System.Windows.Forms.Label();
            this.label35 = new System.Windows.Forms.Label();
            this.label34 = new System.Windows.Forms.Label();
            this.txtNormalAntiAfk = new System.Windows.Forms.NumericUpDown();
            this.mouseXNum = new System.Windows.Forms.NumericUpDown();
            this.mouseYNum = new System.Windows.Forms.NumericUpDown();
            this.checkAntiAFK = new System.Windows.Forms.CheckBox();
            this.label27 = new System.Windows.Forms.Label();
            this.panelAutoHPMP = new System.Windows.Forms.Panel();
            this.lblAutoHP = new System.Windows.Forms.Label();
            this.trackBarHP = new System.Windows.Forms.TrackBar();
            this.manaBox = new System.Windows.Forms.ComboBox();
            this.healthBox = new System.Windows.Forms.ComboBox();
            this.checkAutoMana = new System.Windows.Forms.CheckBox();
            this.checkAutoHealth = new System.Windows.Forms.CheckBox();
            this.label26 = new System.Windows.Forms.Label();
            this.trackBarMP = new System.Windows.Forms.TrackBar();
            this.lblAutoMP = new System.Windows.Forms.Label();
            this.panelCheckers = new System.Windows.Forms.Panel();
            this.button1 = new System.Windows.Forms.Button();
            this.panelCheckersFindPlayer = new System.Windows.Forms.Panel();
            this.checkAnyPlayerFinder = new System.Windows.Forms.CheckBox();
            this.label49 = new System.Windows.Forms.Label();
            this.checkPlayerFinder = new System.Windows.Forms.CheckBox();
            this.txtPlayerNames = new System.Windows.Forms.TextBox();
            this.lblPlayerFound = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.panelCheckerDeathStuff = new System.Windows.Forms.Panel();
            this.checkXY = new System.Windows.Forms.CheckBox();
            this.label44 = new System.Windows.Forms.Label();
            this.txtBoxMapY4 = new System.Windows.Forms.TextBox();
            this.label45 = new System.Windows.Forms.Label();
            this.txtBoxMapX4 = new System.Windows.Forms.TextBox();
            this.label46 = new System.Windows.Forms.Label();
            this.txtBoxMapY3 = new System.Windows.Forms.TextBox();
            this.label47 = new System.Windows.Forms.Label();
            this.txtBoxMapX3 = new System.Windows.Forms.TextBox();
            this.label42 = new System.Windows.Forms.Label();
            this.txtBoxMapY2 = new System.Windows.Forms.TextBox();
            this.label43 = new System.Windows.Forms.Label();
            this.txtBoxMapX2 = new System.Windows.Forms.TextBox();
            this.label41 = new System.Windows.Forms.Label();
            this.txtBoxMapY1 = new System.Windows.Forms.TextBox();
            this.label40 = new System.Windows.Forms.Label();
            this.txtBoxMapX1 = new System.Windows.Forms.TextBox();
            this.label38 = new System.Windows.Forms.Label();
            this.checkAlertLowHP = new System.Windows.Forms.CheckBox();
            this.checkCloseDead = new System.Windows.Forms.CheckBox();
            this.checkSSDead = new System.Windows.Forms.CheckBox();
            this.checkStopDead = new System.Windows.Forms.CheckBox();
            this.panelCheckersDT = new System.Windows.Forms.Panel();
            this.checkTarget = new System.Windows.Forms.CheckBox();
            this.txtTarget = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.checkDeath = new System.Windows.Forms.CheckBox();
            this.panelCheckerTimers = new System.Windows.Forms.Panel();
            this.btnCheckerTimer = new System.Windows.Forms.Button();
            this.lblTimer = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.aliveTimer = new System.Windows.Forms.Timer(this.components);
            this.panelCheats = new System.Windows.Forms.Panel();
            this.panelBlockHide = new System.Windows.Forms.Panel();
            this.checkBlockHide = new System.Windows.Forms.CheckBox();
            this.checkHideAll = new System.Windows.Forms.CheckBox();
            this.checkBlockTeleport = new System.Windows.Forms.CheckBox();
            this.label50 = new System.Windows.Forms.Label();
            this.panelCheatsKnight = new System.Windows.Forms.Panel();
            this.checkPWW2nd = new System.Windows.Forms.CheckBox();
            this.checkPWW3rd = new System.Windows.Forms.CheckBox();
            this.label17 = new System.Windows.Forms.Label();
            this.panelCheatsStats = new System.Windows.Forms.Panel();
            this.lblCurPoints = new System.Windows.Forms.Label();
            this.panelAddStats = new System.Windows.Forms.Panel();
            this.label31 = new System.Windows.Forms.Label();
            this.agiNBox = new System.Windows.Forms.NumericUpDown();
            this.label30 = new System.Windows.Forms.Label();
            this.wisNBox = new System.Windows.Forms.NumericUpDown();
            this.label28 = new System.Windows.Forms.Label();
            this.intNBox = new System.Windows.Forms.NumericUpDown();
            this.label21 = new System.Windows.Forms.Label();
            this.hthNBox = new System.Windows.Forms.NumericUpDown();
            this.label18 = new System.Windows.Forms.Label();
            this.strNBox = new System.Windows.Forms.NumericUpDown();
            this.label32 = new System.Windows.Forms.Label();
            this.btnStatsAdd = new System.Windows.Forms.Button();
            this.panelCurStats = new System.Windows.Forms.Panel();
            this.lblCurAgi = new System.Windows.Forms.Label();
            this.lblCurWis = new System.Windows.Forms.Label();
            this.lblCurInt = new System.Windows.Forms.Label();
            this.lblCurHP = new System.Windows.Forms.Label();
            this.lblCurStr = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.panelCheatsHTML = new System.Windows.Forms.Panel();
            this.txtOpenHTML = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.btnOpenHTML = new System.Windows.Forms.Button();
            this.panelCheatTarget = new System.Windows.Forms.Panel();
            this.checkTabTarget = new System.Windows.Forms.CheckBox();
            this.label48 = new System.Windows.Forms.Label();
            this.checkLockTarget = new System.Windows.Forms.CheckBox();
            this.label25 = new System.Windows.Forms.Label();
            this.checkTargetInfoToSys = new System.Windows.Forms.CheckBox();
            this.checkTargetOverlay = new System.Windows.Forms.CheckBox();
            this.label24 = new System.Windows.Forms.Label();
            this.comboTargetRange = new System.Windows.Forms.ComboBox();
            this.label23 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.panelCheatsMisc = new System.Windows.Forms.Panel();
            this.checkTest = new System.Windows.Forms.CheckBox();
            this.btnSaveQuickslot = new System.Windows.Forms.Button();
            this.checkInifityRush = new System.Windows.Forms.CheckBox();
            this.checkAttackSpeed = new System.Windows.Forms.CheckBox();
            this.label19 = new System.Windows.Forms.Label();
            this.panelCheatZoom = new System.Windows.Forms.Panel();
            this.lblCurZoom = new System.Windows.Forms.Label();
            this.numZoom = new System.Windows.Forms.NumericUpDown();
            this.label16 = new System.Windows.Forms.Label();
            this.panelTitle = new System.Windows.Forms.Panel();
            this.lblUpdateLink = new System.Windows.Forms.Label();
            this.txtConsole = new System.Windows.Forms.TextBox();
            this.lblTitle = new System.Windows.Forms.Label();
            this.DataTimer = new System.Windows.Forms.Timer(this.components);
            this.workerKeyboardMonitor = new System.ComponentModel.BackgroundWorker();
            this.PauseCheckTimer = new System.Windows.Forms.Timer(this.components);
            this.panelMenu.SuspendLayout();
            this.panelStart.SuspendLayout();
            this.panelControl.SuspendLayout();
            this.panelMacro.SuspendLayout();
            this.panelMacroBox.SuspendLayout();
            this.panelMacroCtrl.SuspendLayout();
            this.panelMacroMisc.SuspendLayout();
            this.panelMacroPresets.SuspendLayout();
            this.panelMacroController.SuspendLayout();
            this.panelInject.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.panelAuto.SuspendLayout();
            this.panelCheckerOther.SuspendLayout();
            this.panelAutoAntiAFK.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtNormalAntiAfk)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.mouseXNum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.mouseYNum)).BeginInit();
            this.panelAutoHPMP.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarHP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarMP)).BeginInit();
            this.panelCheckers.SuspendLayout();
            this.panelCheckersFindPlayer.SuspendLayout();
            this.panelCheckerDeathStuff.SuspendLayout();
            this.panelCheckersDT.SuspendLayout();
            this.panelCheckerTimers.SuspendLayout();
            this.panelCheats.SuspendLayout();
            this.panelBlockHide.SuspendLayout();
            this.panelCheatsKnight.SuspendLayout();
            this.panelCheatsStats.SuspendLayout();
            this.panelAddStats.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.agiNBox)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.wisNBox)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.intNBox)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.hthNBox)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.strNBox)).BeginInit();
            this.panelCurStats.SuspendLayout();
            this.panelCheatsHTML.SuspendLayout();
            this.panelCheatTarget.SuspendLayout();
            this.panelCheatsMisc.SuspendLayout();
            this.panelCheatZoom.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numZoom)).BeginInit();
            this.panelTitle.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelMenu
            // 
            this.panelMenu.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.panelMenu.Controls.Add(this.lblWorkingCheat);
            this.panelMenu.Controls.Add(this.panelLeft);
            this.panelMenu.Controls.Add(this.btnCheats);
            this.panelMenu.Controls.Add(this.lblWorkingChecker);
            this.panelMenu.Controls.Add(this.btnCheckers);
            this.panelMenu.Controls.Add(this.lblWorkingAuto);
            this.panelMenu.Controls.Add(this.btnAuto);
            this.panelMenu.Controls.Add(this.lblWorkingMacro);
            this.panelMenu.Controls.Add(this.btnMacro);
            this.panelMenu.Controls.Add(this.panelStart);
            this.panelMenu.Dock = System.Windows.Forms.DockStyle.Left;
            this.panelMenu.Location = new System.Drawing.Point(0, 0);
            this.panelMenu.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelMenu.Name = "panelMenu";
            this.panelMenu.Size = new System.Drawing.Size(246, 534);
            this.panelMenu.TabIndex = 0;
            // 
            // lblWorkingCheat
            // 
            this.lblWorkingCheat.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblWorkingCheat.ForeColor = System.Drawing.Color.MediumAquamarine;
            this.lblWorkingCheat.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.lblWorkingCheat.Location = new System.Drawing.Point(0, 505);
            this.lblWorkingCheat.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblWorkingCheat.Name = "lblWorkingCheat";
            this.lblWorkingCheat.Size = new System.Drawing.Size(246, 15);
            this.lblWorkingCheat.TabIndex = 9;
            this.lblWorkingCheat.Text = "Cheats are working ...";
            this.lblWorkingCheat.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblWorkingCheat.Visible = false;
            // 
            // panelLeft
            // 
            this.panelLeft.BackColor = System.Drawing.Color.PaleGreen;
            this.panelLeft.Location = new System.Drawing.Point(0, 240);
            this.panelLeft.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelLeft.Name = "panelLeft";
            this.panelLeft.Size = new System.Drawing.Size(14, 52);
            this.panelLeft.TabIndex = 1;
            this.panelLeft.Visible = false;
            // 
            // btnCheats
            // 
            this.btnCheats.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.btnCheats.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.btnCheats.Dock = System.Windows.Forms.DockStyle.Top;
            this.btnCheats.Enabled = false;
            this.btnCheats.FlatAppearance.BorderSize = 0;
            this.btnCheats.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCheats.Font = new System.Drawing.Font("Verdana", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCheats.ForeColor = System.Drawing.Color.Silver;
            this.btnCheats.Image = global::Heth_AIO.Properties.Resources.shield_interrogation;
            this.btnCheats.Location = new System.Drawing.Point(0, 450);
            this.btnCheats.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnCheats.Name = "btnCheats";
            this.btnCheats.Size = new System.Drawing.Size(246, 55);
            this.btnCheats.TabIndex = 5;
            this.btnCheats.Text = "     Cheats";
            this.btnCheats.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnCheats.UseVisualStyleBackColor = false;
            this.btnCheats.Click += new System.EventHandler(this.btnCheats_Click);
            // 
            // lblWorkingChecker
            // 
            this.lblWorkingChecker.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblWorkingChecker.ForeColor = System.Drawing.Color.MediumAquamarine;
            this.lblWorkingChecker.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.lblWorkingChecker.Location = new System.Drawing.Point(0, 435);
            this.lblWorkingChecker.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblWorkingChecker.Name = "lblWorkingChecker";
            this.lblWorkingChecker.Size = new System.Drawing.Size(246, 15);
            this.lblWorkingChecker.TabIndex = 8;
            this.lblWorkingChecker.Text = "Checkers are working ...";
            this.lblWorkingChecker.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblWorkingChecker.Visible = false;
            // 
            // btnCheckers
            // 
            this.btnCheckers.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.btnCheckers.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.btnCheckers.Dock = System.Windows.Forms.DockStyle.Top;
            this.btnCheckers.Enabled = false;
            this.btnCheckers.FlatAppearance.BorderSize = 0;
            this.btnCheckers.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCheckers.Font = new System.Drawing.Font("Verdana", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCheckers.ForeColor = System.Drawing.Color.Silver;
            this.btnCheckers.Image = global::Heth_AIO.Properties.Resources.pulse;
            this.btnCheckers.Location = new System.Drawing.Point(0, 380);
            this.btnCheckers.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnCheckers.Name = "btnCheckers";
            this.btnCheckers.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.btnCheckers.Size = new System.Drawing.Size(246, 55);
            this.btnCheckers.TabIndex = 4;
            this.btnCheckers.Text = "  Checkers";
            this.btnCheckers.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnCheckers.UseVisualStyleBackColor = false;
            this.btnCheckers.Click += new System.EventHandler(this.btnCheckers_Click);
            // 
            // lblWorkingAuto
            // 
            this.lblWorkingAuto.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblWorkingAuto.ForeColor = System.Drawing.Color.MediumAquamarine;
            this.lblWorkingAuto.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.lblWorkingAuto.Location = new System.Drawing.Point(0, 365);
            this.lblWorkingAuto.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblWorkingAuto.Name = "lblWorkingAuto";
            this.lblWorkingAuto.Size = new System.Drawing.Size(246, 15);
            this.lblWorkingAuto.TabIndex = 7;
            this.lblWorkingAuto.Text = "Autos are working ...";
            this.lblWorkingAuto.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblWorkingAuto.Visible = false;
            // 
            // btnAuto
            // 
            this.btnAuto.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.btnAuto.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.btnAuto.Dock = System.Windows.Forms.DockStyle.Top;
            this.btnAuto.Enabled = false;
            this.btnAuto.FlatAppearance.BorderSize = 0;
            this.btnAuto.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAuto.Font = new System.Drawing.Font("Verdana", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAuto.ForeColor = System.Drawing.Color.Silver;
            this.btnAuto.Image = global::Heth_AIO.Properties.Resources.doctor;
            this.btnAuto.Location = new System.Drawing.Point(0, 310);
            this.btnAuto.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnAuto.Name = "btnAuto";
            this.btnAuto.Size = new System.Drawing.Size(246, 55);
            this.btnAuto.TabIndex = 3;
            this.btnAuto.Text = "     Autos";
            this.btnAuto.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnAuto.UseVisualStyleBackColor = false;
            this.btnAuto.Click += new System.EventHandler(this.btnAuto_Click);
            // 
            // lblWorkingMacro
            // 
            this.lblWorkingMacro.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblWorkingMacro.ForeColor = System.Drawing.Color.MediumAquamarine;
            this.lblWorkingMacro.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.lblWorkingMacro.Location = new System.Drawing.Point(0, 295);
            this.lblWorkingMacro.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblWorkingMacro.Name = "lblWorkingMacro";
            this.lblWorkingMacro.Size = new System.Drawing.Size(246, 15);
            this.lblWorkingMacro.TabIndex = 6;
            this.lblWorkingMacro.Text = "Macro is working ...";
            this.lblWorkingMacro.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblWorkingMacro.Visible = false;
            this.lblWorkingMacro.Click += new System.EventHandler(this.label11_Click);
            // 
            // btnMacro
            // 
            this.btnMacro.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.btnMacro.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnMacro.Dock = System.Windows.Forms.DockStyle.Top;
            this.btnMacro.Enabled = false;
            this.btnMacro.FlatAppearance.BorderSize = 0;
            this.btnMacro.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnMacro.Font = new System.Drawing.Font("Verdana", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnMacro.ForeColor = System.Drawing.Color.Silver;
            this.btnMacro.Image = global::Heth_AIO.Properties.Resources.list_check;
            this.btnMacro.Location = new System.Drawing.Point(0, 240);
            this.btnMacro.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnMacro.Name = "btnMacro";
            this.btnMacro.Size = new System.Drawing.Size(246, 55);
            this.btnMacro.TabIndex = 2;
            this.btnMacro.Text = "     Macro";
            this.btnMacro.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnMacro.UseVisualStyleBackColor = false;
            this.btnMacro.Click += new System.EventHandler(this.button1_Click);
            this.btnMacro.MouseEnter += new System.EventHandler(this.btnMacro_MouseEnter);
            this.btnMacro.MouseLeave += new System.EventHandler(this.btnMacro_MouseLeave);
            this.btnMacro.MouseHover += new System.EventHandler(this.btnMacro_MouseHover);
            // 
            // panelStart
            // 
            this.panelStart.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.panelStart.Controls.Add(this.NewReaderCheckBox);
            this.panelStart.Controls.Add(this.safeModeCheckBox);
            this.panelStart.Controls.Add(this.lblMap);
            this.panelStart.Controls.Add(this.lblMana);
            this.panelStart.Controls.Add(this.lblHealth);
            this.panelStart.Controls.Add(this.label3);
            this.panelStart.Controls.Add(this.comboEngineType);
            this.panelStart.Controls.Add(this.label2);
            this.panelStart.Controls.Add(this.txtWinName);
            this.panelStart.Controls.Add(this.btnReset);
            this.panelStart.Controls.Add(this.lblName);
            this.panelStart.Controls.Add(this.lblExpire);
            this.panelStart.Controls.Add(this.btnInject);
            this.panelStart.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelStart.Location = new System.Drawing.Point(0, 0);
            this.panelStart.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelStart.Name = "panelStart";
            this.panelStart.Size = new System.Drawing.Size(246, 240);
            this.panelStart.TabIndex = 1;
            this.panelStart.MouseDown += new System.Windows.Forms.MouseEventHandler(this.panelStart_MouseDown);
            this.panelStart.MouseMove += new System.Windows.Forms.MouseEventHandler(this.panelStart_MouseMove);
            this.panelStart.MouseUp += new System.Windows.Forms.MouseEventHandler(this.panelStart_MouseUp);
            // 
            // NewReaderCheckBox
            // 
            this.NewReaderCheckBox.FlatAppearance.BorderSize = 0;
            this.NewReaderCheckBox.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.NewReaderCheckBox.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.NewReaderCheckBox.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.NewReaderCheckBox.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.NewReaderCheckBox.Location = new System.Drawing.Point(104, 132);
            this.NewReaderCheckBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.NewReaderCheckBox.Name = "NewReaderCheckBox";
            this.NewReaderCheckBox.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.NewReaderCheckBox.Size = new System.Drawing.Size(139, 20);
            this.NewReaderCheckBox.TabIndex = 23;
            this.NewReaderCheckBox.Text = "New Reader";
            this.NewReaderCheckBox.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.NewReaderCheckBox.UseVisualStyleBackColor = true;
            this.NewReaderCheckBox.CheckedChanged += new System.EventHandler(this.NewReaderCheckBox_CheckedChanged);
            // 
            // safeModeCheckBox
            // 
            this.safeModeCheckBox.FlatAppearance.BorderSize = 0;
            this.safeModeCheckBox.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.safeModeCheckBox.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.safeModeCheckBox.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.safeModeCheckBox.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.safeModeCheckBox.Location = new System.Drawing.Point(104, 151);
            this.safeModeCheckBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.safeModeCheckBox.Name = "safeModeCheckBox";
            this.safeModeCheckBox.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.safeModeCheckBox.Size = new System.Drawing.Size(139, 20);
            this.safeModeCheckBox.TabIndex = 22;
            this.safeModeCheckBox.Text = "Macro Only";
            this.safeModeCheckBox.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.safeModeCheckBox.UseVisualStyleBackColor = true;
            this.safeModeCheckBox.CheckedChanged += new System.EventHandler(this.checkBox1_CheckedChanged_1);
            // 
            // lblMap
            // 
            this.lblMap.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblMap.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblMap.ForeColor = System.Drawing.Color.Silver;
            this.lblMap.Image = global::Heth_AIO.Properties.Resources.mapico;
            this.lblMap.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblMap.Location = new System.Drawing.Point(0, 82);
            this.lblMap.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblMap.Name = "lblMap";
            this.lblMap.Padding = new System.Windows.Forms.Padding(6, 0, 0, 0);
            this.lblMap.Size = new System.Drawing.Size(246, 21);
            this.lblMap.TabIndex = 10;
            this.lblMap.Text = "Map: X:0|Y:0";
            this.lblMap.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblMana
            // 
            this.lblMana.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblMana.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblMana.ForeColor = System.Drawing.Color.Silver;
            this.lblMana.Image = global::Heth_AIO.Properties.Resources.test_tube;
            this.lblMana.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblMana.Location = new System.Drawing.Point(0, 61);
            this.lblMana.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblMana.Name = "lblMana";
            this.lblMana.Padding = new System.Windows.Forms.Padding(6, 0, 0, 0);
            this.lblMana.Size = new System.Drawing.Size(246, 21);
            this.lblMana.TabIndex = 8;
            this.lblMana.Text = "MP: 0/0";
            this.lblMana.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblHealth
            // 
            this.lblHealth.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblHealth.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblHealth.ForeColor = System.Drawing.Color.Silver;
            this.lblHealth.Image = global::Heth_AIO.Properties.Resources.heart;
            this.lblHealth.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblHealth.Location = new System.Drawing.Point(0, 40);
            this.lblHealth.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblHealth.Name = "lblHealth";
            this.lblHealth.Padding = new System.Windows.Forms.Padding(6, 0, 0, 0);
            this.lblHealth.Size = new System.Drawing.Size(246, 21);
            this.lblHealth.TabIndex = 7;
            this.lblHealth.Text = "HP: 0/0";
            this.lblHealth.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.ForeColor = System.Drawing.Color.Silver;
            this.label3.Location = new System.Drawing.Point(4, 110);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(50, 13);
            this.label3.TabIndex = 5;
            this.label3.Text = "Engine:";
            // 
            // comboEngineType
            // 
            this.comboEngineType.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.comboEngineType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboEngineType.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.comboEngineType.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.comboEngineType.ForeColor = System.Drawing.Color.Silver;
            this.comboEngineType.FormattingEnabled = true;
            this.comboEngineType.Items.AddRange(new object[] {
            "Hypernetwork | 20180821",
            "Bango | 2018",
            "New | 20190930",
            "Loading..",
            "Loading..",
            "Loading.."});
            this.comboEngineType.Location = new System.Drawing.Point(64, 106);
            this.comboEngineType.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.comboEngineType.Name = "comboEngineType";
            this.comboEngineType.Size = new System.Drawing.Size(178, 23);
            this.comboEngineType.TabIndex = 1;
            this.comboEngineType.SelectedIndexChanged += new System.EventHandler(this.comboEngineType_SelectedIndexChanged);
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.Silver;
            this.label2.Location = new System.Drawing.Point(4, 134);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(93, 13);
            this.label2.TabIndex = 1;
            this.label2.Text = "Window Name:";
            // 
            // txtWinName
            // 
            this.txtWinName.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtWinName.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtWinName.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtWinName.ForeColor = System.Drawing.Color.Silver;
            this.txtWinName.Location = new System.Drawing.Point(2, 150);
            this.txtWinName.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtWinName.Name = "txtWinName";
            this.txtWinName.Size = new System.Drawing.Size(94, 21);
            this.txtWinName.TabIndex = 1;
            this.txtWinName.Text = "KalAIO Online";
            this.txtWinName.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // btnReset
            // 
            this.btnReset.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnReset.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.btnReset.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.btnReset.Enabled = false;
            this.btnReset.FlatAppearance.BorderSize = 0;
            this.btnReset.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnReset.Font = new System.Drawing.Font("Verdana", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReset.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(166)))), ((int)(((byte)(28)))), ((int)(((byte)(60)))));
            this.btnReset.Image = global::Heth_AIO.Properties.Resources.rotate_right1;
            this.btnReset.Location = new System.Drawing.Point(0, 177);
            this.btnReset.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new System.Drawing.Size(246, 31);
            this.btnReset.TabIndex = 3;
            this.btnReset.Text = "         Reset";
            this.btnReset.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnReset.UseVisualStyleBackColor = false;
            this.btnReset.Click += new System.EventHandler(this.btnReset_Click);
            // 
            // lblName
            // 
            this.lblName.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblName.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblName.ForeColor = System.Drawing.Color.Silver;
            this.lblName.Image = global::Heth_AIO.Properties.Resources.address_book;
            this.lblName.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblName.Location = new System.Drawing.Point(0, 19);
            this.lblName.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblName.Name = "lblName";
            this.lblName.Padding = new System.Windows.Forms.Padding(6, 0, 0, 0);
            this.lblName.Size = new System.Drawing.Size(246, 21);
            this.lblName.TabIndex = 6;
            this.lblName.Text = "Name";
            this.lblName.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblName.MouseDown += new System.Windows.Forms.MouseEventHandler(this.lblName_MouseDown);
            this.lblName.MouseMove += new System.Windows.Forms.MouseEventHandler(this.lblName_MouseMove);
            this.lblName.MouseUp += new System.Windows.Forms.MouseEventHandler(this.lblName_MouseUp);
            // 
            // lblExpire
            // 
            this.lblExpire.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblExpire.Font = new System.Drawing.Font("Verdana", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblExpire.ForeColor = System.Drawing.Color.Silver;
            this.lblExpire.Image = global::Heth_AIO.Properties.Resources.alarm_clock;
            this.lblExpire.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblExpire.Location = new System.Drawing.Point(0, 0);
            this.lblExpire.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblExpire.Name = "lblExpire";
            this.lblExpire.Padding = new System.Windows.Forms.Padding(6, 0, 0, 0);
            this.lblExpire.Size = new System.Drawing.Size(246, 19);
            this.lblExpire.TabIndex = 9;
            this.lblExpire.Text = "Key Info";
            this.lblExpire.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblExpire.MouseDown += new System.Windows.Forms.MouseEventHandler(this.lblExpire_MouseDown);
            this.lblExpire.MouseMove += new System.Windows.Forms.MouseEventHandler(this.lblExpire_MouseMove);
            this.lblExpire.MouseUp += new System.Windows.Forms.MouseEventHandler(this.lblExpire_MouseUp);
            // 
            // btnInject
            // 
            this.btnInject.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnInject.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.btnInject.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.btnInject.FlatAppearance.BorderSize = 0;
            this.btnInject.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnInject.Font = new System.Drawing.Font("Verdana", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnInject.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(178)))), ((int)(((byte)(152)))), ((int)(((byte)(207)))));
            this.btnInject.Image = global::Heth_AIO.Properties.Resources.syringe;
            this.btnInject.Location = new System.Drawing.Point(0, 208);
            this.btnInject.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnInject.Name = "btnInject";
            this.btnInject.Padding = new System.Windows.Forms.Padding(13, 0, 0, 0);
            this.btnInject.Size = new System.Drawing.Size(246, 32);
            this.btnInject.TabIndex = 4;
            this.btnInject.Text = "         Inject [F5]";
            this.btnInject.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnInject.UseVisualStyleBackColor = false;
            this.btnInject.Click += new System.EventHandler(this.btnInject_Click);
            // 
            // panelControl
            // 
            this.panelControl.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.panelControl.Controls.Add(this.hotkeys);
            this.panelControl.Controls.Add(this.lblRunInfo);
            this.panelControl.Controls.Add(this.label1);
            this.panelControl.Controls.Add(this.minBTN);
            this.panelControl.Controls.Add(this.extBTN);
            this.panelControl.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl.Location = new System.Drawing.Point(246, 0);
            this.panelControl.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelControl.Name = "panelControl";
            this.panelControl.Size = new System.Drawing.Size(610, 25);
            this.panelControl.TabIndex = 1;
            this.panelControl.MouseDown += new System.Windows.Forms.MouseEventHandler(this.panel3_MouseDown);
            this.panelControl.MouseMove += new System.Windows.Forms.MouseEventHandler(this.panelControl_MouseMove);
            this.panelControl.MouseUp += new System.Windows.Forms.MouseEventHandler(this.panelControl_MouseUp);
            // 
            // hotkeys
            // 
            this.hotkeys.Checked = true;
            this.hotkeys.CheckState = System.Windows.Forms.CheckState.Checked;
            this.hotkeys.Dock = System.Windows.Forms.DockStyle.Left;
            this.hotkeys.FlatAppearance.BorderSize = 0;
            this.hotkeys.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.hotkeys.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.hotkeys.ForeColor = System.Drawing.Color.MediumAquamarine;
            this.hotkeys.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.hotkeys.Location = new System.Drawing.Point(69, 0);
            this.hotkeys.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.hotkeys.Name = "hotkeys";
            this.hotkeys.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.hotkeys.Size = new System.Drawing.Size(191, 25);
            this.hotkeys.TabIndex = 21;
            this.hotkeys.Text = "HotKeys (F2 F3 F5)";
            this.hotkeys.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.hotkeys.UseVisualStyleBackColor = true;
            this.hotkeys.CheckedChanged += new System.EventHandler(this.hotkeys_CheckedChanged);
            // 
            // lblRunInfo
            // 
            this.lblRunInfo.Dock = System.Windows.Forms.DockStyle.Right;
            this.lblRunInfo.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblRunInfo.ForeColor = System.Drawing.Color.Silver;
            this.lblRunInfo.Location = new System.Drawing.Point(283, 0);
            this.lblRunInfo.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblRunInfo.Name = "lblRunInfo";
            this.lblRunInfo.Size = new System.Drawing.Size(253, 25);
            this.lblRunInfo.TabIndex = 13;
            this.lblRunInfo.Text = "Waiting for Injection";
            this.lblRunInfo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblRunInfo.MouseDown += new System.Windows.Forms.MouseEventHandler(this.lblRunInfo_MouseDown);
            this.lblRunInfo.MouseMove += new System.Windows.Forms.MouseEventHandler(this.lblRunInfo_MouseMove);
            this.lblRunInfo.MouseUp += new System.Windows.Forms.MouseEventHandler(this.lblRunInfo_MouseUp);
            // 
            // label1
            // 
            this.label1.Dock = System.Windows.Forms.DockStyle.Left;
            this.label1.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.ForeColor = System.Drawing.SystemColors.AppWorkspace;
            this.label1.Location = new System.Drawing.Point(0, 0);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(69, 25);
            this.label1.TabIndex = 12;
            this.label1.Text = "App Info";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.label1.Click += new System.EventHandler(this.label1_Click);
            this.label1.MouseDown += new System.Windows.Forms.MouseEventHandler(this.label1_MouseDown);
            this.label1.MouseMove += new System.Windows.Forms.MouseEventHandler(this.label1_MouseMove);
            this.label1.MouseUp += new System.Windows.Forms.MouseEventHandler(this.label1_MouseUp);
            // 
            // minBTN
            // 
            this.minBTN.Dock = System.Windows.Forms.DockStyle.Right;
            this.minBTN.FlatAppearance.BorderSize = 0;
            this.minBTN.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.minBTN.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.minBTN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(179)))), ((int)(((byte)(181)))), ((int)(((byte)(183)))));
            this.minBTN.ImageAlign = System.Drawing.ContentAlignment.BottomCenter;
            this.minBTN.Location = new System.Drawing.Point(536, 0);
            this.minBTN.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.minBTN.Name = "minBTN";
            this.minBTN.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.minBTN.Size = new System.Drawing.Size(37, 25);
            this.minBTN.TabIndex = 11;
            this.minBTN.Text = "-";
            this.minBTN.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            this.minBTN.UseVisualStyleBackColor = true;
            this.minBTN.Click += new System.EventHandler(this.minBTN_Click);
            // 
            // extBTN
            // 
            this.extBTN.Dock = System.Windows.Forms.DockStyle.Right;
            this.extBTN.FlatAppearance.BorderSize = 0;
            this.extBTN.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.extBTN.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.extBTN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(179)))), ((int)(((byte)(181)))), ((int)(((byte)(183)))));
            this.extBTN.Location = new System.Drawing.Point(573, 0);
            this.extBTN.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.extBTN.Name = "extBTN";
            this.extBTN.Size = new System.Drawing.Size(37, 25);
            this.extBTN.TabIndex = 10;
            this.extBTN.Text = "X";
            this.extBTN.UseVisualStyleBackColor = true;
            this.extBTN.Click += new System.EventHandler(this.extBTN_Click);
            // 
            // panelMacro
            // 
            this.panelMacro.AutoSize = true;
            this.panelMacro.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.panelMacro.Controls.Add(this.panelMacroBox);
            this.panelMacro.Controls.Add(this.panelMacroCtrl);
            this.panelMacro.Controls.Add(this.panelMacroMisc);
            this.panelMacro.Controls.Add(this.panelMacroPresets);
            this.panelMacro.Controls.Add(this.panelMacroController);
            this.panelMacro.Location = new System.Drawing.Point(246, 96);
            this.panelMacro.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelMacro.Name = "panelMacro";
            this.panelMacro.Size = new System.Drawing.Size(610, 437);
            this.panelMacro.TabIndex = 2;
            // 
            // panelMacroBox
            // 
            this.panelMacroBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelMacroBox.Controls.Add(this.label15);
            this.panelMacroBox.Controls.Add(this.richMacroBox);
            this.panelMacroBox.Location = new System.Drawing.Point(45, 84);
            this.panelMacroBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelMacroBox.Name = "panelMacroBox";
            this.panelMacroBox.Size = new System.Drawing.Size(259, 200);
            this.panelMacroBox.TabIndex = 1;
            this.panelMacroBox.Paint += new System.Windows.Forms.PaintEventHandler(this.panelMacroBox_Paint_1);
            // 
            // label15
            // 
            this.label15.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label15.Dock = System.Windows.Forms.DockStyle.Top;
            this.label15.ForeColor = System.Drawing.Color.Silver;
            this.label15.Location = new System.Drawing.Point(0, 0);
            this.label15.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(259, 20);
            this.label15.TabIndex = 8;
            this.label15.Text = "Macro Commands:-";
            this.label15.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // richMacroBox
            // 
            this.richMacroBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.richMacroBox.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.richMacroBox.DetectUrls = false;
            this.richMacroBox.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.richMacroBox.Font = new System.Drawing.Font("Verdana", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.richMacroBox.ForeColor = System.Drawing.Color.Silver;
            this.richMacroBox.Location = new System.Drawing.Point(0, 24);
            this.richMacroBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.richMacroBox.Name = "richMacroBox";
            this.richMacroBox.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.richMacroBox.Size = new System.Drawing.Size(259, 176);
            this.richMacroBox.TabIndex = 0;
            this.richMacroBox.Text = "";
            this.richMacroBox.TextChanged += new System.EventHandler(this.richMacroBox_TextChanged);
            // 
            // panelMacroCtrl
            // 
            this.panelMacroCtrl.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelMacroCtrl.Controls.Add(this.label33);
            this.panelMacroCtrl.Controls.Add(this.richMacroCtrlBox);
            this.panelMacroCtrl.Location = new System.Drawing.Point(45, 289);
            this.panelMacroCtrl.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelMacroCtrl.Name = "panelMacroCtrl";
            this.panelMacroCtrl.Size = new System.Drawing.Size(259, 140);
            this.panelMacroCtrl.TabIndex = 5;
            this.panelMacroCtrl.Visible = false;
            // 
            // label33
            // 
            this.label33.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label33.Dock = System.Windows.Forms.DockStyle.Top;
            this.label33.ForeColor = System.Drawing.Color.Plum;
            this.label33.Location = new System.Drawing.Point(0, 0);
            this.label33.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(259, 20);
            this.label33.TabIndex = 8;
            this.label33.Text = "Macro Controller";
            this.label33.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // richMacroCtrlBox
            // 
            this.richMacroCtrlBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.richMacroCtrlBox.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.richMacroCtrlBox.DetectUrls = false;
            this.richMacroCtrlBox.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.richMacroCtrlBox.Font = new System.Drawing.Font("Verdana", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.richMacroCtrlBox.ForeColor = System.Drawing.Color.Silver;
            this.richMacroCtrlBox.Location = new System.Drawing.Point(0, 24);
            this.richMacroCtrlBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.richMacroCtrlBox.Name = "richMacroCtrlBox";
            this.richMacroCtrlBox.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.richMacroCtrlBox.Size = new System.Drawing.Size(259, 116);
            this.richMacroCtrlBox.TabIndex = 0;
            this.richMacroCtrlBox.Text = "";
            this.richMacroCtrlBox.TextChanged += new System.EventHandler(this.richMacroCtrlBox_TextChanged);
            // 
            // panelMacroMisc
            // 
            this.panelMacroMisc.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelMacroMisc.Controls.Add(this.btnSave);
            this.panelMacroMisc.Controls.Add(this.btnImport);
            this.panelMacroMisc.Controls.Add(this.lblMouseTrackerPosition);
            this.panelMacroMisc.Controls.Add(this.lblInfoMT);
            this.panelMacroMisc.Controls.Add(this.btnMouseTracker);
            this.panelMacroMisc.Controls.Add(this.label6);
            this.panelMacroMisc.Controls.Add(this.btnNewMacro);
            this.panelMacroMisc.Location = new System.Drawing.Point(319, 241);
            this.panelMacroMisc.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelMacroMisc.Name = "panelMacroMisc";
            this.panelMacroMisc.Size = new System.Drawing.Size(265, 183);
            this.panelMacroMisc.TabIndex = 4;
            // 
            // btnSave
            // 
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.btnSave.Dock = System.Windows.Forms.DockStyle.Right;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSave.ForeColor = System.Drawing.Color.Silver;
            this.btnSave.Location = new System.Drawing.Point(160, 153);
            this.btnSave.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(105, 30);
            this.btnSave.TabIndex = 12;
            this.btnSave.Text = "Save *.txt";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnImport
            // 
            this.btnImport.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.btnImport.Dock = System.Windows.Forms.DockStyle.Left;
            this.btnImport.FlatAppearance.BorderSize = 0;
            this.btnImport.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnImport.ForeColor = System.Drawing.Color.Silver;
            this.btnImport.Location = new System.Drawing.Point(0, 153);
            this.btnImport.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(105, 30);
            this.btnImport.TabIndex = 11;
            this.btnImport.Text = "Import *.txt";
            this.btnImport.UseVisualStyleBackColor = false;
            this.btnImport.Click += new System.EventHandler(this.btnImport_Click);
            // 
            // lblMouseTrackerPosition
            // 
            this.lblMouseTrackerPosition.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblMouseTrackerPosition.ForeColor = System.Drawing.Color.Silver;
            this.lblMouseTrackerPosition.Location = new System.Drawing.Point(0, 123);
            this.lblMouseTrackerPosition.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.lblMouseTrackerPosition.Name = "lblMouseTrackerPosition";
            this.lblMouseTrackerPosition.Size = new System.Drawing.Size(265, 30);
            this.lblMouseTrackerPosition.TabIndex = 10;
            this.lblMouseTrackerPosition.Text = "Mouse Position: 0,0";
            this.lblMouseTrackerPosition.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblMouseTrackerPosition.Visible = false;
            // 
            // lblInfoMT
            // 
            this.lblInfoMT.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblInfoMT.ForeColor = System.Drawing.Color.Silver;
            this.lblInfoMT.Location = new System.Drawing.Point(0, 102);
            this.lblInfoMT.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.lblInfoMT.Name = "lblInfoMT";
            this.lblInfoMT.Size = new System.Drawing.Size(265, 21);
            this.lblInfoMT.TabIndex = 7;
            this.lblInfoMT.Text = "F7 For Left Click |  F8 For Right Click";
            this.lblInfoMT.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblInfoMT.Visible = false;
            // 
            // btnMouseTracker
            // 
            this.btnMouseTracker.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnMouseTracker.Dock = System.Windows.Forms.DockStyle.Top;
            this.btnMouseTracker.FlatAppearance.BorderSize = 0;
            this.btnMouseTracker.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnMouseTracker.ForeColor = System.Drawing.Color.Silver;
            this.btnMouseTracker.Location = new System.Drawing.Point(0, 68);
            this.btnMouseTracker.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnMouseTracker.Name = "btnMouseTracker";
            this.btnMouseTracker.Size = new System.Drawing.Size(265, 34);
            this.btnMouseTracker.TabIndex = 0;
            this.btnMouseTracker.Text = "Enable Mouse Tracker";
            this.btnMouseTracker.UseVisualStyleBackColor = false;
            this.btnMouseTracker.Click += new System.EventHandler(this.btnMouseTracker_Click);
            // 
            // label6
            // 
            this.label6.Dock = System.Windows.Forms.DockStyle.Top;
            this.label6.ForeColor = System.Drawing.Color.Silver;
            this.label6.Location = new System.Drawing.Point(0, 48);
            this.label6.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(265, 20);
            this.label6.TabIndex = 6;
            this.label6.Text = "Add Mouse Clicks:-";
            this.label6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnNewMacro
            // 
            this.btnNewMacro.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnNewMacro.Dock = System.Windows.Forms.DockStyle.Top;
            this.btnNewMacro.FlatAppearance.BorderColor = System.Drawing.Color.Plum;
            this.btnNewMacro.FlatAppearance.BorderSize = 0;
            this.btnNewMacro.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNewMacro.ForeColor = System.Drawing.Color.Plum;
            this.btnNewMacro.Location = new System.Drawing.Point(0, 0);
            this.btnNewMacro.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnNewMacro.Name = "btnNewMacro";
            this.btnNewMacro.Size = new System.Drawing.Size(265, 48);
            this.btnNewMacro.TabIndex = 9;
            this.btnNewMacro.Text = "New Macro Window+";
            this.btnNewMacro.UseVisualStyleBackColor = false;
            this.btnNewMacro.Click += new System.EventHandler(this.btnNewMacro_Click);
            // 
            // panelMacroPresets
            // 
            this.panelMacroPresets.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelMacroPresets.Controls.Add(this.btnPresetSave);
            this.panelMacroPresets.Controls.Add(this.comboPresets);
            this.panelMacroPresets.Controls.Add(this.txtBoxPreset);
            this.panelMacroPresets.Controls.Add(this.label7);
            this.panelMacroPresets.Controls.Add(this.label22);
            this.panelMacroPresets.Location = new System.Drawing.Point(342, 102);
            this.panelMacroPresets.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelMacroPresets.Name = "panelMacroPresets";
            this.panelMacroPresets.Size = new System.Drawing.Size(222, 129);
            this.panelMacroPresets.TabIndex = 3;
            // 
            // btnPresetSave
            // 
            this.btnPresetSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnPresetSave.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.btnPresetSave.FlatAppearance.BorderSize = 0;
            this.btnPresetSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnPresetSave.ForeColor = System.Drawing.Color.Silver;
            this.btnPresetSave.Image = global::Heth_AIO.Properties.Resources.disk;
            this.btnPresetSave.Location = new System.Drawing.Point(0, 86);
            this.btnPresetSave.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnPresetSave.Name = "btnPresetSave";
            this.btnPresetSave.Size = new System.Drawing.Size(222, 43);
            this.btnPresetSave.TabIndex = 10;
            this.btnPresetSave.Text = "           Save";
            this.btnPresetSave.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnPresetSave.UseVisualStyleBackColor = false;
            this.btnPresetSave.Click += new System.EventHandler(this.btnPresetSave_Click);
            // 
            // comboPresets
            // 
            this.comboPresets.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.comboPresets.Dock = System.Windows.Forms.DockStyle.Top;
            this.comboPresets.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboPresets.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.comboPresets.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.comboPresets.ForeColor = System.Drawing.Color.Silver;
            this.comboPresets.FormattingEnabled = true;
            this.comboPresets.Items.AddRange(new object[] {
            "Default",
            "Preset 1",
            "Preset 2",
            "Preset 3",
            "Preset 4",
            "Preset 5"});
            this.comboPresets.Location = new System.Drawing.Point(0, 62);
            this.comboPresets.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.comboPresets.Name = "comboPresets";
            this.comboPresets.Size = new System.Drawing.Size(222, 24);
            this.comboPresets.TabIndex = 9;
            this.comboPresets.SelectedIndexChanged += new System.EventHandler(this.comboPresets_SelectedIndexChanged);
            // 
            // txtBoxPreset
            // 
            this.txtBoxPreset.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(14)))), ((int)(((byte)(14)))), ((int)(((byte)(14)))));
            this.txtBoxPreset.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtBoxPreset.Dock = System.Windows.Forms.DockStyle.Top;
            this.txtBoxPreset.Font = new System.Drawing.Font("Arial", 14.25F);
            this.txtBoxPreset.ForeColor = System.Drawing.Color.Silver;
            this.txtBoxPreset.Location = new System.Drawing.Point(0, 40);
            this.txtBoxPreset.Margin = new System.Windows.Forms.Padding(7, 6, 7, 6);
            this.txtBoxPreset.Name = "txtBoxPreset";
            this.txtBoxPreset.Size = new System.Drawing.Size(222, 22);
            this.txtBoxPreset.TabIndex = 8;
            this.txtBoxPreset.Text = "Default";
            this.txtBoxPreset.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtBoxPreset.TextChanged += new System.EventHandler(this.txtBoxPreset_TextChanged);
            // 
            // label7
            // 
            this.label7.Dock = System.Windows.Forms.DockStyle.Top;
            this.label7.ForeColor = System.Drawing.Color.Silver;
            this.label7.Location = new System.Drawing.Point(0, 20);
            this.label7.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(222, 20);
            this.label7.TabIndex = 7;
            this.label7.Text = "Name:";
            this.label7.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label22
            // 
            this.label22.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label22.Dock = System.Windows.Forms.DockStyle.Top;
            this.label22.ForeColor = System.Drawing.Color.Silver;
            this.label22.Location = new System.Drawing.Point(0, 0);
            this.label22.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(222, 20);
            this.label22.TabIndex = 13;
            this.label22.Text = "Macro Presets";
            this.label22.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelMacroController
            // 
            this.panelMacroController.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelMacroController.Controls.Add(this.btnMacroHelpCMD);
            this.panelMacroController.Controls.Add(this.label5);
            this.panelMacroController.Controls.Add(this.label4);
            this.panelMacroController.Controls.Add(this.btnStartBot);
            this.panelMacroController.Controls.Add(this.btnStopBot);
            this.panelMacroController.Location = new System.Drawing.Point(45, 16);
            this.panelMacroController.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelMacroController.Name = "panelMacroController";
            this.panelMacroController.Size = new System.Drawing.Size(530, 58);
            this.panelMacroController.TabIndex = 2;
            // 
            // btnMacroHelpCMD
            // 
            this.btnMacroHelpCMD.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnMacroHelpCMD.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnMacroHelpCMD.FlatAppearance.BorderSize = 0;
            this.btnMacroHelpCMD.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnMacroHelpCMD.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnMacroHelpCMD.ForeColor = System.Drawing.Color.MediumAquamarine;
            this.btnMacroHelpCMD.Location = new System.Drawing.Point(225, 0);
            this.btnMacroHelpCMD.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnMacroHelpCMD.Name = "btnMacroHelpCMD";
            this.btnMacroHelpCMD.Size = new System.Drawing.Size(80, 58);
            this.btnMacroHelpCMD.TabIndex = 10;
            this.btnMacroHelpCMD.Text = "Macro CMDs [Help]";
            this.btnMacroHelpCMD.UseVisualStyleBackColor = false;
            this.btnMacroHelpCMD.Click += new System.EventHandler(this.btnMacroHelpCMD_Click);
            // 
            // label5
            // 
            this.label5.Dock = System.Windows.Forms.DockStyle.Right;
            this.label5.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.ForeColor = System.Drawing.Color.Silver;
            this.label5.Location = new System.Drawing.Point(305, 0);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.label5.Size = new System.Drawing.Size(48, 58);
            this.label5.TabIndex = 7;
            this.label5.Text = "Stop F3";
            this.label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label4
            // 
            this.label4.Dock = System.Windows.Forms.DockStyle.Left;
            this.label4.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.ForeColor = System.Drawing.Color.Silver;
            this.label4.Location = new System.Drawing.Point(177, 0);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(48, 58);
            this.label4.TabIndex = 6;
            this.label4.Text = "Start F2";
            this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnStartBot
            // 
            this.btnStartBot.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnStartBot.Dock = System.Windows.Forms.DockStyle.Left;
            this.btnStartBot.FlatAppearance.BorderSize = 0;
            this.btnStartBot.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnStartBot.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnStartBot.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(146)))), ((int)(((byte)(191)))), ((int)(((byte)(177)))));
            this.btnStartBot.Image = global::Heth_AIO.Properties.Resources.play_alt;
            this.btnStartBot.Location = new System.Drawing.Point(0, 0);
            this.btnStartBot.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnStartBot.Name = "btnStartBot";
            this.btnStartBot.Size = new System.Drawing.Size(177, 58);
            this.btnStartBot.TabIndex = 2;
            this.btnStartBot.Text = "Start";
            this.btnStartBot.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btnStartBot.UseVisualStyleBackColor = false;
            this.btnStartBot.Click += new System.EventHandler(this.btnStartBot_Click);
            // 
            // btnStopBot
            // 
            this.btnStopBot.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnStopBot.Dock = System.Windows.Forms.DockStyle.Right;
            this.btnStopBot.Enabled = false;
            this.btnStopBot.FlatAppearance.BorderSize = 0;
            this.btnStopBot.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnStopBot.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnStopBot.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.btnStopBot.Image = global::Heth_AIO.Properties.Resources.ban;
            this.btnStopBot.Location = new System.Drawing.Point(353, 0);
            this.btnStopBot.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnStopBot.Name = "btnStopBot";
            this.btnStopBot.Size = new System.Drawing.Size(177, 58);
            this.btnStopBot.TabIndex = 1;
            this.btnStopBot.Text = "Stop";
            this.btnStopBot.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btnStopBot.UseVisualStyleBackColor = false;
            this.btnStopBot.Click += new System.EventHandler(this.btnStopBot_Click);
            // 
            // panelInject
            // 
            this.panelInject.AutoSize = true;
            this.panelInject.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.panelInject.Controls.Add(this.pictureBox1);
            this.panelInject.Location = new System.Drawing.Point(246, 96);
            this.panelInject.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelInject.Name = "panelInject";
            this.panelInject.Size = new System.Drawing.Size(610, 437);
            this.panelInject.TabIndex = 3;
            // 
            // pictureBox1
            // 
            this.pictureBox1.BackgroundImage = global::Heth_AIO.Properties.Resources.syringeBig;
            this.pictureBox1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.pictureBox1.Location = new System.Drawing.Point(190, 120);
            this.pictureBox1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(215, 196);
            this.pictureBox1.TabIndex = 1;
            this.pictureBox1.TabStop = false;
            // 
            // panelAuto
            // 
            this.panelAuto.AutoSize = true;
            this.panelAuto.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.panelAuto.Controls.Add(this.panelCheckerOther);
            this.panelAuto.Controls.Add(this.panelAutoAntiAFK);
            this.panelAuto.Controls.Add(this.panelAutoHPMP);
            this.panelAuto.Location = new System.Drawing.Point(246, 96);
            this.panelAuto.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelAuto.Name = "panelAuto";
            this.panelAuto.Size = new System.Drawing.Size(610, 437);
            this.panelAuto.TabIndex = 4;
            // 
            // panelCheckerOther
            // 
            this.panelCheckerOther.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheckerOther.Controls.Add(this.label10);
            this.panelCheckerOther.Controls.Add(this.quickSlotPageNum);
            this.panelCheckerOther.Controls.Add(this.checkQuickSlot);
            this.panelCheckerOther.Controls.Add(this.label39);
            this.panelCheckerOther.Location = new System.Drawing.Point(287, 288);
            this.panelCheckerOther.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheckerOther.Name = "panelCheckerOther";
            this.panelCheckerOther.Size = new System.Drawing.Size(290, 137);
            this.panelCheckerOther.TabIndex = 6;
            // 
            // label10
            // 
            this.label10.Dock = System.Windows.Forms.DockStyle.Top;
            this.label10.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label10.ForeColor = System.Drawing.Color.Silver;
            this.label10.Location = new System.Drawing.Point(0, 51);
            this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(290, 15);
            this.label10.TabIndex = 56;
            this.label10.Text = "Anti Accidantal Ctrl Click";
            this.label10.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // quickSlotPageNum
            // 
            this.quickSlotPageNum.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.quickSlotPageNum.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.quickSlotPageNum.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.quickSlotPageNum.ForeColor = System.Drawing.SystemColors.ActiveBorder;
            this.quickSlotPageNum.FormattingEnabled = true;
            this.quickSlotPageNum.Items.AddRange(new object[] {
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "0"});
            this.quickSlotPageNum.Location = new System.Drawing.Point(238, 25);
            this.quickSlotPageNum.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.quickSlotPageNum.MaxLength = 1;
            this.quickSlotPageNum.Name = "quickSlotPageNum";
            this.quickSlotPageNum.Size = new System.Drawing.Size(38, 21);
            this.quickSlotPageNum.TabIndex = 55;
            this.quickSlotPageNum.SelectedIndexChanged += new System.EventHandler(this.quickSlotPageNum_SelectedIndexChanged);
            // 
            // checkQuickSlot
            // 
            this.checkQuickSlot.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkQuickSlot.FlatAppearance.BorderSize = 0;
            this.checkQuickSlot.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkQuickSlot.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkQuickSlot.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkQuickSlot.Location = new System.Drawing.Point(0, 20);
            this.checkQuickSlot.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkQuickSlot.Name = "checkQuickSlot";
            this.checkQuickSlot.Padding = new System.Windows.Forms.Padding(6, 0, 0, 0);
            this.checkQuickSlot.Size = new System.Drawing.Size(290, 31);
            this.checkQuickSlot.TabIndex = 1;
            this.checkQuickSlot.Text = "SkillBar Freeze";
            this.checkQuickSlot.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkQuickSlot.UseVisualStyleBackColor = true;
            this.checkQuickSlot.CheckedChanged += new System.EventHandler(this.checkQuickSlot_CheckedChanged);
            // 
            // label39
            // 
            this.label39.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label39.Dock = System.Windows.Forms.DockStyle.Top;
            this.label39.ForeColor = System.Drawing.Color.Silver;
            this.label39.Location = new System.Drawing.Point(0, 0);
            this.label39.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(290, 20);
            this.label39.TabIndex = 54;
            this.label39.Text = "Other Stuff";
            this.label39.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelAutoAntiAFK
            // 
            this.panelAutoAntiAFK.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelAutoAntiAFK.Controls.Add(this.label37);
            this.panelAutoAntiAFK.Controls.Add(this.label36);
            this.panelAutoAntiAFK.Controls.Add(this.label35);
            this.panelAutoAntiAFK.Controls.Add(this.label34);
            this.panelAutoAntiAFK.Controls.Add(this.txtNormalAntiAfk);
            this.panelAutoAntiAFK.Controls.Add(this.mouseXNum);
            this.panelAutoAntiAFK.Controls.Add(this.mouseYNum);
            this.panelAutoAntiAFK.Controls.Add(this.checkAntiAFK);
            this.panelAutoAntiAFK.Controls.Add(this.label27);
            this.panelAutoAntiAFK.Location = new System.Drawing.Point(37, 289);
            this.panelAutoAntiAFK.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelAutoAntiAFK.Name = "panelAutoAntiAFK";
            this.panelAutoAntiAFK.Size = new System.Drawing.Size(243, 137);
            this.panelAutoAntiAFK.TabIndex = 5;
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.ForeColor = System.Drawing.Color.Silver;
            this.label37.Location = new System.Drawing.Point(164, 72);
            this.label37.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(14, 13);
            this.label37.TabIndex = 53;
            this.label37.Text = "Y";
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.ForeColor = System.Drawing.Color.Silver;
            this.label36.Location = new System.Drawing.Point(58, 71);
            this.label36.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(15, 13);
            this.label36.TabIndex = 52;
            this.label36.Text = "X";
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Font = new System.Drawing.Font("Microsoft Sans Serif", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label35.ForeColor = System.Drawing.Color.Silver;
            this.label35.Location = new System.Drawing.Point(59, 56);
            this.label35.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(116, 12);
            this.label35.TabIndex = 51;
            this.label35.Text = "Mouse Right Click Position";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.ForeColor = System.Drawing.Color.Silver;
            this.label34.Location = new System.Drawing.Point(34, 117);
            this.label34.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(72, 13);
            this.label34.TabIndex = 50;
            this.label34.Text = "Cooldown: ";
            // 
            // txtNormalAntiAfk
            // 
            this.txtNormalAntiAfk.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.txtNormalAntiAfk.ForeColor = System.Drawing.SystemColors.MenuBar;
            this.txtNormalAntiAfk.Location = new System.Drawing.Point(114, 113);
            this.txtNormalAntiAfk.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtNormalAntiAfk.Maximum = new decimal(new int[] {
            1410065407,
            2,
            0,
            0});
            this.txtNormalAntiAfk.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.txtNormalAntiAfk.Name = "txtNormalAntiAfk";
            this.txtNormalAntiAfk.Size = new System.Drawing.Size(100, 21);
            this.txtNormalAntiAfk.TabIndex = 49;
            this.txtNormalAntiAfk.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtNormalAntiAfk.Value = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            // 
            // mouseXNum
            // 
            this.mouseXNum.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.mouseXNum.ForeColor = System.Drawing.SystemColors.MenuBar;
            this.mouseXNum.Location = new System.Drawing.Point(35, 86);
            this.mouseXNum.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.mouseXNum.Maximum = new decimal(new int[] {
            6000,
            0,
            0,
            0});
            this.mouseXNum.Name = "mouseXNum";
            this.mouseXNum.Size = new System.Drawing.Size(80, 21);
            this.mouseXNum.TabIndex = 48;
            this.mouseXNum.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.mouseXNum.Value = new decimal(new int[] {
            150,
            0,
            0,
            0});
            // 
            // mouseYNum
            // 
            this.mouseYNum.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.mouseYNum.ForeColor = System.Drawing.SystemColors.MenuBar;
            this.mouseYNum.Location = new System.Drawing.Point(134, 86);
            this.mouseYNum.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.mouseYNum.Maximum = new decimal(new int[] {
            6000,
            0,
            0,
            0});
            this.mouseYNum.Name = "mouseYNum";
            this.mouseYNum.Size = new System.Drawing.Size(80, 21);
            this.mouseYNum.TabIndex = 47;
            this.mouseYNum.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.mouseYNum.Value = new decimal(new int[] {
            150,
            0,
            0,
            0});
            // 
            // checkAntiAFK
            // 
            this.checkAntiAFK.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkAntiAFK.FlatAppearance.BorderSize = 0;
            this.checkAntiAFK.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkAntiAFK.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkAntiAFK.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkAntiAFK.Location = new System.Drawing.Point(0, 20);
            this.checkAntiAFK.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkAntiAFK.Name = "checkAntiAFK";
            this.checkAntiAFK.Padding = new System.Windows.Forms.Padding(23, 0, 0, 0);
            this.checkAntiAFK.Size = new System.Drawing.Size(243, 45);
            this.checkAntiAFK.TabIndex = 1;
            this.checkAntiAFK.Text = "Anti Ingame Window";
            this.checkAntiAFK.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkAntiAFK.UseVisualStyleBackColor = true;
            this.checkAntiAFK.CheckedChanged += new System.EventHandler(this.checkAntiAFK_CheckedChanged);
            // 
            // label27
            // 
            this.label27.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label27.Dock = System.Windows.Forms.DockStyle.Top;
            this.label27.ForeColor = System.Drawing.Color.Silver;
            this.label27.Location = new System.Drawing.Point(0, 0);
            this.label27.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(243, 20);
            this.label27.TabIndex = 54;
            this.label27.Text = "Anti Trade | Party | Window | Chat";
            this.label27.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelAutoHPMP
            // 
            this.panelAutoHPMP.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelAutoHPMP.Controls.Add(this.lblAutoHP);
            this.panelAutoHPMP.Controls.Add(this.trackBarHP);
            this.panelAutoHPMP.Controls.Add(this.manaBox);
            this.panelAutoHPMP.Controls.Add(this.healthBox);
            this.panelAutoHPMP.Controls.Add(this.checkAutoMana);
            this.panelAutoHPMP.Controls.Add(this.checkAutoHealth);
            this.panelAutoHPMP.Controls.Add(this.label26);
            this.panelAutoHPMP.Controls.Add(this.trackBarMP);
            this.panelAutoHPMP.Controls.Add(this.lblAutoMP);
            this.panelAutoHPMP.Location = new System.Drawing.Point(163, 19);
            this.panelAutoHPMP.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelAutoHPMP.Name = "panelAutoHPMP";
            this.panelAutoHPMP.Size = new System.Drawing.Size(284, 241);
            this.panelAutoHPMP.TabIndex = 4;
            // 
            // lblAutoHP
            // 
            this.lblAutoHP.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblAutoHP.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAutoHP.ForeColor = System.Drawing.Color.Silver;
            this.lblAutoHP.Location = new System.Drawing.Point(0, 99);
            this.lblAutoHP.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblAutoHP.Name = "lblAutoHP";
            this.lblAutoHP.Size = new System.Drawing.Size(284, 21);
            this.lblAutoHP.TabIndex = 51;
            this.lblAutoHP.Text = "When HP Reaches: 0";
            this.lblAutoHP.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // trackBarHP
            // 
            this.trackBarHP.AutoSize = false;
            this.trackBarHP.Dock = System.Windows.Forms.DockStyle.Top;
            this.trackBarHP.LargeChange = 100;
            this.trackBarHP.Location = new System.Drawing.Point(0, 75);
            this.trackBarHP.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.trackBarHP.Maximum = 3700;
            this.trackBarHP.Name = "trackBarHP";
            this.trackBarHP.Size = new System.Drawing.Size(284, 24);
            this.trackBarHP.SmallChange = 100;
            this.trackBarHP.TabIndex = 49;
            this.trackBarHP.TickFrequency = 50;
            this.trackBarHP.TickStyle = System.Windows.Forms.TickStyle.None;
            this.trackBarHP.Value = 500;
            this.trackBarHP.Scroll += new System.EventHandler(this.trackBarHP_Scroll);
            // 
            // manaBox
            // 
            this.manaBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.manaBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.manaBox.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.manaBox.ForeColor = System.Drawing.SystemColors.ActiveBorder;
            this.manaBox.FormattingEnabled = true;
            this.manaBox.Items.AddRange(new object[] {
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "0"});
            this.manaBox.Location = new System.Drawing.Point(229, 156);
            this.manaBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.manaBox.MaxLength = 1;
            this.manaBox.Name = "manaBox";
            this.manaBox.Size = new System.Drawing.Size(38, 21);
            this.manaBox.TabIndex = 47;
            // 
            // healthBox
            // 
            this.healthBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.healthBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.healthBox.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.healthBox.ForeColor = System.Drawing.SystemColors.ActiveBorder;
            this.healthBox.FormattingEnabled = true;
            this.healthBox.Items.AddRange(new object[] {
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "0"});
            this.healthBox.Location = new System.Drawing.Point(231, 36);
            this.healthBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.healthBox.MaxLength = 1;
            this.healthBox.Name = "healthBox";
            this.healthBox.Size = new System.Drawing.Size(38, 21);
            this.healthBox.TabIndex = 46;
            // 
            // checkAutoMana
            // 
            this.checkAutoMana.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.checkAutoMana.FlatAppearance.BorderSize = 0;
            this.checkAutoMana.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkAutoMana.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkAutoMana.Image = global::Heth_AIO.Properties.Resources.test_tube;
            this.checkAutoMana.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkAutoMana.Location = new System.Drawing.Point(0, 141);
            this.checkAutoMana.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkAutoMana.Name = "checkAutoMana";
            this.checkAutoMana.Padding = new System.Windows.Forms.Padding(23, 0, 23, 0);
            this.checkAutoMana.Size = new System.Drawing.Size(284, 55);
            this.checkAutoMana.TabIndex = 1;
            this.checkAutoMana.Text = "Auto Mana";
            this.checkAutoMana.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkAutoMana.UseVisualStyleBackColor = true;
            this.checkAutoMana.CheckedChanged += new System.EventHandler(this.checkAutoMana_CheckedChanged);
            // 
            // checkAutoHealth
            // 
            this.checkAutoHealth.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkAutoHealth.FlatAppearance.BorderSize = 0;
            this.checkAutoHealth.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkAutoHealth.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkAutoHealth.Image = global::Heth_AIO.Properties.Resources.heart;
            this.checkAutoHealth.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkAutoHealth.Location = new System.Drawing.Point(0, 20);
            this.checkAutoHealth.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkAutoHealth.Name = "checkAutoHealth";
            this.checkAutoHealth.Padding = new System.Windows.Forms.Padding(23, 0, 23, 0);
            this.checkAutoHealth.Size = new System.Drawing.Size(284, 55);
            this.checkAutoHealth.TabIndex = 0;
            this.checkAutoHealth.Text = "Auto Health";
            this.checkAutoHealth.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkAutoHealth.UseVisualStyleBackColor = true;
            this.checkAutoHealth.CheckedChanged += new System.EventHandler(this.checkAutoHealth_CheckedChanged);
            // 
            // label26
            // 
            this.label26.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label26.Dock = System.Windows.Forms.DockStyle.Top;
            this.label26.ForeColor = System.Drawing.Color.Silver;
            this.label26.Location = new System.Drawing.Point(0, 0);
            this.label26.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(284, 20);
            this.label26.TabIndex = 48;
            this.label26.Text = "Auto Health | Mana";
            this.label26.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // trackBarMP
            // 
            this.trackBarMP.AutoSize = false;
            this.trackBarMP.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.trackBarMP.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.trackBarMP.LargeChange = 100;
            this.trackBarMP.Location = new System.Drawing.Point(0, 196);
            this.trackBarMP.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.trackBarMP.Maximum = 1000;
            this.trackBarMP.Name = "trackBarMP";
            this.trackBarMP.Size = new System.Drawing.Size(284, 24);
            this.trackBarMP.SmallChange = 100;
            this.trackBarMP.TabIndex = 50;
            this.trackBarMP.TickFrequency = 50;
            this.trackBarMP.TickStyle = System.Windows.Forms.TickStyle.None;
            this.trackBarMP.Value = 500;
            this.trackBarMP.Scroll += new System.EventHandler(this.trackBarMP_Scroll);
            // 
            // lblAutoMP
            // 
            this.lblAutoMP.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.lblAutoMP.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAutoMP.ForeColor = System.Drawing.Color.Silver;
            this.lblAutoMP.Location = new System.Drawing.Point(0, 220);
            this.lblAutoMP.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblAutoMP.Name = "lblAutoMP";
            this.lblAutoMP.Size = new System.Drawing.Size(284, 21);
            this.lblAutoMP.TabIndex = 52;
            this.lblAutoMP.Text = "When MP Reaches: 0";
            this.lblAutoMP.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelCheckers
            // 
            this.panelCheckers.AutoSize = true;
            this.panelCheckers.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.panelCheckers.Controls.Add(this.button1);
            this.panelCheckers.Controls.Add(this.panelCheckersFindPlayer);
            this.panelCheckers.Controls.Add(this.panelCheckerDeathStuff);
            this.panelCheckers.Controls.Add(this.panelCheckersDT);
            this.panelCheckers.Controls.Add(this.panelCheckerTimers);
            this.panelCheckers.Location = new System.Drawing.Point(246, 96);
            this.panelCheckers.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheckers.Name = "panelCheckers";
            this.panelCheckers.Size = new System.Drawing.Size(610, 437);
            this.panelCheckers.TabIndex = 5;
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(460, 20);
            this.button1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(47, 23);
            this.button1.TabIndex = 8;
            this.button1.Text = "SS";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Visible = false;
            this.button1.Click += new System.EventHandler(this.button1_Click_1);
            // 
            // panelCheckersFindPlayer
            // 
            this.panelCheckersFindPlayer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheckersFindPlayer.Controls.Add(this.checkAnyPlayerFinder);
            this.panelCheckersFindPlayer.Controls.Add(this.label49);
            this.panelCheckersFindPlayer.Controls.Add(this.checkPlayerFinder);
            this.panelCheckersFindPlayer.Controls.Add(this.txtPlayerNames);
            this.panelCheckersFindPlayer.Controls.Add(this.lblPlayerFound);
            this.panelCheckersFindPlayer.Controls.Add(this.label9);
            this.panelCheckersFindPlayer.Location = new System.Drawing.Point(22, 100);
            this.panelCheckersFindPlayer.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheckersFindPlayer.Name = "panelCheckersFindPlayer";
            this.panelCheckersFindPlayer.Size = new System.Drawing.Size(293, 160);
            this.panelCheckersFindPlayer.TabIndex = 7;
            // 
            // checkAnyPlayerFinder
            // 
            this.checkAnyPlayerFinder.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkAnyPlayerFinder.FlatAppearance.BorderSize = 0;
            this.checkAnyPlayerFinder.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkAnyPlayerFinder.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold);
            this.checkAnyPlayerFinder.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkAnyPlayerFinder.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkAnyPlayerFinder.Location = new System.Drawing.Point(0, 122);
            this.checkAnyPlayerFinder.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkAnyPlayerFinder.Name = "checkAnyPlayerFinder";
            this.checkAnyPlayerFinder.Padding = new System.Windows.Forms.Padding(23, 0, 23, 0);
            this.checkAnyPlayerFinder.Size = new System.Drawing.Size(293, 33);
            this.checkAnyPlayerFinder.TabIndex = 16;
            this.checkAnyPlayerFinder.Text = "Player Checker";
            this.checkAnyPlayerFinder.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkAnyPlayerFinder.UseVisualStyleBackColor = true;
            this.checkAnyPlayerFinder.CheckedChanged += new System.EventHandler(this.checkAnyPlayerFinder_CheckedChanged);
            // 
            // label49
            // 
            this.label49.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(24)))), ((int)(((byte)(24)))));
            this.label49.Dock = System.Windows.Forms.DockStyle.Top;
            this.label49.ForeColor = System.Drawing.Color.Silver;
            this.label49.Location = new System.Drawing.Point(0, 100);
            this.label49.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label49.Name = "label49";
            this.label49.Size = new System.Drawing.Size(293, 22);
            this.label49.TabIndex = 17;
            this.label49.Text = "checks if any player is near";
            this.label49.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // checkPlayerFinder
            // 
            this.checkPlayerFinder.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkPlayerFinder.FlatAppearance.BorderSize = 0;
            this.checkPlayerFinder.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkPlayerFinder.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold);
            this.checkPlayerFinder.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkPlayerFinder.Image = global::Heth_AIO.Properties.Resources.search;
            this.checkPlayerFinder.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkPlayerFinder.Location = new System.Drawing.Point(0, 67);
            this.checkPlayerFinder.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkPlayerFinder.Name = "checkPlayerFinder";
            this.checkPlayerFinder.Padding = new System.Windows.Forms.Padding(23, 0, 23, 0);
            this.checkPlayerFinder.Size = new System.Drawing.Size(293, 33);
            this.checkPlayerFinder.TabIndex = 14;
            this.checkPlayerFinder.Text = "Player Name Finder";
            this.checkPlayerFinder.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkPlayerFinder.UseVisualStyleBackColor = true;
            this.checkPlayerFinder.CheckedChanged += new System.EventHandler(this.checkPlayerFinder_CheckedChanged);
            // 
            // txtPlayerNames
            // 
            this.txtPlayerNames.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtPlayerNames.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtPlayerNames.Dock = System.Windows.Forms.DockStyle.Top;
            this.txtPlayerNames.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtPlayerNames.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtPlayerNames.Location = new System.Drawing.Point(0, 45);
            this.txtPlayerNames.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtPlayerNames.Name = "txtPlayerNames";
            this.txtPlayerNames.Size = new System.Drawing.Size(293, 22);
            this.txtPlayerNames.TabIndex = 13;
            this.txtPlayerNames.Text = "Admin,GameMaster,<A>Admin";
            this.txtPlayerNames.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // lblPlayerFound
            // 
            this.lblPlayerFound.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(24)))), ((int)(((byte)(24)))));
            this.lblPlayerFound.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblPlayerFound.ForeColor = System.Drawing.Color.Silver;
            this.lblPlayerFound.Location = new System.Drawing.Point(0, 23);
            this.lblPlayerFound.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.lblPlayerFound.Name = "lblPlayerFound";
            this.lblPlayerFound.Size = new System.Drawing.Size(293, 22);
            this.lblPlayerFound.TabIndex = 15;
            this.lblPlayerFound.Text = "type the exact name !";
            this.lblPlayerFound.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label9
            // 
            this.label9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(24)))), ((int)(((byte)(24)))));
            this.label9.Dock = System.Windows.Forms.DockStyle.Top;
            this.label9.Font = new System.Drawing.Font("Microsoft Sans Serif", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.ForeColor = System.Drawing.Color.Silver;
            this.label9.Location = new System.Drawing.Point(0, 0);
            this.label9.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(293, 23);
            this.label9.TabIndex = 12;
            this.label9.Text = "Plays sound when it finds any name near (even mobs)";
            this.label9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelCheckerDeathStuff
            // 
            this.panelCheckerDeathStuff.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheckerDeathStuff.Controls.Add(this.checkXY);
            this.panelCheckerDeathStuff.Controls.Add(this.label44);
            this.panelCheckerDeathStuff.Controls.Add(this.txtBoxMapY4);
            this.panelCheckerDeathStuff.Controls.Add(this.label45);
            this.panelCheckerDeathStuff.Controls.Add(this.txtBoxMapX4);
            this.panelCheckerDeathStuff.Controls.Add(this.label46);
            this.panelCheckerDeathStuff.Controls.Add(this.txtBoxMapY3);
            this.panelCheckerDeathStuff.Controls.Add(this.label47);
            this.panelCheckerDeathStuff.Controls.Add(this.txtBoxMapX3);
            this.panelCheckerDeathStuff.Controls.Add(this.label42);
            this.panelCheckerDeathStuff.Controls.Add(this.txtBoxMapY2);
            this.panelCheckerDeathStuff.Controls.Add(this.label43);
            this.panelCheckerDeathStuff.Controls.Add(this.txtBoxMapX2);
            this.panelCheckerDeathStuff.Controls.Add(this.label41);
            this.panelCheckerDeathStuff.Controls.Add(this.txtBoxMapY1);
            this.panelCheckerDeathStuff.Controls.Add(this.label40);
            this.panelCheckerDeathStuff.Controls.Add(this.txtBoxMapX1);
            this.panelCheckerDeathStuff.Controls.Add(this.label38);
            this.panelCheckerDeathStuff.Controls.Add(this.checkAlertLowHP);
            this.panelCheckerDeathStuff.Controls.Add(this.checkCloseDead);
            this.panelCheckerDeathStuff.Controls.Add(this.checkSSDead);
            this.panelCheckerDeathStuff.Controls.Add(this.checkStopDead);
            this.panelCheckerDeathStuff.Location = new System.Drawing.Point(365, 101);
            this.panelCheckerDeathStuff.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheckerDeathStuff.Name = "panelCheckerDeathStuff";
            this.panelCheckerDeathStuff.Size = new System.Drawing.Size(233, 321);
            this.panelCheckerDeathStuff.TabIndex = 6;
            // 
            // checkXY
            // 
            this.checkXY.FlatAppearance.BorderSize = 0;
            this.checkXY.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkXY.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkXY.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkXY.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkXY.Location = new System.Drawing.Point(0, 268);
            this.checkXY.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkXY.Name = "checkXY";
            this.checkXY.Padding = new System.Windows.Forms.Padding(23, 0, 0, 0);
            this.checkXY.Size = new System.Drawing.Size(233, 31);
            this.checkXY.TabIndex = 30;
            this.checkXY.Text = "[Stop] When In X|Y";
            this.checkXY.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkXY.UseVisualStyleBackColor = true;
            this.checkXY.CheckedChanged += new System.EventHandler(this.checkXY_CheckedChanged);
            // 
            // label44
            // 
            this.label44.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label44.ForeColor = System.Drawing.Color.Silver;
            this.label44.Location = new System.Drawing.Point(121, 229);
            this.label44.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(21, 20);
            this.label44.TabIndex = 29;
            this.label44.Text = "Y: ";
            this.label44.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtBoxMapY4
            // 
            this.txtBoxMapY4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtBoxMapY4.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtBoxMapY4.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBoxMapY4.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtBoxMapY4.Location = new System.Drawing.Point(144, 228);
            this.txtBoxMapY4.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtBoxMapY4.Name = "txtBoxMapY4";
            this.txtBoxMapY4.Size = new System.Drawing.Size(48, 22);
            this.txtBoxMapY4.TabIndex = 28;
            this.txtBoxMapY4.Text = "0";
            this.txtBoxMapY4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label45
            // 
            this.label45.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label45.ForeColor = System.Drawing.Color.Silver;
            this.label45.Location = new System.Drawing.Point(38, 229);
            this.label45.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(21, 20);
            this.label45.TabIndex = 27;
            this.label45.Text = "X: ";
            this.label45.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtBoxMapX4
            // 
            this.txtBoxMapX4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtBoxMapX4.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtBoxMapX4.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBoxMapX4.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtBoxMapX4.Location = new System.Drawing.Point(62, 228);
            this.txtBoxMapX4.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtBoxMapX4.Name = "txtBoxMapX4";
            this.txtBoxMapX4.Size = new System.Drawing.Size(48, 22);
            this.txtBoxMapX4.TabIndex = 26;
            this.txtBoxMapX4.Text = "0";
            this.txtBoxMapX4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label46
            // 
            this.label46.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label46.ForeColor = System.Drawing.Color.Silver;
            this.label46.Location = new System.Drawing.Point(121, 204);
            this.label46.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(21, 20);
            this.label46.TabIndex = 25;
            this.label46.Text = "Y: ";
            this.label46.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtBoxMapY3
            // 
            this.txtBoxMapY3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtBoxMapY3.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtBoxMapY3.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBoxMapY3.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtBoxMapY3.Location = new System.Drawing.Point(144, 203);
            this.txtBoxMapY3.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtBoxMapY3.Name = "txtBoxMapY3";
            this.txtBoxMapY3.Size = new System.Drawing.Size(48, 22);
            this.txtBoxMapY3.TabIndex = 24;
            this.txtBoxMapY3.Text = "0";
            this.txtBoxMapY3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label47
            // 
            this.label47.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label47.ForeColor = System.Drawing.Color.Silver;
            this.label47.Location = new System.Drawing.Point(38, 204);
            this.label47.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label47.Name = "label47";
            this.label47.Size = new System.Drawing.Size(21, 20);
            this.label47.TabIndex = 23;
            this.label47.Text = "X: ";
            this.label47.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtBoxMapX3
            // 
            this.txtBoxMapX3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtBoxMapX3.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtBoxMapX3.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBoxMapX3.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtBoxMapX3.Location = new System.Drawing.Point(62, 203);
            this.txtBoxMapX3.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtBoxMapX3.Name = "txtBoxMapX3";
            this.txtBoxMapX3.Size = new System.Drawing.Size(48, 22);
            this.txtBoxMapX3.TabIndex = 22;
            this.txtBoxMapX3.Text = "0";
            this.txtBoxMapX3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label42
            // 
            this.label42.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label42.ForeColor = System.Drawing.Color.Silver;
            this.label42.Location = new System.Drawing.Point(121, 179);
            this.label42.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(21, 20);
            this.label42.TabIndex = 21;
            this.label42.Text = "Y: ";
            this.label42.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtBoxMapY2
            // 
            this.txtBoxMapY2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtBoxMapY2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtBoxMapY2.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBoxMapY2.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtBoxMapY2.Location = new System.Drawing.Point(144, 178);
            this.txtBoxMapY2.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtBoxMapY2.Name = "txtBoxMapY2";
            this.txtBoxMapY2.Size = new System.Drawing.Size(48, 22);
            this.txtBoxMapY2.TabIndex = 20;
            this.txtBoxMapY2.Text = "0";
            this.txtBoxMapY2.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label43
            // 
            this.label43.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label43.ForeColor = System.Drawing.Color.Silver;
            this.label43.Location = new System.Drawing.Point(38, 179);
            this.label43.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(21, 20);
            this.label43.TabIndex = 19;
            this.label43.Text = "X: ";
            this.label43.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtBoxMapX2
            // 
            this.txtBoxMapX2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtBoxMapX2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtBoxMapX2.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBoxMapX2.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtBoxMapX2.Location = new System.Drawing.Point(62, 178);
            this.txtBoxMapX2.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtBoxMapX2.Name = "txtBoxMapX2";
            this.txtBoxMapX2.Size = new System.Drawing.Size(48, 22);
            this.txtBoxMapX2.TabIndex = 18;
            this.txtBoxMapX2.Text = "0";
            this.txtBoxMapX2.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label41
            // 
            this.label41.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label41.ForeColor = System.Drawing.Color.Silver;
            this.label41.Location = new System.Drawing.Point(121, 154);
            this.label41.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(21, 20);
            this.label41.TabIndex = 17;
            this.label41.Text = "Y: ";
            this.label41.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtBoxMapY1
            // 
            this.txtBoxMapY1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtBoxMapY1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtBoxMapY1.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBoxMapY1.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtBoxMapY1.Location = new System.Drawing.Point(144, 153);
            this.txtBoxMapY1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtBoxMapY1.Name = "txtBoxMapY1";
            this.txtBoxMapY1.Size = new System.Drawing.Size(48, 22);
            this.txtBoxMapY1.TabIndex = 16;
            this.txtBoxMapY1.Text = "0";
            this.txtBoxMapY1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label40
            // 
            this.label40.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label40.ForeColor = System.Drawing.Color.Silver;
            this.label40.Location = new System.Drawing.Point(38, 154);
            this.label40.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(21, 20);
            this.label40.TabIndex = 15;
            this.label40.Text = "X: ";
            this.label40.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtBoxMapX1
            // 
            this.txtBoxMapX1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtBoxMapX1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtBoxMapX1.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBoxMapX1.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtBoxMapX1.Location = new System.Drawing.Point(62, 153);
            this.txtBoxMapX1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtBoxMapX1.Name = "txtBoxMapX1";
            this.txtBoxMapX1.Size = new System.Drawing.Size(48, 22);
            this.txtBoxMapX1.TabIndex = 14;
            this.txtBoxMapX1.Text = "0";
            this.txtBoxMapX1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label38
            // 
            this.label38.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(24)))), ((int)(((byte)(24)))));
            this.label38.Dock = System.Windows.Forms.DockStyle.Top;
            this.label38.ForeColor = System.Drawing.Color.Silver;
            this.label38.Location = new System.Drawing.Point(0, 124);
            this.label38.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(233, 20);
            this.label38.TabIndex = 13;
            this.label38.Text = "Stop macro when in X:Y";
            this.label38.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // checkAlertLowHP
            // 
            this.checkAlertLowHP.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkAlertLowHP.FlatAppearance.BorderSize = 0;
            this.checkAlertLowHP.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkAlertLowHP.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkAlertLowHP.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkAlertLowHP.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkAlertLowHP.Location = new System.Drawing.Point(0, 93);
            this.checkAlertLowHP.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkAlertLowHP.Name = "checkAlertLowHP";
            this.checkAlertLowHP.Padding = new System.Windows.Forms.Padding(23, 0, 0, 0);
            this.checkAlertLowHP.Size = new System.Drawing.Size(233, 31);
            this.checkAlertLowHP.TabIndex = 4;
            this.checkAlertLowHP.Text = "[Alert] When Low HP";
            this.checkAlertLowHP.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkAlertLowHP.UseVisualStyleBackColor = true;
            this.checkAlertLowHP.CheckedChanged += new System.EventHandler(this.checkAlertLowHP_CheckedChanged);
            // 
            // checkCloseDead
            // 
            this.checkCloseDead.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkCloseDead.FlatAppearance.BorderSize = 0;
            this.checkCloseDead.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkCloseDead.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkCloseDead.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkCloseDead.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkCloseDead.Location = new System.Drawing.Point(0, 62);
            this.checkCloseDead.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkCloseDead.Name = "checkCloseDead";
            this.checkCloseDead.Padding = new System.Windows.Forms.Padding(23, 0, 0, 0);
            this.checkCloseDead.Size = new System.Drawing.Size(233, 31);
            this.checkCloseDead.TabIndex = 3;
            this.checkCloseDead.Text = "[Close] When Dead";
            this.checkCloseDead.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkCloseDead.UseVisualStyleBackColor = true;
            this.checkCloseDead.CheckedChanged += new System.EventHandler(this.checkCloseDead_CheckedChanged);
            // 
            // checkSSDead
            // 
            this.checkSSDead.Checked = true;
            this.checkSSDead.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkSSDead.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkSSDead.FlatAppearance.BorderSize = 0;
            this.checkSSDead.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkSSDead.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold);
            this.checkSSDead.ForeColor = System.Drawing.Color.MediumAquamarine;
            this.checkSSDead.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkSSDead.Location = new System.Drawing.Point(0, 31);
            this.checkSSDead.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkSSDead.Name = "checkSSDead";
            this.checkSSDead.Padding = new System.Windows.Forms.Padding(23, 0, 0, 0);
            this.checkSSDead.Size = new System.Drawing.Size(233, 31);
            this.checkSSDead.TabIndex = 5;
            this.checkSSDead.Text = "Take SS When Dead";
            this.checkSSDead.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkSSDead.UseVisualStyleBackColor = true;
            this.checkSSDead.CheckedChanged += new System.EventHandler(this.checkDiscordDead_CheckedChanged);
            // 
            // checkStopDead
            // 
            this.checkStopDead.Checked = true;
            this.checkStopDead.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkStopDead.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkStopDead.FlatAppearance.BorderSize = 0;
            this.checkStopDead.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkStopDead.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkStopDead.ForeColor = System.Drawing.Color.MediumAquamarine;
            this.checkStopDead.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkStopDead.Location = new System.Drawing.Point(0, 0);
            this.checkStopDead.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkStopDead.Name = "checkStopDead";
            this.checkStopDead.Padding = new System.Windows.Forms.Padding(23, 0, 0, 0);
            this.checkStopDead.Size = new System.Drawing.Size(233, 31);
            this.checkStopDead.TabIndex = 2;
            this.checkStopDead.Text = "[Stop] When Dead";
            this.checkStopDead.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkStopDead.UseVisualStyleBackColor = true;
            this.checkStopDead.CheckedChanged += new System.EventHandler(this.checkStopDead_CheckedChanged);
            // 
            // panelCheckersDT
            // 
            this.panelCheckersDT.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheckersDT.Controls.Add(this.checkTarget);
            this.panelCheckersDT.Controls.Add(this.txtTarget);
            this.panelCheckersDT.Controls.Add(this.label12);
            this.panelCheckersDT.Controls.Add(this.label11);
            this.panelCheckersDT.Controls.Add(this.checkDeath);
            this.panelCheckersDT.Location = new System.Drawing.Point(16, 274);
            this.panelCheckersDT.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheckersDT.Name = "panelCheckersDT";
            this.panelCheckersDT.Size = new System.Drawing.Size(299, 149);
            this.panelCheckersDT.TabIndex = 5;
            // 
            // checkTarget
            // 
            this.checkTarget.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.checkTarget.FlatAppearance.BorderSize = 0;
            this.checkTarget.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkTarget.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkTarget.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkTarget.Image = global::Heth_AIO.Properties.Resources.target;
            this.checkTarget.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkTarget.Location = new System.Drawing.Point(0, 74);
            this.checkTarget.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkTarget.Name = "checkTarget";
            this.checkTarget.Padding = new System.Windows.Forms.Padding(23, 0, 23, 0);
            this.checkTarget.Size = new System.Drawing.Size(299, 33);
            this.checkTarget.TabIndex = 11;
            this.checkTarget.Text = "Target Checker";
            this.checkTarget.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkTarget.UseVisualStyleBackColor = true;
            this.checkTarget.CheckedChanged += new System.EventHandler(this.checkTarget_CheckedChanged);
            // 
            // txtTarget
            // 
            this.txtTarget.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtTarget.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtTarget.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.txtTarget.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtTarget.ForeColor = System.Drawing.Color.NavajoWhite;
            this.txtTarget.Location = new System.Drawing.Point(0, 107);
            this.txtTarget.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtTarget.Name = "txtTarget";
            this.txtTarget.Size = new System.Drawing.Size(299, 22);
            this.txtTarget.TabIndex = 10;
            this.txtTarget.Text = "High Class";
            this.txtTarget.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label12
            // 
            this.label12.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.label12.ForeColor = System.Drawing.Color.Silver;
            this.label12.Location = new System.Drawing.Point(0, 129);
            this.label12.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(299, 20);
            this.label12.TabIndex = 9;
            this.label12.Text = "Plays sound when you are not targeting the shiny monster";
            this.label12.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label11
            // 
            this.label11.Dock = System.Windows.Forms.DockStyle.Top;
            this.label11.ForeColor = System.Drawing.Color.Silver;
            this.label11.Location = new System.Drawing.Point(0, 33);
            this.label11.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(299, 20);
            this.label11.TabIndex = 8;
            this.label11.Text = "Plays sound when you are dead";
            this.label11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // checkDeath
            // 
            this.checkDeath.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkDeath.FlatAppearance.BorderSize = 0;
            this.checkDeath.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkDeath.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkDeath.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkDeath.Image = global::Heth_AIO.Properties.Resources.pulseRed;
            this.checkDeath.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkDeath.Location = new System.Drawing.Point(0, 0);
            this.checkDeath.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkDeath.Name = "checkDeath";
            this.checkDeath.Padding = new System.Windows.Forms.Padding(23, 0, 23, 0);
            this.checkDeath.Size = new System.Drawing.Size(299, 33);
            this.checkDeath.TabIndex = 2;
            this.checkDeath.Text = "Death Checker";
            this.checkDeath.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkDeath.UseVisualStyleBackColor = true;
            this.checkDeath.CheckedChanged += new System.EventHandler(this.checkDeath_CheckedChanged);
            // 
            // panelCheckerTimers
            // 
            this.panelCheckerTimers.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheckerTimers.Controls.Add(this.btnCheckerTimer);
            this.panelCheckerTimers.Controls.Add(this.lblTimer);
            this.panelCheckerTimers.Controls.Add(this.label13);
            this.panelCheckerTimers.Location = new System.Drawing.Point(238, 10);
            this.panelCheckerTimers.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheckerTimers.Name = "panelCheckerTimers";
            this.panelCheckerTimers.Size = new System.Drawing.Size(167, 79);
            this.panelCheckerTimers.TabIndex = 4;
            // 
            // btnCheckerTimer
            // 
            this.btnCheckerTimer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnCheckerTimer.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.btnCheckerTimer.FlatAppearance.BorderSize = 0;
            this.btnCheckerTimer.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCheckerTimer.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCheckerTimer.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(146)))), ((int)(((byte)(191)))), ((int)(((byte)(177)))));
            this.btnCheckerTimer.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnCheckerTimer.Location = new System.Drawing.Point(0, 46);
            this.btnCheckerTimer.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnCheckerTimer.Name = "btnCheckerTimer";
            this.btnCheckerTimer.Size = new System.Drawing.Size(167, 33);
            this.btnCheckerTimer.TabIndex = 40;
            this.btnCheckerTimer.Text = "Start";
            this.btnCheckerTimer.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btnCheckerTimer.UseVisualStyleBackColor = false;
            this.btnCheckerTimer.Click += new System.EventHandler(this.btnCheckerTimer_Click);
            // 
            // lblTimer
            // 
            this.lblTimer.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblTimer.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTimer.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            this.lblTimer.Location = new System.Drawing.Point(0, 20);
            this.lblTimer.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblTimer.Name = "lblTimer";
            this.lblTimer.Size = new System.Drawing.Size(167, 20);
            this.lblTimer.TabIndex = 39;
            this.lblTimer.Text = "0:0:0";
            this.lblTimer.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label13
            // 
            this.label13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(24)))), ((int)(((byte)(24)))));
            this.label13.Dock = System.Windows.Forms.DockStyle.Top;
            this.label13.ForeColor = System.Drawing.Color.Silver;
            this.label13.Location = new System.Drawing.Point(0, 0);
            this.label13.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(167, 20);
            this.label13.TabIndex = 9;
            this.label13.Text = "Alive Timer";
            this.label13.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // aliveTimer
            // 
            this.aliveTimer.Interval = 1000;
            this.aliveTimer.Tick += new System.EventHandler(this.aliveTimer_Tick);
            // 
            // panelCheats
            // 
            this.panelCheats.AutoSize = true;
            this.panelCheats.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.panelCheats.Controls.Add(this.panelBlockHide);
            this.panelCheats.Controls.Add(this.panelCheatsKnight);
            this.panelCheats.Controls.Add(this.panelCheatsStats);
            this.panelCheats.Controls.Add(this.panelCheatsHTML);
            this.panelCheats.Controls.Add(this.panelCheatTarget);
            this.panelCheats.Controls.Add(this.panelCheatsMisc);
            this.panelCheats.Controls.Add(this.panelCheatZoom);
            this.panelCheats.Location = new System.Drawing.Point(246, 96);
            this.panelCheats.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheats.Name = "panelCheats";
            this.panelCheats.Size = new System.Drawing.Size(610, 437);
            this.panelCheats.TabIndex = 6;
            // 
            // panelBlockHide
            // 
            this.panelBlockHide.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelBlockHide.Controls.Add(this.checkBlockHide);
            this.panelBlockHide.Controls.Add(this.checkHideAll);
            this.panelBlockHide.Controls.Add(this.checkBlockTeleport);
            this.panelBlockHide.Controls.Add(this.label50);
            this.panelBlockHide.Location = new System.Drawing.Point(41, 335);
            this.panelBlockHide.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelBlockHide.Name = "panelBlockHide";
            this.panelBlockHide.Size = new System.Drawing.Size(289, 91);
            this.panelBlockHide.TabIndex = 10;
            // 
            // checkBlockHide
            // 
            this.checkBlockHide.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkBlockHide.FlatAppearance.BorderSize = 0;
            this.checkBlockHide.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkBlockHide.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkBlockHide.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkBlockHide.Location = new System.Drawing.Point(0, 64);
            this.checkBlockHide.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkBlockHide.Name = "checkBlockHide";
            this.checkBlockHide.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkBlockHide.Size = new System.Drawing.Size(289, 22);
            this.checkBlockHide.TabIndex = 19;
            this.checkBlockHide.Text = "Block Teleport and Hide All";
            this.checkBlockHide.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkBlockHide.UseVisualStyleBackColor = true;
            this.checkBlockHide.CheckedChanged += new System.EventHandler(this.checkBlockHide_CheckedChanged);
            // 
            // checkHideAll
            // 
            this.checkHideAll.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkHideAll.FlatAppearance.BorderSize = 0;
            this.checkHideAll.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkHideAll.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkHideAll.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkHideAll.Location = new System.Drawing.Point(0, 42);
            this.checkHideAll.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkHideAll.Name = "checkHideAll";
            this.checkHideAll.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkHideAll.Size = new System.Drawing.Size(289, 22);
            this.checkHideAll.TabIndex = 18;
            this.checkHideAll.Text = "Hide Mobs,Npcs and Ppl";
            this.checkHideAll.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkHideAll.UseVisualStyleBackColor = true;
            this.checkHideAll.CheckedChanged += new System.EventHandler(this.checkHideAll_CheckedChanged);
            // 
            // checkBlockTeleport
            // 
            this.checkBlockTeleport.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkBlockTeleport.FlatAppearance.BorderSize = 0;
            this.checkBlockTeleport.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkBlockTeleport.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkBlockTeleport.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkBlockTeleport.Location = new System.Drawing.Point(0, 20);
            this.checkBlockTeleport.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkBlockTeleport.Name = "checkBlockTeleport";
            this.checkBlockTeleport.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkBlockTeleport.Size = new System.Drawing.Size(289, 22);
            this.checkBlockTeleport.TabIndex = 17;
            this.checkBlockTeleport.Text = "Block Teleports";
            this.checkBlockTeleport.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkBlockTeleport.UseVisualStyleBackColor = true;
            this.checkBlockTeleport.CheckedChanged += new System.EventHandler(this.checkBlockTeleport_CheckedChanged);
            // 
            // label50
            // 
            this.label50.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label50.Dock = System.Windows.Forms.DockStyle.Top;
            this.label50.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label50.ForeColor = System.Drawing.Color.Silver;
            this.label50.Location = new System.Drawing.Point(0, 0);
            this.label50.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label50.Name = "label50";
            this.label50.Size = new System.Drawing.Size(289, 20);
            this.label50.TabIndex = 13;
            this.label50.Text = "Blocker and Hider";
            this.label50.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelCheatsKnight
            // 
            this.panelCheatsKnight.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheatsKnight.Controls.Add(this.checkPWW2nd);
            this.panelCheatsKnight.Controls.Add(this.checkPWW3rd);
            this.panelCheatsKnight.Controls.Add(this.label17);
            this.panelCheatsKnight.Location = new System.Drawing.Point(397, 351);
            this.panelCheatsKnight.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheatsKnight.Name = "panelCheatsKnight";
            this.panelCheatsKnight.Size = new System.Drawing.Size(202, 74);
            this.panelCheatsKnight.TabIndex = 9;
            // 
            // checkPWW2nd
            // 
            this.checkPWW2nd.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkPWW2nd.FlatAppearance.BorderSize = 0;
            this.checkPWW2nd.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkPWW2nd.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkPWW2nd.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkPWW2nd.Location = new System.Drawing.Point(0, 42);
            this.checkPWW2nd.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkPWW2nd.Name = "checkPWW2nd";
            this.checkPWW2nd.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkPWW2nd.Size = new System.Drawing.Size(202, 22);
            this.checkPWW2nd.TabIndex = 18;
            this.checkPWW2nd.Text = "Skip to 2nd combo";
            this.checkPWW2nd.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkPWW2nd.UseVisualStyleBackColor = true;
            this.checkPWW2nd.CheckedChanged += new System.EventHandler(this.checkPWW2nd_CheckedChanged);
            // 
            // checkPWW3rd
            // 
            this.checkPWW3rd.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkPWW3rd.FlatAppearance.BorderSize = 0;
            this.checkPWW3rd.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkPWW3rd.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkPWW3rd.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkPWW3rd.Location = new System.Drawing.Point(0, 20);
            this.checkPWW3rd.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkPWW3rd.Name = "checkPWW3rd";
            this.checkPWW3rd.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkPWW3rd.Size = new System.Drawing.Size(202, 22);
            this.checkPWW3rd.TabIndex = 17;
            this.checkPWW3rd.Text = "Skip to 3rd combo";
            this.checkPWW3rd.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkPWW3rd.UseVisualStyleBackColor = true;
            this.checkPWW3rd.CheckedChanged += new System.EventHandler(this.checkPWW3rd_CheckedChanged);
            // 
            // label17
            // 
            this.label17.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label17.Dock = System.Windows.Forms.DockStyle.Top;
            this.label17.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label17.ForeColor = System.Drawing.Color.Silver;
            this.label17.Location = new System.Drawing.Point(0, 0);
            this.label17.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(202, 20);
            this.label17.TabIndex = 13;
            this.label17.Text = "Knight Skills";
            this.label17.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelCheatsStats
            // 
            this.panelCheatsStats.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheatsStats.Controls.Add(this.lblCurPoints);
            this.panelCheatsStats.Controls.Add(this.panelAddStats);
            this.panelCheatsStats.Controls.Add(this.btnStatsAdd);
            this.panelCheatsStats.Controls.Add(this.panelCurStats);
            this.panelCheatsStats.Controls.Add(this.label29);
            this.panelCheatsStats.Location = new System.Drawing.Point(41, 98);
            this.panelCheatsStats.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheatsStats.Name = "panelCheatsStats";
            this.panelCheatsStats.Size = new System.Drawing.Size(289, 231);
            this.panelCheatsStats.TabIndex = 8;
            // 
            // lblCurPoints
            // 
            this.lblCurPoints.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblCurPoints.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCurPoints.ForeColor = System.Drawing.Color.Silver;
            this.lblCurPoints.Location = new System.Drawing.Point(0, 20);
            this.lblCurPoints.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblCurPoints.Name = "lblCurPoints";
            this.lblCurPoints.Size = new System.Drawing.Size(289, 14);
            this.lblCurPoints.TabIndex = 17;
            this.lblCurPoints.Text = "Points: 0";
            this.lblCurPoints.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelAddStats
            // 
            this.panelAddStats.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.panelAddStats.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panelAddStats.Controls.Add(this.label31);
            this.panelAddStats.Controls.Add(this.agiNBox);
            this.panelAddStats.Controls.Add(this.label30);
            this.panelAddStats.Controls.Add(this.wisNBox);
            this.panelAddStats.Controls.Add(this.label28);
            this.panelAddStats.Controls.Add(this.intNBox);
            this.panelAddStats.Controls.Add(this.label21);
            this.panelAddStats.Controls.Add(this.hthNBox);
            this.panelAddStats.Controls.Add(this.label18);
            this.panelAddStats.Controls.Add(this.strNBox);
            this.panelAddStats.Controls.Add(this.label32);
            this.panelAddStats.Location = new System.Drawing.Point(149, 45);
            this.panelAddStats.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelAddStats.Name = "panelAddStats";
            this.panelAddStats.Size = new System.Drawing.Size(130, 143);
            this.panelAddStats.TabIndex = 16;
            // 
            // label31
            // 
            this.label31.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label31.ForeColor = System.Drawing.Color.Silver;
            this.label31.Location = new System.Drawing.Point(-1, 114);
            this.label31.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(56, 21);
            this.label31.TabIndex = 69;
            this.label31.Text = "AGI:";
            this.label31.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // agiNBox
            // 
            this.agiNBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(15)))), ((int)(((byte)(15)))));
            this.agiNBox.ForeColor = System.Drawing.SystemColors.Menu;
            this.agiNBox.Location = new System.Drawing.Point(56, 116);
            this.agiNBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.agiNBox.Maximum = new decimal(new int[] {
            99999999,
            0,
            0,
            0});
            this.agiNBox.Name = "agiNBox";
            this.agiNBox.Size = new System.Drawing.Size(68, 21);
            this.agiNBox.TabIndex = 68;
            this.agiNBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label30
            // 
            this.label30.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label30.ForeColor = System.Drawing.Color.Silver;
            this.label30.Location = new System.Drawing.Point(-1, 90);
            this.label30.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(56, 21);
            this.label30.TabIndex = 67;
            this.label30.Text = "WIS:";
            this.label30.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // wisNBox
            // 
            this.wisNBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(15)))), ((int)(((byte)(15)))));
            this.wisNBox.ForeColor = System.Drawing.SystemColors.Menu;
            this.wisNBox.Location = new System.Drawing.Point(56, 92);
            this.wisNBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.wisNBox.Maximum = new decimal(new int[] {
            99999999,
            0,
            0,
            0});
            this.wisNBox.Name = "wisNBox";
            this.wisNBox.Size = new System.Drawing.Size(68, 21);
            this.wisNBox.TabIndex = 66;
            this.wisNBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label28
            // 
            this.label28.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label28.ForeColor = System.Drawing.Color.Silver;
            this.label28.Location = new System.Drawing.Point(-2, 67);
            this.label28.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(56, 21);
            this.label28.TabIndex = 65;
            this.label28.Text = "INT:";
            this.label28.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // intNBox
            // 
            this.intNBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(15)))), ((int)(((byte)(15)))));
            this.intNBox.ForeColor = System.Drawing.SystemColors.Menu;
            this.intNBox.Location = new System.Drawing.Point(56, 69);
            this.intNBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.intNBox.Maximum = new decimal(new int[] {
            99999999,
            0,
            0,
            0});
            this.intNBox.Name = "intNBox";
            this.intNBox.Size = new System.Drawing.Size(68, 21);
            this.intNBox.TabIndex = 64;
            this.intNBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label21
            // 
            this.label21.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label21.ForeColor = System.Drawing.Color.Silver;
            this.label21.Location = new System.Drawing.Point(-2, 44);
            this.label21.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(56, 21);
            this.label21.TabIndex = 63;
            this.label21.Text = "HP:";
            this.label21.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // hthNBox
            // 
            this.hthNBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(15)))), ((int)(((byte)(15)))));
            this.hthNBox.ForeColor = System.Drawing.SystemColors.Menu;
            this.hthNBox.Location = new System.Drawing.Point(56, 46);
            this.hthNBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.hthNBox.Maximum = new decimal(new int[] {
            99999999,
            0,
            0,
            0});
            this.hthNBox.Name = "hthNBox";
            this.hthNBox.Size = new System.Drawing.Size(68, 21);
            this.hthNBox.TabIndex = 62;
            this.hthNBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label18
            // 
            this.label18.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label18.ForeColor = System.Drawing.Color.Silver;
            this.label18.Location = new System.Drawing.Point(-2, 21);
            this.label18.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(56, 21);
            this.label18.TabIndex = 61;
            this.label18.Text = "STR:";
            this.label18.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // strNBox
            // 
            this.strNBox.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(15)))), ((int)(((byte)(15)))));
            this.strNBox.ForeColor = System.Drawing.SystemColors.Menu;
            this.strNBox.Location = new System.Drawing.Point(55, 23);
            this.strNBox.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.strNBox.Maximum = new decimal(new int[] {
            99999999,
            0,
            0,
            0});
            this.strNBox.Name = "strNBox";
            this.strNBox.Size = new System.Drawing.Size(68, 21);
            this.strNBox.TabIndex = 60;
            this.strNBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label32
            // 
            this.label32.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.label32.Dock = System.Windows.Forms.DockStyle.Top;
            this.label32.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label32.ForeColor = System.Drawing.Color.Silver;
            this.label32.Location = new System.Drawing.Point(0, 0);
            this.label32.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(128, 21);
            this.label32.TabIndex = 7;
            this.label32.Text = "Add Stats";
            this.label32.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnStatsAdd
            // 
            this.btnStatsAdd.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnStatsAdd.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.btnStatsAdd.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.btnStatsAdd.FlatAppearance.BorderSize = 0;
            this.btnStatsAdd.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnStatsAdd.Font = new System.Drawing.Font("Verdana", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnStatsAdd.ForeColor = System.Drawing.Color.Silver;
            this.btnStatsAdd.Image = global::Heth_AIO.Properties.Resources.disk;
            this.btnStatsAdd.Location = new System.Drawing.Point(0, 196);
            this.btnStatsAdd.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnStatsAdd.Name = "btnStatsAdd";
            this.btnStatsAdd.Padding = new System.Windows.Forms.Padding(13, 0, 0, 0);
            this.btnStatsAdd.Size = new System.Drawing.Size(289, 35);
            this.btnStatsAdd.TabIndex = 15;
            this.btnStatsAdd.Text = "         ADD";
            this.btnStatsAdd.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnStatsAdd.UseVisualStyleBackColor = false;
            this.btnStatsAdd.Click += new System.EventHandler(this.btnStatsAdd_Click);
            // 
            // panelCurStats
            // 
            this.panelCurStats.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.panelCurStats.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panelCurStats.Controls.Add(this.lblCurAgi);
            this.panelCurStats.Controls.Add(this.lblCurWis);
            this.panelCurStats.Controls.Add(this.lblCurInt);
            this.panelCurStats.Controls.Add(this.lblCurHP);
            this.panelCurStats.Controls.Add(this.lblCurStr);
            this.panelCurStats.Controls.Add(this.label8);
            this.panelCurStats.Location = new System.Drawing.Point(13, 46);
            this.panelCurStats.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCurStats.Name = "panelCurStats";
            this.panelCurStats.Size = new System.Drawing.Size(126, 143);
            this.panelCurStats.TabIndex = 13;
            // 
            // lblCurAgi
            // 
            this.lblCurAgi.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblCurAgi.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCurAgi.ForeColor = System.Drawing.Color.Silver;
            this.lblCurAgi.Location = new System.Drawing.Point(0, 105);
            this.lblCurAgi.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblCurAgi.Name = "lblCurAgi";
            this.lblCurAgi.Size = new System.Drawing.Size(124, 21);
            this.lblCurAgi.TabIndex = 12;
            this.lblCurAgi.Text = "AGI: 0";
            this.lblCurAgi.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblCurWis
            // 
            this.lblCurWis.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblCurWis.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCurWis.ForeColor = System.Drawing.Color.Silver;
            this.lblCurWis.Location = new System.Drawing.Point(0, 84);
            this.lblCurWis.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblCurWis.Name = "lblCurWis";
            this.lblCurWis.Size = new System.Drawing.Size(124, 21);
            this.lblCurWis.TabIndex = 11;
            this.lblCurWis.Text = "WIS: 0";
            this.lblCurWis.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblCurInt
            // 
            this.lblCurInt.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblCurInt.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCurInt.ForeColor = System.Drawing.Color.Silver;
            this.lblCurInt.Location = new System.Drawing.Point(0, 63);
            this.lblCurInt.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblCurInt.Name = "lblCurInt";
            this.lblCurInt.Size = new System.Drawing.Size(124, 21);
            this.lblCurInt.TabIndex = 10;
            this.lblCurInt.Text = "INT: 0";
            this.lblCurInt.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblCurHP
            // 
            this.lblCurHP.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblCurHP.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCurHP.ForeColor = System.Drawing.Color.Silver;
            this.lblCurHP.Location = new System.Drawing.Point(0, 42);
            this.lblCurHP.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblCurHP.Name = "lblCurHP";
            this.lblCurHP.Size = new System.Drawing.Size(124, 21);
            this.lblCurHP.TabIndex = 9;
            this.lblCurHP.Text = "Health: 0";
            this.lblCurHP.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblCurStr
            // 
            this.lblCurStr.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblCurStr.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCurStr.ForeColor = System.Drawing.Color.Silver;
            this.lblCurStr.Location = new System.Drawing.Point(0, 21);
            this.lblCurStr.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblCurStr.Name = "lblCurStr";
            this.lblCurStr.Size = new System.Drawing.Size(124, 21);
            this.lblCurStr.TabIndex = 8;
            this.lblCurStr.Text = "STR: 0";
            this.lblCurStr.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label8
            // 
            this.label8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.label8.Dock = System.Windows.Forms.DockStyle.Top;
            this.label8.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.ForeColor = System.Drawing.Color.Silver;
            this.label8.Location = new System.Drawing.Point(0, 0);
            this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(124, 21);
            this.label8.TabIndex = 7;
            this.label8.Text = "Current Stats";
            this.label8.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label29
            // 
            this.label29.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label29.Dock = System.Windows.Forms.DockStyle.Top;
            this.label29.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label29.ForeColor = System.Drawing.Color.Silver;
            this.label29.Location = new System.Drawing.Point(0, 0);
            this.label29.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(289, 20);
            this.label29.TabIndex = 12;
            this.label29.Text = "Add Stats (add one point ingame first)";
            this.label29.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelCheatsHTML
            // 
            this.panelCheatsHTML.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheatsHTML.Controls.Add(this.txtOpenHTML);
            this.panelCheatsHTML.Controls.Add(this.label14);
            this.panelCheatsHTML.Controls.Add(this.btnOpenHTML);
            this.panelCheatsHTML.Location = new System.Drawing.Point(18, 17);
            this.panelCheatsHTML.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheatsHTML.Name = "panelCheatsHTML";
            this.panelCheatsHTML.Size = new System.Drawing.Size(167, 74);
            this.panelCheatsHTML.TabIndex = 7;
            // 
            // txtOpenHTML
            // 
            this.txtOpenHTML.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.txtOpenHTML.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtOpenHTML.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.txtOpenHTML.Font = new System.Drawing.Font("Verdana", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtOpenHTML.ForeColor = System.Drawing.Color.Silver;
            this.txtOpenHTML.Location = new System.Drawing.Point(0, 25);
            this.txtOpenHTML.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtOpenHTML.Name = "txtOpenHTML";
            this.txtOpenHTML.Size = new System.Drawing.Size(167, 24);
            this.txtOpenHTML.TabIndex = 11;
            this.txtOpenHTML.Text = "s100005";
            this.txtOpenHTML.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label14
            // 
            this.label14.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label14.Dock = System.Windows.Forms.DockStyle.Top;
            this.label14.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label14.ForeColor = System.Drawing.Color.Silver;
            this.label14.Location = new System.Drawing.Point(0, 0);
            this.label14.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(167, 25);
            this.label14.TabIndex = 10;
            this.label14.Text = "Open Ingame Window";
            this.label14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnOpenHTML
            // 
            this.btnOpenHTML.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnOpenHTML.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.btnOpenHTML.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.btnOpenHTML.FlatAppearance.BorderSize = 0;
            this.btnOpenHTML.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnOpenHTML.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOpenHTML.ForeColor = System.Drawing.Color.Silver;
            this.btnOpenHTML.Location = new System.Drawing.Point(0, 49);
            this.btnOpenHTML.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnOpenHTML.Name = "btnOpenHTML";
            this.btnOpenHTML.Size = new System.Drawing.Size(167, 25);
            this.btnOpenHTML.TabIndex = 16;
            this.btnOpenHTML.Text = "Open";
            this.btnOpenHTML.UseVisualStyleBackColor = false;
            this.btnOpenHTML.Click += new System.EventHandler(this.btnOpenHTML_Click);
            // 
            // panelCheatTarget
            // 
            this.panelCheatTarget.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheatTarget.Controls.Add(this.checkTabTarget);
            this.panelCheatTarget.Controls.Add(this.label48);
            this.panelCheatTarget.Controls.Add(this.checkLockTarget);
            this.panelCheatTarget.Controls.Add(this.label25);
            this.panelCheatTarget.Controls.Add(this.checkTargetInfoToSys);
            this.panelCheatTarget.Controls.Add(this.checkTargetOverlay);
            this.panelCheatTarget.Controls.Add(this.label24);
            this.panelCheatTarget.Controls.Add(this.comboTargetRange);
            this.panelCheatTarget.Controls.Add(this.label23);
            this.panelCheatTarget.Controls.Add(this.label20);
            this.panelCheatTarget.Location = new System.Drawing.Point(397, 17);
            this.panelCheatTarget.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheatTarget.Name = "panelCheatTarget";
            this.panelCheatTarget.Size = new System.Drawing.Size(202, 237);
            this.panelCheatTarget.TabIndex = 6;
            // 
            // checkTabTarget
            // 
            this.checkTabTarget.Checked = true;
            this.checkTabTarget.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkTabTarget.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkTabTarget.FlatAppearance.BorderSize = 0;
            this.checkTabTarget.Font = new System.Drawing.Font("Verdana", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkTabTarget.ForeColor = System.Drawing.Color.MediumAquamarine;
            this.checkTabTarget.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkTabTarget.Location = new System.Drawing.Point(0, 207);
            this.checkTabTarget.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkTabTarget.Name = "checkTabTarget";
            this.checkTabTarget.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkTabTarget.Size = new System.Drawing.Size(202, 22);
            this.checkTabTarget.TabIndex = 20;
            this.checkTabTarget.Text = "Enable TAB Target";
            this.checkTabTarget.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkTabTarget.UseVisualStyleBackColor = true;
            this.checkTabTarget.CheckedChanged += new System.EventHandler(this.checkBox1_CheckedChanged);
            // 
            // label48
            // 
            this.label48.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label48.Dock = System.Windows.Forms.DockStyle.Top;
            this.label48.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label48.ForeColor = System.Drawing.Color.Silver;
            this.label48.Location = new System.Drawing.Point(0, 191);
            this.label48.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label48.Name = "label48";
            this.label48.Size = new System.Drawing.Size(202, 16);
            this.label48.TabIndex = 21;
            this.label48.Text = "TAB Target";
            this.label48.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // checkLockTarget
            // 
            this.checkLockTarget.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkLockTarget.FlatAppearance.BorderSize = 0;
            this.checkLockTarget.Font = new System.Drawing.Font("Verdana", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkLockTarget.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkLockTarget.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkLockTarget.Location = new System.Drawing.Point(0, 169);
            this.checkLockTarget.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkLockTarget.Name = "checkLockTarget";
            this.checkLockTarget.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkLockTarget.Size = new System.Drawing.Size(202, 22);
            this.checkLockTarget.TabIndex = 18;
            this.checkLockTarget.Text = "Lock Target";
            this.checkLockTarget.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkLockTarget.UseVisualStyleBackColor = true;
            this.checkLockTarget.CheckedChanged += new System.EventHandler(this.CheckLockTarget_CheckedChanged);
            // 
            // label25
            // 
            this.label25.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label25.Dock = System.Windows.Forms.DockStyle.Top;
            this.label25.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label25.ForeColor = System.Drawing.Color.Silver;
            this.label25.Location = new System.Drawing.Point(0, 153);
            this.label25.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(202, 16);
            this.label25.TabIndex = 17;
            this.label25.Text = "Target Lock";
            this.label25.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // checkTargetInfoToSys
            // 
            this.checkTargetInfoToSys.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkTargetInfoToSys.FlatAppearance.BorderSize = 0;
            this.checkTargetInfoToSys.Font = new System.Drawing.Font("Verdana", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkTargetInfoToSys.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkTargetInfoToSys.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkTargetInfoToSys.Location = new System.Drawing.Point(0, 131);
            this.checkTargetInfoToSys.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkTargetInfoToSys.Name = "checkTargetInfoToSys";
            this.checkTargetInfoToSys.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkTargetInfoToSys.Size = new System.Drawing.Size(202, 22);
            this.checkTargetInfoToSys.TabIndex = 19;
            this.checkTargetInfoToSys.Text = "Send to System Chat";
            this.checkTargetInfoToSys.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkTargetInfoToSys.UseVisualStyleBackColor = true;
            this.checkTargetInfoToSys.CheckedChanged += new System.EventHandler(this.checkChangeVersion_CheckedChanged);
            // 
            // checkTargetOverlay
            // 
            this.checkTargetOverlay.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkTargetOverlay.FlatAppearance.BorderSize = 0;
            this.checkTargetOverlay.Font = new System.Drawing.Font("Verdana", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkTargetOverlay.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkTargetOverlay.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkTargetOverlay.Location = new System.Drawing.Point(0, 109);
            this.checkTargetOverlay.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkTargetOverlay.Name = "checkTargetOverlay";
            this.checkTargetOverlay.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkTargetOverlay.Size = new System.Drawing.Size(202, 22);
            this.checkTargetOverlay.TabIndex = 16;
            this.checkTargetOverlay.Text = "Ingame Overlay";
            this.checkTargetOverlay.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkTargetOverlay.UseVisualStyleBackColor = true;
            this.checkTargetOverlay.CheckedChanged += new System.EventHandler(this.checkTargetOverlay_CheckedChanged);
            // 
            // label24
            // 
            this.label24.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label24.Dock = System.Windows.Forms.DockStyle.Top;
            this.label24.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label24.ForeColor = System.Drawing.Color.Silver;
            this.label24.Location = new System.Drawing.Point(0, 79);
            this.label24.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(202, 30);
            this.label24.TabIndex = 15;
            this.label24.Text = "Target Info";
            this.label24.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // comboTargetRange
            // 
            this.comboTargetRange.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(18)))), ((int)(((byte)(18)))));
            this.comboTargetRange.Dock = System.Windows.Forms.DockStyle.Top;
            this.comboTargetRange.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboTargetRange.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.comboTargetRange.Font = new System.Drawing.Font("Verdana", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.comboTargetRange.ForeColor = System.Drawing.Color.Silver;
            this.comboTargetRange.FormattingEnabled = true;
            this.comboTargetRange.Items.AddRange(new object[] {
            "Default",
            "Low Range",
            "Medium Range",
            "Far",
            "Very Far",
            "Extremely Far(not recommended)"});
            this.comboTargetRange.Location = new System.Drawing.Point(0, 55);
            this.comboTargetRange.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.comboTargetRange.Name = "comboTargetRange";
            this.comboTargetRange.Size = new System.Drawing.Size(202, 24);
            this.comboTargetRange.TabIndex = 13;
            this.comboTargetRange.SelectedIndexChanged += new System.EventHandler(this.comboTargetRange_SelectedIndexChanged);
            // 
            // label23
            // 
            this.label23.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.label23.Dock = System.Windows.Forms.DockStyle.Top;
            this.label23.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label23.ForeColor = System.Drawing.Color.Silver;
            this.label23.Location = new System.Drawing.Point(0, 20);
            this.label23.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(202, 35);
            this.label23.TabIndex = 14;
            this.label23.Text = "Tap-Target Range";
            this.label23.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label20
            // 
            this.label20.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label20.Dock = System.Windows.Forms.DockStyle.Top;
            this.label20.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label20.ForeColor = System.Drawing.Color.Silver;
            this.label20.Location = new System.Drawing.Point(0, 0);
            this.label20.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(202, 20);
            this.label20.TabIndex = 12;
            this.label20.Text = "Target Stuff";
            this.label20.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelCheatsMisc
            // 
            this.panelCheatsMisc.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheatsMisc.Controls.Add(this.checkTest);
            this.panelCheatsMisc.Controls.Add(this.btnSaveQuickslot);
            this.panelCheatsMisc.Controls.Add(this.checkInifityRush);
            this.panelCheatsMisc.Controls.Add(this.checkAttackSpeed);
            this.panelCheatsMisc.Controls.Add(this.label19);
            this.panelCheatsMisc.Location = new System.Drawing.Point(397, 275);
            this.panelCheatsMisc.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheatsMisc.Name = "panelCheatsMisc";
            this.panelCheatsMisc.Size = new System.Drawing.Size(202, 64);
            this.panelCheatsMisc.TabIndex = 5;
            // 
            // checkTest
            // 
            this.checkTest.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkTest.FlatAppearance.BorderSize = 0;
            this.checkTest.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkTest.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkTest.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkTest.Location = new System.Drawing.Point(0, 64);
            this.checkTest.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkTest.Name = "checkTest";
            this.checkTest.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkTest.Size = new System.Drawing.Size(202, 22);
            this.checkTest.TabIndex = 20;
            this.checkTest.Text = "Testing";
            this.checkTest.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkTest.UseVisualStyleBackColor = true;
            this.checkTest.Visible = false;
            this.checkTest.CheckedChanged += new System.EventHandler(this.checkTest_CheckedChanged);
            // 
            // btnSaveQuickslot
            // 
            this.btnSaveQuickslot.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.btnSaveQuickslot.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.btnSaveQuickslot.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.btnSaveQuickslot.FlatAppearance.BorderSize = 0;
            this.btnSaveQuickslot.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSaveQuickslot.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSaveQuickslot.ForeColor = System.Drawing.Color.MediumSeaGreen;
            this.btnSaveQuickslot.Location = new System.Drawing.Point(0, 39);
            this.btnSaveQuickslot.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnSaveQuickslot.Name = "btnSaveQuickslot";
            this.btnSaveQuickslot.Size = new System.Drawing.Size(202, 25);
            this.btnSaveQuickslot.TabIndex = 19;
            this.btnSaveQuickslot.Text = "Save SkillBar To Server";
            this.btnSaveQuickslot.UseVisualStyleBackColor = false;
            this.btnSaveQuickslot.Click += new System.EventHandler(this.btnSaveQuickslot_Click);
            // 
            // checkInifityRush
            // 
            this.checkInifityRush.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkInifityRush.FlatAppearance.BorderSize = 0;
            this.checkInifityRush.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkInifityRush.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkInifityRush.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkInifityRush.Location = new System.Drawing.Point(0, 42);
            this.checkInifityRush.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkInifityRush.Name = "checkInifityRush";
            this.checkInifityRush.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkInifityRush.Size = new System.Drawing.Size(202, 22);
            this.checkInifityRush.TabIndex = 18;
            this.checkInifityRush.Text = "Infinite Rush Time";
            this.checkInifityRush.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkInifityRush.UseVisualStyleBackColor = true;
            this.checkInifityRush.Visible = false;
            this.checkInifityRush.CheckedChanged += new System.EventHandler(this.checkInifityRush_CheckedChanged);
            // 
            // checkAttackSpeed
            // 
            this.checkAttackSpeed.Dock = System.Windows.Forms.DockStyle.Top;
            this.checkAttackSpeed.FlatAppearance.BorderSize = 0;
            this.checkAttackSpeed.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkAttackSpeed.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(206)))), ((int)(((byte)(45)))), ((int)(((byte)(79)))));
            this.checkAttackSpeed.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.checkAttackSpeed.Location = new System.Drawing.Point(0, 20);
            this.checkAttackSpeed.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.checkAttackSpeed.Name = "checkAttackSpeed";
            this.checkAttackSpeed.Padding = new System.Windows.Forms.Padding(12, 0, 12, 0);
            this.checkAttackSpeed.Size = new System.Drawing.Size(202, 22);
            this.checkAttackSpeed.TabIndex = 17;
            this.checkAttackSpeed.Text = "[New] Attack Speed";
            this.checkAttackSpeed.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.checkAttackSpeed.UseVisualStyleBackColor = true;
            this.checkAttackSpeed.CheckedChanged += new System.EventHandler(this.checkAttackSpeed_CheckedChanged);
            // 
            // label19
            // 
            this.label19.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label19.Dock = System.Windows.Forms.DockStyle.Top;
            this.label19.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label19.ForeColor = System.Drawing.Color.Silver;
            this.label19.Location = new System.Drawing.Point(0, 0);
            this.label19.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(202, 20);
            this.label19.TabIndex = 13;
            this.label19.Text = "Other Cheats";
            this.label19.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelCheatZoom
            // 
            this.panelCheatZoom.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(28)))), ((int)(((byte)(28)))));
            this.panelCheatZoom.Controls.Add(this.lblCurZoom);
            this.panelCheatZoom.Controls.Add(this.numZoom);
            this.panelCheatZoom.Controls.Add(this.label16);
            this.panelCheatZoom.Location = new System.Drawing.Point(189, 17);
            this.panelCheatZoom.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelCheatZoom.Name = "panelCheatZoom";
            this.panelCheatZoom.Size = new System.Drawing.Size(167, 74);
            this.panelCheatZoom.TabIndex = 4;
            // 
            // lblCurZoom
            // 
            this.lblCurZoom.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblCurZoom.ForeColor = System.Drawing.Color.Silver;
            this.lblCurZoom.Location = new System.Drawing.Point(0, 46);
            this.lblCurZoom.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.lblCurZoom.Name = "lblCurZoom";
            this.lblCurZoom.Size = new System.Drawing.Size(167, 29);
            this.lblCurZoom.TabIndex = 60;
            this.lblCurZoom.Text = "Current Zoom: 0";
            this.lblCurZoom.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // numZoom
            // 
            this.numZoom.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(15)))), ((int)(((byte)(15)))));
            this.numZoom.Dock = System.Windows.Forms.DockStyle.Top;
            this.numZoom.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numZoom.ForeColor = System.Drawing.SystemColors.Menu;
            this.numZoom.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numZoom.Location = new System.Drawing.Point(0, 25);
            this.numZoom.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.numZoom.Maximum = new decimal(new int[] {
            99999999,
            0,
            0,
            0});
            this.numZoom.Name = "numZoom";
            this.numZoom.Size = new System.Drawing.Size(167, 21);
            this.numZoom.TabIndex = 59;
            this.numZoom.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numZoom.ValueChanged += new System.EventHandler(this.numZoom_ValueChanged);
            // 
            // label16
            // 
            this.label16.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.label16.Dock = System.Windows.Forms.DockStyle.Top;
            this.label16.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label16.ForeColor = System.Drawing.Color.Silver;
            this.label16.Location = new System.Drawing.Point(0, 0);
            this.label16.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(167, 25);
            this.label16.TabIndex = 10;
            this.label16.Text = "Zoom Hack";
            this.label16.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panelTitle
            // 
            this.panelTitle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(23)))), ((int)(((byte)(23)))), ((int)(((byte)(23)))));
            this.panelTitle.Controls.Add(this.lblUpdateLink);
            this.panelTitle.Controls.Add(this.txtConsole);
            this.panelTitle.Controls.Add(this.lblTitle);
            this.panelTitle.Location = new System.Drawing.Point(246, 29);
            this.panelTitle.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelTitle.Name = "panelTitle";
            this.panelTitle.Size = new System.Drawing.Size(610, 67);
            this.panelTitle.TabIndex = 7;
            // 
            // lblUpdateLink
            // 
            this.lblUpdateLink.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.lblUpdateLink.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblUpdateLink.ForeColor = System.Drawing.Color.MediumSeaGreen;
            this.lblUpdateLink.Location = new System.Drawing.Point(0, 1);
            this.lblUpdateLink.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblUpdateLink.Name = "lblUpdateLink";
            this.lblUpdateLink.Size = new System.Drawing.Size(330, 15);
            this.lblUpdateLink.TabIndex = 3;
            this.lblUpdateLink.Text = "Update Available ! Click here to download";
            this.lblUpdateLink.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblUpdateLink.Visible = false;
            this.lblUpdateLink.Click += new System.EventHandler(this.lblUpdateLink_Click);
            // 
            // txtConsole
            // 
            this.txtConsole.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(21)))), ((int)(((byte)(21)))), ((int)(((byte)(21)))));
            this.txtConsole.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtConsole.Dock = System.Windows.Forms.DockStyle.Right;
            this.txtConsole.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtConsole.ForeColor = System.Drawing.Color.Silver;
            this.txtConsole.HideSelection = false;
            this.txtConsole.Location = new System.Drawing.Point(355, 0);
            this.txtConsole.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtConsole.MaxLength = 327679999;
            this.txtConsole.Multiline = true;
            this.txtConsole.Name = "txtConsole";
            this.txtConsole.ReadOnly = true;
            this.txtConsole.Size = new System.Drawing.Size(255, 67);
            this.txtConsole.TabIndex = 2;
            this.txtConsole.Text = "[Heth AIO v1.2]\r\nConsole";
            this.txtConsole.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // lblTitle
            // 
            this.lblTitle.Dock = System.Windows.Forms.DockStyle.Left;
            this.lblTitle.Font = new System.Drawing.Font("Verdana", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTitle.ForeColor = System.Drawing.Color.Silver;
            this.lblTitle.Location = new System.Drawing.Point(0, 0);
            this.lblTitle.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(330, 67);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "Inject Please ..";
            this.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // DataTimer
            // 
            this.DataTimer.Interval = 500;
            this.DataTimer.Tick += new System.EventHandler(this.updateUIDataTimer_Tick);
            // 
            // workerKeyboardMonitor
            // 
            this.workerKeyboardMonitor.DoWork += new System.ComponentModel.DoWorkEventHandler(this.workerKeyboardMonitor_DoWork);
            // 
            // PauseCheckTimer
            // 
            this.PauseCheckTimer.Interval = 1000;
            this.PauseCheckTimer.Tick += new System.EventHandler(this.PauseCheckTimer_Tick);
            // 
            // MainApp
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(49)))), ((int)(((byte)(49)))), ((int)(((byte)(49)))));
            this.ClientSize = new System.Drawing.Size(856, 534);
            this.ControlBox = false;
            this.Controls.Add(this.panelTitle);
            this.Controls.Add(this.panelControl);
            this.Controls.Add(this.panelMenu);
            this.Controls.Add(this.panelMacro);
            this.Controls.Add(this.panelCheckers);
            this.Controls.Add(this.panelCheats);
            this.Controls.Add(this.panelAuto);
            this.Controls.Add(this.panelInject);
            this.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Name = "MainApp";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Heth\'s AIO";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Form1_FormClosing);
            this.Load += new System.EventHandler(this.Form1_Load);
            this.panelMenu.ResumeLayout(false);
            this.panelStart.ResumeLayout(false);
            this.panelStart.PerformLayout();
            this.panelControl.ResumeLayout(false);
            this.panelMacro.ResumeLayout(false);
            this.panelMacroBox.ResumeLayout(false);
            this.panelMacroCtrl.ResumeLayout(false);
            this.panelMacroMisc.ResumeLayout(false);
            this.panelMacroPresets.ResumeLayout(false);
            this.panelMacroPresets.PerformLayout();
            this.panelMacroController.ResumeLayout(false);
            this.panelInject.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.panelAuto.ResumeLayout(false);
            this.panelCheckerOther.ResumeLayout(false);
            this.panelAutoAntiAFK.ResumeLayout(false);
            this.panelAutoAntiAFK.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtNormalAntiAfk)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.mouseXNum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.mouseYNum)).EndInit();
            this.panelAutoHPMP.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.trackBarHP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarMP)).EndInit();
            this.panelCheckers.ResumeLayout(false);
            this.panelCheckersFindPlayer.ResumeLayout(false);
            this.panelCheckersFindPlayer.PerformLayout();
            this.panelCheckerDeathStuff.ResumeLayout(false);
            this.panelCheckerDeathStuff.PerformLayout();
            this.panelCheckersDT.ResumeLayout(false);
            this.panelCheckersDT.PerformLayout();
            this.panelCheckerTimers.ResumeLayout(false);
            this.panelCheats.ResumeLayout(false);
            this.panelBlockHide.ResumeLayout(false);
            this.panelCheatsKnight.ResumeLayout(false);
            this.panelCheatsStats.ResumeLayout(false);
            this.panelAddStats.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.agiNBox)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.wisNBox)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.intNBox)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.hthNBox)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.strNBox)).EndInit();
            this.panelCurStats.ResumeLayout(false);
            this.panelCheatsHTML.ResumeLayout(false);
            this.panelCheatsHTML.PerformLayout();
            this.panelCheatTarget.ResumeLayout(false);
            this.panelCheatsMisc.ResumeLayout(false);
            this.panelCheatZoom.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.numZoom)).EndInit();
            this.panelTitle.ResumeLayout(false);
            this.panelTitle.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Panel panelMenu;
        private System.Windows.Forms.Panel panelStart;
        private System.Windows.Forms.Panel panelControl;
        private System.Windows.Forms.Button minBTN;
        private System.Windows.Forms.Button extBTN;
        private System.Windows.Forms.Button btnMacro;
        private System.Windows.Forms.Button btnCheats;
        private System.Windows.Forms.Button btnCheckers;
        private System.Windows.Forms.Button btnAuto;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnInject;
        private System.Windows.Forms.Button btnReset;
        private System.Windows.Forms.Panel panelMacro;
        private System.Windows.Forms.Panel panelLeft;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox comboEngineType;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtWinName;
        private System.Windows.Forms.Panel panelMacroBox;
        private System.Windows.Forms.RichTextBox richMacroBox;
        private System.Windows.Forms.Panel panelMacroPresets;
        private System.Windows.Forms.Panel panelMacroController;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnStartBot;
        private System.Windows.Forms.Button btnStopBot;
        private System.Windows.Forms.Panel panelMacroMisc;
        private System.Windows.Forms.Button btnMouseTracker;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Button btnMacroHelpCMD;
        private System.Windows.Forms.Label lblInfoMT;
        private System.Windows.Forms.Button btnNewMacro;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.Label lblMouseTrackerPosition;
        private System.Windows.Forms.Button btnPresetSave;
        private System.Windows.Forms.ComboBox comboPresets;
        private System.Windows.Forms.TextBox txtBoxPreset;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Panel panelInject;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.Label lblMana;
        private System.Windows.Forms.Label lblHealth;
        private System.Windows.Forms.Label lblName;
        private System.Windows.Forms.Label lblRunInfo;
        private System.Windows.Forms.Panel panelAuto;
        private System.Windows.Forms.Panel panelAutoAntiAFK;
        private System.Windows.Forms.Panel panelAutoHPMP;
        private System.Windows.Forms.CheckBox checkAutoHealth;
        private System.Windows.Forms.ComboBox manaBox;
        private System.Windows.Forms.ComboBox healthBox;
        private System.Windows.Forms.CheckBox checkAutoMana;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.NumericUpDown txtNormalAntiAfk;
        private System.Windows.Forms.NumericUpDown mouseXNum;
        private System.Windows.Forms.NumericUpDown mouseYNum;
        private System.Windows.Forms.CheckBox checkAntiAFK;
        private System.Windows.Forms.Label lblWorkingMacro;
        private System.Windows.Forms.Panel panelCheckers;
        private System.Windows.Forms.Panel panelCheckersDT;
        private System.Windows.Forms.CheckBox checkTarget;
        private System.Windows.Forms.TextBox txtTarget;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.CheckBox checkDeath;
        private System.Windows.Forms.Panel panelCheckerTimers;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Panel panelCheckerDeathStuff;
        private System.Windows.Forms.CheckBox checkAlertLowHP;
        private System.Windows.Forms.CheckBox checkCloseDead;
        private System.Windows.Forms.CheckBox checkStopDead;
        private System.Windows.Forms.Button btnCheckerTimer;
        private System.Windows.Forms.Label lblTimer;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label lblWorkingChecker;
        private System.Windows.Forms.Label lblWorkingAuto;
        private System.Windows.Forms.Timer aliveTimer;
        private System.Windows.Forms.Panel panelCheats;
        private System.Windows.Forms.Panel panelCheatTarget;
        private System.Windows.Forms.Panel panelCheatsMisc;
        private System.Windows.Forms.Panel panelCheatZoom;
        private System.Windows.Forms.Label lblCurZoom;
        private System.Windows.Forms.NumericUpDown numZoom;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.CheckBox checkLockTarget;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.CheckBox checkTargetOverlay;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.ComboBox comboTargetRange;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.CheckBox checkInifityRush;
        private System.Windows.Forms.CheckBox checkAttackSpeed;
        private System.Windows.Forms.Label lblWorkingCheat;
        private System.Windows.Forms.Panel panelTitle;
        private System.Windows.Forms.TextBox txtConsole;
        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Timer DataTimer;
        private System.Windows.Forms.Label lblExpire;
        private System.Windows.Forms.Panel panelCheatsHTML;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox txtOpenHTML;
        private System.Windows.Forms.CheckBox checkTargetInfoToSys;
        private System.Windows.Forms.Panel panelCheatsStats;
        private System.Windows.Forms.Panel panelAddStats;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.NumericUpDown agiNBox;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.NumericUpDown wisNBox;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.NumericUpDown intNBox;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.NumericUpDown hthNBox;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.NumericUpDown strNBox;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Button btnStatsAdd;
        private System.Windows.Forms.Panel panelCurStats;
        private System.Windows.Forms.Label lblCurAgi;
        private System.Windows.Forms.Label lblCurWis;
        private System.Windows.Forms.Label lblCurInt;
        private System.Windows.Forms.Label lblCurHP;
        private System.Windows.Forms.Label lblCurStr;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.TrackBar trackBarMP;
        private System.Windows.Forms.TrackBar trackBarHP;
        private System.Windows.Forms.Label lblAutoHP;
        private System.Windows.Forms.Label lblAutoMP;
        private System.Windows.Forms.Button btnOpenHTML;
        private System.Windows.Forms.Panel panelCheckersFindPlayer;
        private System.Windows.Forms.Label lblPlayerFound;
        private System.Windows.Forms.CheckBox checkPlayerFinder;
        private System.Windows.Forms.TextBox txtPlayerNames;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Button btnSaveQuickslot;
        private System.Windows.Forms.Panel panelCheckerOther;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.ComboBox quickSlotPageNum;
        private System.Windows.Forms.CheckBox checkQuickSlot;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.Label lblCurPoints;
        private System.ComponentModel.BackgroundWorker workerKeyboardMonitor;
        private System.Windows.Forms.Label lblUpdateLink;
        private System.Windows.Forms.CheckBox checkSSDead;
        private System.Windows.Forms.Panel panelCheatsKnight;
        private System.Windows.Forms.CheckBox checkPWW2nd;
        private System.Windows.Forms.CheckBox checkPWW3rd;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Timer PauseCheckTimer;
        private System.Windows.Forms.Panel panelMacroCtrl;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.RichTextBox richMacroCtrlBox;
        private System.Windows.Forms.Label lblMap;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.TextBox txtBoxMapY1;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.TextBox txtBoxMapX1;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.CheckBox checkXY;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.TextBox txtBoxMapY4;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.TextBox txtBoxMapX4;
        private System.Windows.Forms.Label label46;
        private System.Windows.Forms.TextBox txtBoxMapY3;
        private System.Windows.Forms.Label label47;
        private System.Windows.Forms.TextBox txtBoxMapX3;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.TextBox txtBoxMapY2;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.TextBox txtBoxMapX2;
        private System.Windows.Forms.CheckBox checkTabTarget;
        private System.Windows.Forms.Label label48;
        private System.Windows.Forms.CheckBox checkAnyPlayerFinder;
        private System.Windows.Forms.Label label49;
        private System.Windows.Forms.CheckBox hotkeys;
        private System.Windows.Forms.CheckBox checkTest;
        private System.Windows.Forms.Panel panelBlockHide;
        private System.Windows.Forms.CheckBox checkBlockHide;
        private System.Windows.Forms.CheckBox checkHideAll;
        private System.Windows.Forms.CheckBox checkBlockTeleport;
        private System.Windows.Forms.Label label50;
        private System.Windows.Forms.CheckBox safeModeCheckBox;
        private System.Windows.Forms.CheckBox NewReaderCheckBox;
    }
}

